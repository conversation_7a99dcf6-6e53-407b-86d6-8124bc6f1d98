const IS_PROD = process.env.APP_ENVIRONMENT === 'production';

const ENV = {
  development: {
    bundleIdentifier: 'com.zentanet.finsmarket.dev',
    package: 'com.zentanet.finsmarket.dev',
    name: '[Dev] FinSMarket',
    googleAuth: {
      iosClientId: '470903500690-3r59kah9nfk4vj5r9hbirpjod74pk1fi',
      iosUrlScheme: 'com.googleusercontent.apps.470903500690-3r59kah9nfk4vj5r9hbirpjod74pk1fi',
    },
  },
  production: {
    bundleIdentifier: 'com.zentanet.finsmarket',
    package: 'com.zentanet.finsmarket',
    name: 'FinSMarket',
    googleAuth: {
      iosClientId: '1081343787918-mkt0f2859hvh4g2jg9ev0168kvs18tq5',
      iosUrlScheme: 'com.googleusercontent.apps.1081343787918-mkt0f2859hvh4g2jg9ev0168kvs18tq5',
    },
  },
};

const selectedEnv = IS_PROD ? ENV.production : ENV.development;

export default {
  expo: {
    name: selectedEnv.name,
    slug: 'finsmarket',
    version: '1.5.0',
    orientation: 'portrait',
    icon: './assets/logo/icon.png',
    scheme: 'finsmarket',
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    owner: 'zentoai-team',
    ios: {
      bundleIdentifier: selectedEnv.bundleIdentifier,
      usesAppleSignIn: true,
      supportsTablet: false,
      icon: {
        light: './assets/logo/icon.png',
        dark: './assets/logo/icon-dark.png',
      },
      infoPlist: {
        CFBundleLocalizations: ['en', 'zh-TW', 'ja', 'ko', 'th', 'vi', 'ms', 'pt'],
        CFBundleDevelopmentRegion: 'en',
        CFBundleURLTypes: [
          {
            CFBundleURLSchemes: [selectedEnv.googleAuth.iosUrlScheme],
          },
        ],
      },
    },
    android: {
      package: selectedEnv.package,
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/favicon.png',
    },
    plugins: [
      'expo-router',
      'expo-secure-store',
      [
        'expo-splash-screen',
        {
          image: './assets/logo/icon-white.png',
          imageWidth: 140,
          resizeMode: 'contain',
          backgroundColor: '#FF9339',
          dark: {
            image: './assets/logo/icon-orange.png',
            backgroundColor: '#001221',
          },
        },
      ],
      [
        '@react-native-google-signin/google-signin',
        {
          iosUrlScheme: selectedEnv.googleAuth.iosUrlScheme,
        },
      ],
      'expo-localization',
      'expo-apple-authentication',
      [
        'expo-build-properties',
        {
          android: {
            compileSdkVersion: 35,
            targetSdkVersion: 35,
            buildToolsVersion: '35.0.0',
          },
          ios: {
            deploymentTarget: '15.1',
          },
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {
        origin: false,
      },
      eas: {
        projectId: '9d72364d-ade0-4af2-885a-0147d0758c82',
      },
    },
    runtimeVersion: {
      policy: 'appVersion',
    },
    updates: {
      url: 'https://u.expo.dev/9d72364d-ade0-4af2-885a-0147d0758c82',
    },
  },
};
