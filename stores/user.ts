import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';

export const PERMISSION = {
  free_report_2024: 'free_report_2024',
  portfolio_manager: 'portfolio_manager',
  paid_tier_1: 'paid_tier_1',
  admin: 'admin',
};

export type Permission = keyof typeof PERMISSION;

export type User = {
  id: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  email: string;
  verifiedAt?: string;
  picture?: string;
  permissions?: {
    permission: Permission;
  }[];
  hasPassword?: boolean;
};

export type UserPreferences = {
  theme?: 'light' | 'dark' | 'system';
  notifications?: boolean;
  // Add more preferences in the future
};

type UserState = {
  user: User | null;
  preferences: UserPreferences;
  isAuthenticated: boolean;
};

type UserAction = {
  setUser: (user: User | null) => void;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  hasPermissionTo: (permission: Permission) => boolean;
  logout: () => void;
};

// Concept from - https://stackoverflow.com/a/78460702
const storage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    const data = (await AsyncStorage.getItem(name)) || null;

    return data;
  },
  setItem: async (name: string, value: string): Promise<void> => {
    await AsyncStorage.setItem(name, value);
  },
  removeItem: async (name: string): Promise<void> => {
    await AsyncStorage.removeItem(name);
  },
};

export const useUserStore = create<UserState & UserAction>()(
  persist(
    (set, get) => ({
      user: null,
      preferences: {},
      isAuthenticated: false,
      setUser: user => set({ user, isAuthenticated: !!user }),
      updatePreferences: newPreferences =>
        set(state => ({
          preferences: { ...state.preferences, ...newPreferences },
        })),
      hasPermissionTo: (permission: Permission) => {
        if (!get().user) return false;
        return get().user?.permissions?.some(p => p.permission === permission) || false;
      },
      logout: () => set({ user: null, isAuthenticated: false }),
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => storage),
      // storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
