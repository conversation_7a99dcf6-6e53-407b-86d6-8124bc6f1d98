import { isMarketOpen } from '@/utils/stock-market';
import AsyncStorage from '@react-native-async-storage/async-storage';
import EventSource from 'react-native-sse';
import { z } from 'zod';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

const SHOW_LOGS = false;

export type CustomStockData = {
  symbol: string;
  lastPrice: number;
  timestamp: Date;
  lastClosePrice?: number;
  // volume: number;
  // askPrice: number;
  // bidPrice: number;
};

export const CustomStockSchema = z
  .object({
    symbol: z.string(),
    lastPrice: z.number(),
    timestamp: z.date().or(z.string()),
    lastClosePrice: z.number().optional(),
  })
  .array();

export type FollowedStock = {
  symbol: string;
  name: string;
};

type StockState = {
  stocks: { [ticker: string]: CustomStockData };
  followedStocks: FollowedStock[];
  viewedStocksToday: string[];
};

type StockActions = {
  updateStocks: (data: CustomStockData[]) => void;
  getStock: (ticker: string) => CustomStockData | undefined;
  getStocks: (tickers: string[]) => CustomStockData[];
  subscribeToStocks: (userId: string, tickers: string[]) => void;
  unsubscribeFromStocks: () => void;
  followStock: (stock: FollowedStock) => Promise<void>;
  unfollowStock: (symbol: string) => Promise<void>;
  isFollowed: (symbol: string) => boolean;
  updateLastClosePrices: (closePrices: { [symbol: string]: { price: number } }) => void;
  isStockViewedToday: (symbol: string) => boolean;
  syncViewedStocksFromServer: (stocks: string[]) => void;
};

type CustomEvents = 'stock_update' | 'heartbeat' | 'error';

const getEventSourceUrl = (userId: string, tickers: string[]) => {
  const tickerString = tickers.join(',');
  return `${process.env.EXPO_PUBLIC_API_URL}/stock-prices?userId=${userId}&tickers=${tickerString}`;
};

const DEFAULT_STOCKS: FollowedStock[] = [
  { symbol: 'META', name: 'Meta Platforms Inc' },
  { symbol: 'MSFT', name: 'Microsoft Corporation' },
  { symbol: 'AMZN', name: 'Amazon.com Inc' },
  { symbol: 'AAPL', name: 'Apple Inc' },
  { symbol: 'GOOGL', name: 'Alphabet Inc' },
  { symbol: 'NVDA', name: 'NVIDIA Corporation' },
  { symbol: 'TSLA', name: 'Tesla Inc' },
];

export const useStockStore = create<StockState & StockActions>()(
  persist(
    (set, get) => ({
      // Initial state
      stocks: {},
      followedStocks: DEFAULT_STOCKS,
      viewedStocksToday: [],

      // Stock data methods
      updateStocks: (data: CustomStockData[]) =>
        set(state => {
          const newStocks = { ...state.stocks };
          data.forEach(stock => {
            const existingStock = state.stocks[stock.symbol];
            newStocks[stock.symbol] = {
              ...stock,
              lastClosePrice: existingStock?.lastClosePrice ?? stock.lastClosePrice,
            };
          });

          // Format stock data for easy monitoring - one stock per line
          if (isMarketOpen() && SHOW_LOGS) {
            console.log('📈 Stock Store Updated:');
            Object.values(newStocks).forEach(stock => {
              const change = stock.lastClosePrice ? stock.lastPrice - stock.lastClosePrice : 0;
              const changePercent = stock.lastClosePrice
                ? (change / stock.lastClosePrice) * 100
                : 0;
              const timeStr =
                stock.timestamp instanceof Date ? stock.timestamp.toLocaleTimeString() : 'N/A';

              console.log(
                `${stock.symbol}: $${stock.lastPrice.toFixed(2)} (${change >= 0 ? '+' : ''}${change.toFixed(2)} | ${changePercent.toFixed(2)}%) - Close: $${stock.lastClosePrice?.toFixed(2) || 'N/A'} - ${timeStr}`
              );
            });
          }

          return { stocks: newStocks };
        }),

      getStock: (ticker: string) => get().stocks[ticker.toUpperCase()],

      getStocks: (tickers: string[]) =>
        Object.values(get().stocks).filter(stock => tickers.includes(stock.symbol)),

      updateLastClosePrices: (closePrices: { [symbol: string]: { price: number } }) =>
        set(state => {
          const newStocks = { ...state.stocks };
          Object.entries(closePrices).forEach(([symbol, data]) => {
            newStocks[symbol] = {
              symbol,
              lastPrice: newStocks[symbol]?.lastPrice ?? 0,
              timestamp: newStocks[symbol]?.timestamp ?? new Date(),
              lastClosePrice: data.price,
            };
          });
          return { stocks: newStocks };
        }),

      // Subscription methods
      subscribeToStocks: (userId: string, tickers: string[]) => {
        let retryCount = 0;
        const MAX_RETRIES = 5;
        const RETRY_DELAY = 5000;

        const url = getEventSourceUrl(userId, tickers);
        if (SHOW_LOGS) {
          console.log('Subscribing to stocks:', url);
        }

        const connect = () => {
          const eventSource = new EventSource<CustomEvents>(url);

          eventSource.addEventListener('stock_update', event => {
            if (!event.data) {
              console.warn('Received empty stock_update event data');
              return;
            }

            try {
              // Parse the JSON data once
              const rawData = JSON.parse(event.data);

              // Validate the data structure
              const parsedData = CustomStockSchema.safeParse(rawData);
              if (!parsedData.success) {
                console.error('Schema validation failed:', parsedData.error);
                return;
              }

              // Process and convert data to match CustomStockData type
              if (!Array.isArray(rawData) || rawData.length === 0) {
                console.warn('Invalid data array:', rawData);
                return;
              }

              const processedData: CustomStockData[] = rawData.map((stock: any) => ({
                symbol: stock.symbol,
                lastPrice: stock.lastPrice,
                timestamp:
                  typeof stock.timestamp === 'string' ? new Date(stock.timestamp) : stock.timestamp,
                lastClosePrice: stock.lastClosePrice,
              }));

              get().updateStocks(processedData);
            } catch (error) {
              console.error('Error parsing stock update data:', error);
            }
          });

          eventSource.addEventListener('heartbeat', () => {
            if (isMarketOpen() && SHOW_LOGS) {
              console.log('Received heartbeat');
            }
            retryCount = 0;
          });

          eventSource.addEventListener('error', error => {
            console.error('EventSource failed:', error);
            eventSource.close();

            if (!isMarketOpen()) {
              console.log('Market is closed, not retrying connection');
              return;
            }

            if (retryCount < MAX_RETRIES) {
              retryCount++;
              console.log(`Retrying connection (${retryCount}/${MAX_RETRIES}) in ${RETRY_DELAY}ms`);
              setTimeout(connect, RETRY_DELAY);
            } else {
              console.error('Max retries reached, giving up connection');
            }
          });

          set(state => ({ ...state, eventSource }));
        };

        connect();
      },

      unsubscribeFromStocks: () => {
        const { eventSource } = get() as any;
        if (eventSource) {
          eventSource.close();
        }
        set(state => ({ ...state, eventSource: null }));
      },

      // Followed stocks methods
      followStock: async (stock: FollowedStock) => {
        set(state => {
          if (state.followedStocks.some(s => s.symbol === stock.symbol)) {
            return state;
          }
          const newFollowedStocks = [...state.followedStocks, stock];
          return { followedStocks: newFollowedStocks };
        });
      },

      unfollowStock: async (symbol: string) => {
        set(state => {
          const newFollowedStocks = state.followedStocks.filter(s => s.symbol !== symbol);
          return { followedStocks: newFollowedStocks };
        });
      },

      isFollowed: (symbol: string) => {
        return get().followedStocks.some(stock => stock.symbol === symbol);
      },

      // Viewed stocks methods
      isStockViewedToday: (symbol: string) => get().viewedStocksToday.includes(symbol),

      syncViewedStocksFromServer: (stocks: string[]) => set({ viewedStocksToday: stocks }),
    }),
    {
      name: 'stock-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        followedStocks: state.followedStocks,
        viewedStocksToday: state.viewedStocksToday,
      }),
    }
  )
);
