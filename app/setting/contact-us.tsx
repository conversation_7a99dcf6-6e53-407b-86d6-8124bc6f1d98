import { Ionicons } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack, router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import React from 'react';

import { View, Text, TouchableOpacity, ScrollView, Platform } from 'react-native';

import ContactUsSvg from '@/components/illustrations/contact-us';

type ContactOption = {
  id: 'problem' | 'suggestion' | 'question';
  icon: keyof typeof FontAwesome.glyphMap;
  title: string;
  description: string;
  color: string;
};

// Platform-specific shadow styles
const getShadowStyle = () => {
  if (Platform.OS === 'ios') {
    return {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: 3,
    };
  } else {
    return {
      elevation: 5,
    };
  }
};

export default function ContactUs() {
  const { t } = useTranslation();

  const contactOptions: ContactOption[] = [
    {
      id: 'problem',
      icon: 'exclamation-circle',
      title: t('setting.contact.options.problem.title'),
      description: t('setting.contact.options.problem.description'),
      color: '#EF4444', // Red
    },
    {
      id: 'suggestion',
      icon: 'lightbulb-o',
      title: t('setting.contact.options.suggestion.title'),
      description: t('setting.contact.options.suggestion.description'),
      color: '#F97316', // Orange (matching app theme)
    },
    {
      id: 'question',
      icon: 'question-circle',
      title: t('setting.contact.options.question.title'),
      description: t('setting.contact.options.question.description'),
      color: '#3B82F6', // Blue
    },
  ];

  const handleOptionPress = (option: ContactOption) => {
    router.push({
      pathname: '/setting/contact-form',
      params: { type: option.id },
    } as any);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.contact.title'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="mail" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <SafeAreaView edges={[]} className="flex-1 bg-white">
        <ScrollView className="flex-1">
          <View className="p-4">
            <View className="mb-4">
              <Text className="text-2xl font-bold text-zinc-900">{t('setting.contact.help')}</Text>
              <Text className="text-base text-zinc-500">{t('setting.contact.description')}</Text>
            </View>

            <View className="h-64 w-full">
              <ContactUsSvg height={'100%'} width={'100%'} />
            </View>

            <View className="mt-6 space-y-4">
              {contactOptions.map(option => (
                <TouchableOpacity
                  key={option.id}
                  className="mb-6 rounded-xl bg-white p-4"
                  style={{ borderLeftWidth: 4, borderLeftColor: option.color, ...getShadowStyle() }}
                  activeOpacity={0.7}
                  onPress={() => handleOptionPress(option)}>
                  <View className="flex-row items-center">
                    <View
                      className="h-10 w-10 items-center justify-center rounded-full"
                      style={{ backgroundColor: `${option.color}20` }}>
                      <FontAwesome name={option.icon} size={20} color={option.color} />
                    </View>
                    <View className="ml-4 flex-1">
                      <Text className="text-base font-semibold text-zinc-900">{option.title}</Text>
                      <Text className="text-sm text-zinc-500">{option.description}</Text>
                    </View>
                    <FontAwesome name="chevron-right" size={14} color="#9CA3AF" />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
