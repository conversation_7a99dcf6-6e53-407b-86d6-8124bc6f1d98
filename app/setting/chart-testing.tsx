import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { WebView } from 'react-native-webview';

import React, { useState, useRef, useCallback } from 'react';

import {
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';

import { useHistoricalPrices } from '@/hooks/useFMP';

const ChartTesting: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const [selectedChart, setSelectedChart] = useState<'area' | 'line' | 'baseline' | 'bar' | 'histogram' | 'candlestick'>('area');
  const [selectedSymbol, setSelectedSymbol] = useState('AAPL');
  const [selectedTimeRange, setSelectedTimeRange] = useState('1M');
  const [isLoading, setIsLoading] = useState(true);
  const webViewRef = useRef<WebView>(null);
  
  const { data: historicalData, isLoading: isLoadingData } = useHistoricalPrices(selectedSymbol);
  
  const screenWidth = Dimensions.get('window').width;
  const chartWidth = screenWidth - 32;
  const chartHeight = 400;

  // Sample symbols for testing
  const testSymbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'];
  const timeRanges = ['5D', '1M', '3M', '6M', '1Y', 'ALL'];

  // Process data for charts
  const allChartData = historicalData?.historical ? historicalData.historical.map(item => ({
    time: new Date(item.date).getTime() / 1000,
    value: parseFloat(item.close.toString()),
    volume: item.volume,
    high: parseFloat(item.high.toString()),
    low: parseFloat(item.low.toString()),
    open: parseFloat(item.open.toString()),
  })).sort((a, b) => a.time - b.time) : [];

  // Filter data based on selected time range
  const getFilteredChartData = () => {
    if (allChartData.length === 0) return [];
    
    if (selectedTimeRange === 'ALL') {
      return allChartData;
    }

    const days = {
      '5D': 5,
      '1M': 30,
      '3M': 90,
      '6M': 180,
      '1Y': 365,
    }[selectedTimeRange];

    if (!days) return allChartData;

    const lastTime = allChartData[allChartData.length - 1].time;
    const startTime = lastTime - (days * 24 * 60 * 60);
    
    return allChartData.filter(item => item.time >= startTime);
  };

  const chartData = getFilteredChartData();

  const createAreaChartHtml = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            #chart_container {
              width: 100%;
              height: ${chartHeight}px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
          </style>
          <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
        </head>
        <body>
          <div class="title">Area Chart - ${selectedSymbol}</div>
          <div id="chart_container"></div>
          <script>
            try {
              var chartData = ${JSON.stringify(chartData)};
              
              var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
                width: ${chartWidth},
                height: ${chartHeight},
                layout: {
                  backgroundColor: '#ffffff',
                  textColor: '#333',
                },
                grid: {
                  vertLines: { color: 'rgba(197, 203, 206, 0.2)' },
                  horzLines: { color: 'rgba(197, 203, 206, 0.2)' },
                },
                timeScale: {
                  timeVisible: true,
                  secondsVisible: false,
                  borderColor: '#e0e0e0',
                },
                rightPriceScale: {
                  borderColor: '#e0e0e0',
                },
              });
              
              var areaSeries = chart.addAreaSeries({
                topColor: 'rgba(255, 107, 0, 0.56)',
                bottomColor: 'rgba(255, 107, 0, 0.04)',
                lineColor: 'rgba(255, 107, 0, 1)',
                lineWidth: 2,
              });
              
              if (chartData.length > 0) {
                areaSeries.setData(chartData);
                chart.timeScale().fitContent();
              }
              
              window.ReactNativeWebView.postMessage('loaded');
            } catch (error) {
              window.ReactNativeWebView.postMessage('error:' + error.message);
            }
          </script>
        </body>
      </html>
    `;
  };

  const createLineChartHtml = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            #chart_container {
              width: 100%;
              height: ${chartHeight}px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
          </style>
          <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
        </head>
        <body>
          <div class="title">Line Chart - ${selectedSymbol}</div>
          <div id="chart_container"></div>
          <script>
            try {
              var chartData = ${JSON.stringify(chartData)};
              
              var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
                width: ${chartWidth},
                height: ${chartHeight},
                layout: {
                  backgroundColor: '#ffffff',
                  textColor: '#333',
                },
                grid: {
                  vertLines: { color: 'rgba(197, 203, 206, 0.2)' },
                  horzLines: { color: 'rgba(197, 203, 206, 0.2)' },
                },
                timeScale: {
                  timeVisible: true,
                  secondsVisible: false,
                  borderColor: '#e0e0e0',
                },
                rightPriceScale: {
                  borderColor: '#e0e0e0',
                },
              });
              
              var lineSeries = chart.addLineSeries({
                color: '#FF6B00',
                lineWidth: 2,
              });
              
              if (chartData.length > 0) {
                lineSeries.setData(chartData);
                chart.timeScale().fitContent();
              }
              
              window.ReactNativeWebView.postMessage('loaded');
            } catch (error) {
              window.ReactNativeWebView.postMessage('error:' + error.message);
            }
          </script>
        </body>
      </html>
    `;
  };

  const createBaselineChartHtml = () => {
    // Calculate baseline as average of first few data points
    const baselineValue = chartData.length > 0 
      ? chartData.slice(0, Math.min(10, chartData.length)).reduce((sum, item) => sum + item.value, 0) / Math.min(10, chartData.length)
      : 0;

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            #chart_container {
              width: 100%;
              height: ${chartHeight}px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
          </style>
          <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
        </head>
        <body>
          <div class="title">Baseline Chart - ${selectedSymbol} (Baseline: $${baselineValue.toFixed(2)})</div>
          <div id="chart_container"></div>
          <script>
            try {
              var chartData = ${JSON.stringify(chartData)};
              var baselineValue = ${baselineValue};
              
              var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
                width: ${chartWidth},
                height: ${chartHeight},
                layout: {
                  backgroundColor: '#ffffff',
                  textColor: '#333',
                },
                grid: {
                  vertLines: { color: 'rgba(197, 203, 206, 0.2)' },
                  horzLines: { color: 'rgba(197, 203, 206, 0.2)' },
                },
                timeScale: {
                  timeVisible: true,
                  secondsVisible: false,
                  borderColor: '#e0e0e0',
                },
                rightPriceScale: {
                  borderColor: '#e0e0e0',
                },
              });
              
              var baselineSeries = chart.addBaselineSeries({
                baseValue: { type: 'price', price: baselineValue },
                topLineColor: '#26a69a',
                bottomLineColor: '#ef5350',
                topFillColor1: 'rgba(38, 166, 154, 0.28)',
                topFillColor2: 'rgba(38, 166, 154, 0.05)',
                bottomFillColor1: 'rgba(239, 83, 80, 0.28)',
                bottomFillColor2: 'rgba(239, 83, 80, 0.05)',
                lineWidth: 2,
              });
              
              if (chartData.length > 0) {
                baselineSeries.setData(chartData);
                chart.timeScale().fitContent();
              }
              
              window.ReactNativeWebView.postMessage('loaded');
            } catch (error) {
              window.ReactNativeWebView.postMessage('error:' + error.message);
            }
          </script>
        </body>
      </html>
    `;
  };

  const createBarChartHtml = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            #chart_container {
              width: 100%;
              height: ${chartHeight}px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
          </style>
          <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
        </head>
        <body>
          <div class="title">Bar Chart - ${selectedSymbol} (OHLC Bars)</div>
          <div id="chart_container"></div>
          <script>
            try {
              var chartData = ${JSON.stringify(chartData.map(item => ({
                time: item.time,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.value
              })))};
              
              var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
                width: ${chartWidth},
                height: ${chartHeight},
                layout: {
                  backgroundColor: '#ffffff',
                  textColor: '#333',
                },
                grid: {
                  vertLines: { color: 'rgba(197, 203, 206, 0.2)' },
                  horzLines: { color: 'rgba(197, 203, 206, 0.2)' },
                },
                timeScale: {
                  timeVisible: true,
                  secondsVisible: false,
                  borderColor: '#e0e0e0',
                },
                rightPriceScale: {
                  borderColor: '#e0e0e0',
                },
              });
              
              var barSeries = chart.addBarSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                openVisible: true,
                thinBars: true,
              });
              
              if (chartData.length > 0) {
                barSeries.setData(chartData);
                chart.timeScale().fitContent();
              }
              
              window.ReactNativeWebView.postMessage('loaded');
            } catch (error) {
              window.ReactNativeWebView.postMessage('error:' + error.message);
            }
          </script>
        </body>
      </html>
    `;
  };

  const createHistogramChartHtml = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            #chart_container {
              width: 100%;
              height: ${chartHeight}px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
          </style>
          <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
        </head>
        <body>
          <div class="title">Histogram Chart - ${selectedSymbol} (Volume)</div>
          <div id="chart_container"></div>
          <script>
            try {
              var chartData = ${JSON.stringify(chartData.map(item => ({
                time: item.time,
                value: item.volume,
                color: item.value >= item.open ? '#26a69a' : '#ef5350'
              })))};
              
              var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
                width: ${chartWidth},
                height: ${chartHeight},
                layout: {
                  backgroundColor: '#ffffff',
                  textColor: '#333',
                },
                grid: {
                  vertLines: { color: 'rgba(197, 203, 206, 0.2)' },
                  horzLines: { color: 'rgba(197, 203, 206, 0.2)' },
                },
                timeScale: {
                  timeVisible: true,
                  secondsVisible: false,
                  borderColor: '#e0e0e0',
                },
                rightPriceScale: {
                  borderColor: '#e0e0e0',
                },
              });
              
              var histogramSeries = chart.addHistogramSeries({
                color: '#26a69a',
                priceFormat: {
                  type: 'volume',
                },
              });
              
              if (chartData.length > 0) {
                histogramSeries.setData(chartData);
                chart.timeScale().fitContent();
              }
              
              window.ReactNativeWebView.postMessage('loaded');
            } catch (error) {
              window.ReactNativeWebView.postMessage('error:' + error.message);
            }
          </script>
        </body>
      </html>
    `;
  };

  const createCandlestickChartHtml = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <style>
            body {
              margin: 0;
              padding: 20px;
              background-color: white;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            #chart_container {
              width: 100%;
              height: ${chartHeight}px;
              border: 1px solid #ddd;
              border-radius: 8px;
            }
            .title {
              text-align: center;
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
          </style>
          <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
        </head>
        <body>
          <div class="title">Candlestick Chart - ${selectedSymbol}</div>
          <div id="chart_container"></div>
          <script>
            try {
              var chartData = ${JSON.stringify(chartData.map(item => ({
                time: item.time,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.value
              })))};
              
              var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
                width: ${chartWidth},
                height: ${chartHeight},
                layout: {
                  backgroundColor: '#ffffff',
                  textColor: '#333',
                },
                grid: {
                  vertLines: { color: 'rgba(197, 203, 206, 0.2)' },
                  horzLines: { color: 'rgba(197, 203, 206, 0.2)' },
                },
                timeScale: {
                  timeVisible: true,
                  secondsVisible: false,
                  borderColor: '#e0e0e0',
                },
                rightPriceScale: {
                  borderColor: '#e0e0e0',
                },
              });
              
              var candlestickSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
              });
              
              if (chartData.length > 0) {
                candlestickSeries.setData(chartData);
                chart.timeScale().fitContent();
              }
              
              window.ReactNativeWebView.postMessage('loaded');
            } catch (error) {
              window.ReactNativeWebView.postMessage('error:' + error.message);
            }
          </script>
        </body>
      </html>
    `;
  };

  const handleMessage = (event: any) => {
    const { data } = event.nativeEvent;
    
    if (data === 'loaded') {
      setIsLoading(false);
    } else if (data.startsWith('error:')) {
      setIsLoading(false);
      Alert.alert('Chart Error', data.substring(6));
    }
  };

  const handleSymbolChange = (symbol: string) => {
    setSelectedSymbol(symbol);
    setIsLoading(true);
  };

  const handleChartTypeChange = (type: 'area' | 'line' | 'baseline' | 'bar' | 'histogram' | 'candlestick') => {
    setSelectedChart(type);
    setIsLoading(true);
  };

  const handleTimeRangeChange = (range: string) => {
    setSelectedTimeRange(range);
    setIsLoading(true);
  };

  if (isLoadingData) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <Stack.Screen
          options={{
            title: 'Chart Testing',
            headerStyle: { backgroundColor: '#FF6B00' },
            headerTintColor: '#fff',
          }}
        />
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#FF6B00" />
          <Text className="mt-4 text-lg text-gray-600">Loading chart data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <Stack.Screen
        options={{
          title: 'Chart Testing',
          headerStyle: { backgroundColor: '#FF6B00' },
          headerTintColor: '#fff',
        }}
      />
      
      <ScrollView className="flex-1 p-4">
        {/* Controls */}
        <View className="mb-4 rounded-2xl bg-white p-4 shadow-sm">
          <Text className="mb-3 text-lg font-semibold text-gray-800">Test Controls</Text>
          
          {/* Symbol Selection */}
          <View className="mb-4">
            <Text className="mb-2 text-sm font-medium text-gray-600">Select Symbol:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View className="flex-row gap-2">
                {testSymbols.map(symbol => (
                  <TouchableOpacity
                    key={symbol}
                    onPress={() => handleSymbolChange(symbol)}
                    className={`rounded-lg px-3 py-2 ${
                      selectedSymbol === symbol ? 'bg-orange-500' : 'bg-gray-200'
                    }`}>
                    <Text className={selectedSymbol === symbol ? 'text-white' : 'text-gray-700'}>
                      {symbol}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Time Range Selection */}
          <View className="mb-4">
            <Text className="mb-2 text-sm font-medium text-gray-600">Time Range:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View className="flex-row gap-2">
                {timeRanges.map(range => (
                  <TouchableOpacity
                    key={range}
                    onPress={() => handleTimeRangeChange(range)}
                    className={`rounded-lg px-3 py-2 ${
                      selectedTimeRange === range ? 'bg-orange-500' : 'bg-gray-200'
                    }`}>
                    <Text className={selectedTimeRange === range ? 'text-white' : 'text-gray-700'}>
                      {range}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          {/* Chart Type Selection */}
          <View className="mb-4">
            <Text className="mb-2 text-sm font-medium text-gray-600">Chart Type:</Text>
            <View className="flex-row flex-wrap gap-2">
              <TouchableOpacity
                onPress={() => handleChartTypeChange('area')}
                className={`flex-1 min-w-[30%] rounded-lg p-3 ${
                  selectedChart === 'area' ? 'bg-orange-500' : 'bg-gray-200'
                }`}>
                <Text className={`text-center text-xs ${selectedChart === 'area' ? 'text-white' : 'text-gray-700'}`}>
                  Area
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleChartTypeChange('line')}
                className={`flex-1 min-w-[30%] rounded-lg p-3 ${
                  selectedChart === 'line' ? 'bg-orange-500' : 'bg-gray-200'
                }`}>
                <Text className={`text-center text-xs ${selectedChart === 'line' ? 'text-white' : 'text-gray-700'}`}>
                  Line
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleChartTypeChange('baseline')}
                className={`flex-1 min-w-[30%] rounded-lg p-3 ${
                  selectedChart === 'baseline' ? 'bg-orange-500' : 'bg-gray-200'
                }`}>
                <Text className={`text-center text-xs ${selectedChart === 'baseline' ? 'text-white' : 'text-gray-700'}`}>
                  Baseline
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleChartTypeChange('bar')}
                className={`flex-1 min-w-[30%] rounded-lg p-3 ${
                  selectedChart === 'bar' ? 'bg-orange-500' : 'bg-gray-200'
                }`}>
                <Text className={`text-center text-xs ${selectedChart === 'bar' ? 'text-white' : 'text-gray-700'}`}>
                  Bar
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleChartTypeChange('histogram')}
                className={`flex-1 min-w-[30%] rounded-lg p-3 ${
                  selectedChart === 'histogram' ? 'bg-orange-500' : 'bg-gray-200'
                }`}>
                <Text className={`text-center text-xs ${selectedChart === 'histogram' ? 'text-white' : 'text-gray-700'}`}>
                  Histogram
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleChartTypeChange('candlestick')}
                className={`flex-1 min-w-[30%] rounded-lg p-3 ${
                  selectedChart === 'candlestick' ? 'bg-orange-500' : 'bg-gray-200'
                }`}>
                <Text className={`text-center text-xs ${selectedChart === 'candlestick' ? 'text-white' : 'text-gray-700'}`}>
                  Candlestick
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Data Info */}
          <View className="rounded-lg bg-orange-50 p-3">
            <Text className="text-sm text-orange-600">
              Data Points: {chartData.length} | Symbol: {selectedSymbol} | Range: {selectedTimeRange}
            </Text>
            <Text className="text-sm text-orange-600">
              Type: {
                selectedChart === 'area' ? 'Area Chart (Price with Fill)' :
                selectedChart === 'line' ? 'Line Chart (Price Line)' :
                selectedChart === 'baseline' ? 'Baseline Chart (Above/Below Reference)' :
                selectedChart === 'bar' ? 'Bar Chart (OHLC Bars)' :
                selectedChart === 'histogram' ? 'Histogram Chart (Volume Bars)' :
                selectedChart === 'candlestick' ? 'Candlestick Chart (OHLC Candles)' : 'Unknown'
              }
            </Text>
          </View>
        </View>

        {/* Chart Container */}
        <View className="rounded-2xl bg-white shadow-sm">
          <View className="relative">
            {isLoading && (
              <View className="absolute inset-0 z-10 items-center justify-center bg-white">
                <ActivityIndicator size="large" color="#FF6B00" />
                <Text className="mt-2 text-gray-600">Loading chart...</Text>
              </View>
            )}
            
            {chartData.length > 0 ? (
              <WebView
                ref={webViewRef}
                source={{ html: selectedChart === 'area' ? createAreaChartHtml() : selectedChart === 'line' ? createLineChartHtml() : selectedChart === 'baseline' ? createBaselineChartHtml() : selectedChart === 'bar' ? createBarChartHtml() : selectedChart === 'histogram' ? createHistogramChartHtml() : createCandlestickChartHtml() }}
                style={{ 
                  width: chartWidth, 
                  height: chartHeight + 80, 
                  opacity: isLoading ? 0 : 1 
                }}
                scrollEnabled={false}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                originWhitelist={['*']}
                onMessage={handleMessage}
                onError={() => {
                  setIsLoading(false);
                  Alert.alert('Error', 'Failed to load chart');
                }}
                scalesPageToFit={false}
                bounces={false}
              />
            ) : (
              <View className="items-center justify-center p-8">
                <FontAwesome name="line-chart" size={48} color="#ccc" />
                <Text className="mt-4 text-gray-500">No chart data available</Text>
              </View>
            )}
          </View>
        </View>

        {/* Debug Info */}
        {chartData.length > 0 && (
          <View className="mt-4 rounded-2xl bg-white p-4 shadow-sm">
            <Text className="mb-2 text-lg font-semibold text-gray-800">Debug Info</Text>
            <Text className="text-sm text-gray-600">
              Total Data Points: {allChartData.length} | Filtered: {chartData.length}
            </Text>
            <Text className="text-sm text-gray-600">
              Time Range: {selectedTimeRange} ({chartData.length > 0 ? `${new Date(chartData[0]?.time * 1000).toLocaleDateString()} - ${new Date(chartData[chartData.length - 1]?.time * 1000).toLocaleDateString()}` : 'No data'})
            </Text>
            <Text className="text-sm text-gray-600">
              Price Range: ${chartData.reduce((min, item) => Math.min(min, item.value), Infinity).toFixed(2)} - 
              ${chartData.reduce((max, item) => Math.max(max, item.value), 0).toFixed(2)}
            </Text>
            {(selectedChart === 'histogram' || selectedChart === 'bar' || selectedChart === 'candlestick') && (
              <Text className="text-sm text-gray-600">
                Volume Range: {chartData.reduce((min, item) => Math.min(min, item.volume), Infinity).toLocaleString()} - 
                {chartData.reduce((max, item) => Math.max(max, item.volume), 0).toLocaleString()}
              </Text>
            )}
            {selectedChart === 'baseline' && chartData.length > 0 && (
              <Text className="text-sm text-gray-600">
                Baseline Value: ${(chartData.slice(0, Math.min(10, chartData.length)).reduce((sum, item) => sum + item.value, 0) / Math.min(10, chartData.length)).toFixed(2)} (avg of first 10 points)
              </Text>
            )}
            <Text className="text-sm text-gray-600">
              Chart Library: Lightweight Charts v3.8.0 | Series Type: {selectedChart}
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default ChartTesting; 