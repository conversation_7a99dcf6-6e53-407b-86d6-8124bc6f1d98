import { Ionicons } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import * as Application from 'expo-application';
import { router, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';

import React from 'react';

import { Image, ScrollView, Text, View, Linking, TouchableOpacity } from 'react-native';

export default function About() {
  const { t } = useTranslation();

  const handleEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  const handleWebsitePress = () => {
    Linking.openURL(`${process.env.EXPO_PUBLIC_WEBSITE_URL || 'https://www.finsmarket.ai'}/mobile`);
  };

  return (
    <>
      <Stack.Screen
        name="setting/about"
        options={{
          title: t('setting.helpAndSupport.aboutUs'),
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="information-circle" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <ScrollView className="flex-1 bg-white">
        {/* Header Section with Logo */}
        <View className="items-center bg-gray-50 px-6 pb-8 pt-12">
          <View className="rounded-xl shadow-lg shadow-gray-300/80">
            <Image source={require('@/assets/logo/icon.png')} className="h-28 w-28 rounded-xl" />
          </View>
          <Text className="mt-6 text-3xl font-bold text-gray-800">FinSMarket</Text>
          <View className="mt-2 rounded-full bg-gray-100 px-4 py-1">
            <Text className="text-gray-600">
              {t('setting.about.version', { version: Application.nativeApplicationVersion })}
            </Text>
          </View>
        </View>

        {/* Description Section */}
        <View className="px-6 py-8">
          <View className="rounded-2xl bg-theme/5 p-6">
            <Text className="mb-4 text-xl font-semibold text-gray-800">
              {t('setting.about.title')}
            </Text>
            <Text className="leading-7 text-gray-600">{t('setting.about.description')}</Text>
          </View>
        </View>

        {/* Contact Section */}
        <View className="px-6 pb-8">
          <Text className="mb-4 text-center text-lg font-semibold text-gray-800">
            {t('setting.about.contact.title')}
          </Text>
          <View className="gap-3">
            <TouchableOpacity
              onPress={handleEmailPress}
              className="flex-row items-center justify-center gap-3 rounded-xl bg-gray-50 p-4">
              <FontAwesome name="envelope-o" size={18} color="#4B5563" />
              <Text className="text-gray-600"><EMAIL></Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleWebsitePress}
              className="flex-row items-center justify-center gap-3 rounded-xl bg-gray-50 p-4">
              <FontAwesome name="globe" size={18} color="#4B5563" />
              <Text className="text-gray-600">www.finsmarket.ai</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Footer */}
        <View className="px-6 pb-8">
          <View className="border-t border-gray-200 pt-6">
            <Text className="text-center text-sm text-gray-500">
              {t('setting.about.copyright')}
            </Text>
          </View>
        </View>
      </ScrollView>
    </>
  );
}
