import { Ionicons } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import * as ImagePicker from 'expo-image-picker';
import * as MailComposer from 'expo-mail-composer';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Snackbar } from 'react-native-paper';

import React, { useState, useEffect } from 'react';

import { Linking } from 'react-native';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  Modal,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import { COLORS } from '@/constants/colors';

type ContactFormData = {
  email: string;
  type: 'problem' | 'suggestion' | 'question';
  topic: string;
  description: string;
  attachments: {
    name: string;
    uri: string;
    type: string;
    size?: number;
  }[];
};

// Create a simple type selector component
const TypeSelector = ({
  value,
  onChange,
  options,
}: {
  value: string;
  onChange: (value: 'problem' | 'suggestion' | 'question') => void;
  options: { id: string; title: string }[];
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const { t } = useTranslation();

  const selectedOption = options.find(option => option.id === value);

  return (
    <View>
      <Pressable
        onPress={() => setModalVisible(true)}
        className="flex-row items-center justify-between rounded-lg border border-zinc-200 bg-white p-3">
        <Text className="text-zinc-900">{selectedOption?.title || ''}</Text>
        <FontAwesome name="chevron-down" size={14} color="#9CA3AF" />
      </Pressable>

      <Modal
        transparent={true}
        visible={modalVisible}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <Pressable
          style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)' }}
          onPress={() => setModalVisible(false)}>
          <View className="m-4 mb-auto mt-auto overflow-hidden rounded-lg bg-white">
            <View className="border-b border-zinc-200 p-4">
              <Text className="text-center text-lg font-medium">
                {t('setting.contact.form.selectType')}
              </Text>
            </View>

            {options.map(option => (
              <Pressable
                key={option.id}
                className={`border-b border-zinc-100 p-4 ${value === option.id ? 'bg-zinc-50' : ''}`}
                onPress={() => {
                  onChange(option.id as 'problem' | 'suggestion' | 'question');
                  setModalVisible(false);
                }}>
                <View className="flex-row items-center justify-between">
                  <Text className="text-zinc-900">{option.title}</Text>
                  {value === option.id && <FontAwesome name="check" size={16} color="#FF9339" />}
                </View>
              </Pressable>
            ))}

            <Pressable
              className="border-t border-zinc-200 p-4"
              onPress={() => setModalVisible(false)}>
              <Text className="text-center font-medium text-theme">{t('common.cancel')}</Text>
            </Pressable>
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

// Helper function for mailto functionality
const sendMailtoEmail = async (subject: string, body: string) => {
  const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  const canOpen = await Linking.canOpenURL(mailtoUrl);

  if (canOpen) {
    await Linking.openURL(mailtoUrl);
    return true;
  }
  return false;
};

function ContactForm() {
  const { t } = useTranslation();
  const { type } = useLocalSearchParams<{ type: 'problem' | 'suggestion' | 'question' }>();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<{
    type: 'success' | 'warning';
    message: string;
  } | null>(null);
  const [formData, setFormData] = useState<ContactFormData>({
    email: user?.email || '',
    type: type || 'problem',
    topic: '',
    description: '',
    attachments: [],
  });
  const [errors, setErrors] = useState<{
    email?: string;
    topic?: string;
    description?: string;
  }>({});

  useEffect(() => {
    if (type) {
      setFormData(prev => ({ ...prev, type: type as 'problem' | 'suggestion' | 'question' }));
    }
  }, [type]);

  const contactOptions = [
    {
      id: 'problem',
      icon: 'exclamation-circle',
      title: t('setting.contact.options.problem.title'),
      description: t('setting.contact.options.problem.description'),
      color: '#EF4444', // Red
    },
    {
      id: 'suggestion',
      icon: 'lightbulb-o',
      title: t('setting.contact.options.suggestion.title'),
      description: t('setting.contact.options.suggestion.description'),
      color: '#F97316', // Orange (matching app theme)
    },
    {
      id: 'question',
      icon: 'question-circle',
      title: t('setting.contact.options.question.title'),
      description: t('setting.contact.options.question.description'),
      color: '#3B82F6', // Blue
    },
  ];

  const handleTypeChange = (selectedType: 'problem' | 'suggestion' | 'question') => {
    setFormData(prev => ({ ...prev, type: selectedType }));
  };

  const handlePickDocument = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        selectionLimit: 1,
        exif: false,
        quality: 0.8,
        mediaTypes: ImagePicker.MediaTypeOptions.All,
      });

      if (result.canceled) return;

      const file = result.assets[0];
      if (file) {
        // Check file size (limit to 5MB)
        if (file.fileSize && file.fileSize > 5 * 1024 * 1024) {
          setSnackbarMessage({
            type: 'warning',
            message: t('setting.contact.form.fileSizeError.message'),
          });
          setSnackbarVisible(true);
          return;
        }

        setFormData(prev => ({
          ...prev,
          attachments: [
            ...prev.attachments,
            {
              name: file.fileName || `file-${Date.now()}`,
              uri: file.uri,
              type: file.type || 'application/octet-stream',
              size: file.fileSize,
            },
          ],
        }));
      }
    } catch (error) {
      console.error('Error picking document:', error);
      setSnackbarMessage({
        type: 'warning',
        message: t('setting.contact.form.error.pickingFile'),
      });
      setSnackbarVisible(true);
    }
  };

  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        selectionLimit: 1,
        exif: false,
        quality: 0.8,
      });

      if (result.canceled) return;

      const asset = result.assets[0];
      if (asset) {
        // Check file size (limit to 5MB)
        if (asset.fileSize && asset.fileSize > 5 * 1024 * 1024) {
          setSnackbarMessage({
            type: 'warning',
            message: t('setting.contact.form.fileSizeError.message'),
          });
          setSnackbarVisible(true);
          return;
        }

        setFormData(prev => ({
          ...prev,
          attachments: [
            ...prev.attachments,
            {
              name: asset.fileName || `image-${Date.now()}.jpg`,
              uri: asset.uri,
              type: asset.type || 'image/jpeg',
              size: asset.fileSize,
            },
          ],
        }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      setSnackbarMessage({
        type: 'warning',
        message: t('setting.contact.form.error.pickingImage'),
      });
      setSnackbarVisible(true);
    }
  };

  const handleRemoveAttachment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index),
    }));
  };

  const validateForm = () => {
    const newErrors: {
      email?: string;
      topic?: string;
      description?: string;
    } = {};
    let isValid = true;

    if (!formData.email.trim()) {
      newErrors.email = t('setting.contact.form.validation.emailRequired');
      isValid = false;
    }

    // Topic validation with length constraints
    const topicLength = formData.topic.trim().length;
    if (!formData.topic.trim()) {
      newErrors.topic = t('setting.contact.form.validation.topicRequired');
      isValid = false;
    } else if (topicLength < 2) {
      newErrors.topic = t('setting.contact.form.validation.topicTooShort');
      isValid = false;
    } else if (topicLength > 40) {
      newErrors.topic = t('setting.contact.form.validation.topicTooLong');
      isValid = false;
    }

    // Description validation with length constraints
    const descriptionLength = formData.description.trim().length;
    if (!formData.description.trim()) {
      newErrors.description = t('setting.contact.form.validation.descriptionRequired');
      isValid = false;
    } else if (descriptionLength < 20) {
      newErrors.description = t('setting.contact.form.validation.descriptionTooShort');
      isValid = false;
    } else if (descriptionLength > 400) {
      newErrors.description = t('setting.contact.form.validation.descriptionTooLong');
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const subject = `[${t(`setting.contact.options.${formData.type}.title`)}] ${formData.topic}`;
      const body = `
${t('setting.contact.form.type')}: ${t(`setting.contact.options.${formData.type}.title`)}

${t('setting.contact.form.topic')}: ${formData.topic}

${t('setting.contact.form.description')}:
${formData.description}
      `;

      // Use mailto for Android and MailComposer for iOS
      if (Platform.OS === 'android') {
        const success = await sendMailtoEmail(subject, body);
        if (success) {
          setSnackbarMessage({
            type: 'success',
            message: t('setting.contact.form.success.pendingMessage'),
          });
          setSnackbarVisible(true);
        } else {
          throw new Error('Cannot open email app');
        }
      } else {
        // iOS - Use MailComposer
        const isAvailable = await MailComposer.isAvailableAsync();

        if (!isAvailable) {
          const success = await sendMailtoEmail(subject, body);
          if (!success) {
            throw new Error('Cannot open email app');
          }
          return;
        }

        const attachments = formData.attachments.map(attachment => attachment.uri);

        const result = await MailComposer.composeAsync({
          recipients: ['<EMAIL>'],
          subject,
          body,
          attachments,
          isHtml: false,
        });

        if (result.status === 'sent') {
          setFormData(prev => ({
            ...prev,
            topic: '',
            description: '',
            attachments: [],
          }));
          setSnackbarMessage({
            type: 'success',
            message: t('setting.contact.form.success.message'),
          });
          setSnackbarVisible(true);
        } else if (result.status === 'saved') {
          setSnackbarMessage({
            type: 'warning',
            message: t('setting.contact.form.success.draftMessage'),
          });
          setSnackbarVisible(true);
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSnackbarMessage({
        type: 'warning',
        message: t('setting.contact.form.error.submission'),
      });
      setSnackbarVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.contact.form.title'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="create" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1">
        <ScrollView className="flex-1 bg-white">
          <View className="p-4">
            {/* Form Fields */}
            <View className="space-y-4">
              {/* Email Field */}
              <View className="mb-2">
                <Text className="mb-1 text-sm font-medium text-zinc-700">
                  {t('setting.contact.form.email')} *
                </Text>
                <TextInput
                  className={`rounded-lg border ${
                    errors.email ? 'border-red-500' : 'border-zinc-200'
                  } bg-white p-3 text-zinc-900`}
                  value={formData.email}
                  onChangeText={text => {
                    setFormData(prev => ({ ...prev, email: text }));
                    if (errors.email) {
                      setErrors(prev => ({ ...prev, email: undefined }));
                    }
                  }}
                  placeholder={t('setting.contact.form.emailPlaceholder')}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  editable={false}
                />
                {errors.email && <Text className="mt-1 text-sm text-red-500">{errors.email}</Text>}
              </View>

              {/* Type Selection */}
              <View className="mb-2">
                <Text className="mb-1 text-sm font-medium text-zinc-700">
                  {t('setting.contact.form.type')} *
                </Text>
                <TypeSelector
                  value={formData.type}
                  onChange={handleTypeChange}
                  options={contactOptions.map(option => ({
                    id: option.id,
                    title: option.title,
                  }))}
                />
              </View>

              {/* Topic Field */}
              <View className="mb-2">
                <View className="mb-1 flex-row items-center justify-between">
                  <Text className="text-sm font-medium text-zinc-700">
                    {t('setting.contact.form.topic')} *
                  </Text>
                  <Text className="text-xs text-gray-500">{formData.topic.length}/40</Text>
                </View>
                <TextInput
                  className={`rounded-lg border ${
                    errors.topic ? 'border-red-500' : 'border-zinc-200'
                  } bg-white p-3 text-zinc-900`}
                  value={formData.topic}
                  onChangeText={text => {
                    setFormData(prev => ({ ...prev, topic: text }));
                    if (errors.topic) {
                      setErrors(prev => ({ ...prev, topic: undefined }));
                    }
                  }}
                  placeholder={t('setting.contact.form.topicPlaceholder')}
                  maxLength={40}
                />
                {errors.topic && <Text className="mt-1 text-sm text-red-500">{errors.topic}</Text>}
              </View>

              {/* Description Field */}
              <View className="mb-2">
                <View className="mb-1 flex-row items-center justify-between">
                  <Text className="text-sm font-medium text-zinc-700">
                    {t('setting.contact.form.description')} *
                  </Text>
                  <Text className="text-xs text-gray-500">{formData.description.length}/400</Text>
                </View>
                <TextInput
                  className={`min-h-[120px] rounded-lg border ${
                    errors.description ? 'border-red-500' : 'border-zinc-200'
                  } bg-white p-3 text-zinc-900`}
                  value={formData.description}
                  onChangeText={text => {
                    setFormData(prev => ({ ...prev, description: text }));
                    if (errors.description) {
                      setErrors(prev => ({ ...prev, description: undefined }));
                    }
                  }}
                  placeholder={t('setting.contact.form.descriptionPlaceholder')}
                  multiline
                  textAlignVertical="top"
                  maxLength={400}
                />
                {errors.description && (
                  <Text className="mt-1 text-sm text-red-500">{errors.description}</Text>
                )}
              </View>

              {/* Attachments - Only show on iOS */}
              {Platform.OS === 'ios' && (
                <View className="mb-2">
                  <Text className="mb-1 text-sm font-medium text-zinc-700">
                    {t('setting.contact.form.attachments')}
                  </Text>
                  <View className="flex-row flex-wrap gap-2">
                    <TouchableOpacity
                      onPress={handlePickImage}
                      className="flex-row items-center rounded-lg border border-dashed border-zinc-300 bg-zinc-50 px-3 py-2">
                      <FontAwesome name="image" size={16} color="#9CA3AF" />
                      <Text className="ml-2 text-sm text-zinc-600">
                        {t('setting.contact.form.addImage')}
                      </Text>
                    </TouchableOpacity>
                  </View>

                  {/* Attachment List */}
                  {formData.attachments.length > 0 && (
                    <View className="mt-2 space-y-2">
                      {formData.attachments.map((attachment, index) => (
                        <View
                          key={`${attachment.name}-${index}`}
                          className="flex-row items-center justify-between rounded-lg bg-zinc-50 p-2">
                          <View className="mr-2 flex-1 flex-row items-center">
                            <FontAwesome
                              name={
                                attachment.type.startsWith('image/') ? 'file-image-o' : 'file-o'
                              }
                              size={16}
                              color="#4B5563"
                            />
                            <Text
                              className="ml-2 flex-1 text-sm text-zinc-700"
                              numberOfLines={1}
                              ellipsizeMode="middle">
                              {attachment.name}
                            </Text>
                          </View>
                          <TouchableOpacity
                            onPress={() => handleRemoveAttachment(index)}
                            className="rounded-full bg-zinc-200 p-1">
                            <FontAwesome name="times" size={12} color="#4B5563" />
                          </TouchableOpacity>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}

              {/* Submit Button */}
              <TouchableOpacity
                onPress={handleSubmit}
                disabled={isSubmitting}
                className={`mt-4 rounded-lg p-4 ${isSubmitting ? 'bg-zinc-400' : 'bg-theme'}`}>
                {isSubmitting ? (
                  <ActivityIndicator color="#FFFFFF" />
                ) : (
                  <Text className="text-center font-semibold text-white">
                    {t('setting.contact.form.submit')}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Add Snackbar for notifications */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => {
          setSnackbarVisible(false);
          setTimeout(() => {
            setSnackbarMessage(null);
          }, 200);
        }}
        duration={3000}
        style={{
          backgroundColor: snackbarMessage?.type === 'success' ? COLORS.theme : '#ef4444',
          borderRadius: 8,
        }}>
        <View className="flex-row items-center">
          <FontAwesome
            name={snackbarMessage?.type === 'success' ? 'check-circle' : 'exclamation-circle'}
            size={16}
            color="#FFFFFF"
            style={{ marginRight: 8 }}
          />
          <Text className="text-white">{snackbarMessage?.message || ''}</Text>
        </View>
      </Snackbar>
    </>
  );
}

export default ContactForm;
