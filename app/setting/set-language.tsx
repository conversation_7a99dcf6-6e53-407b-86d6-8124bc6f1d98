import { LANGUAGE_OPTIONS, LANGUAGES } from '@/i18n';
import { useI18n } from '@/i18n/provider';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { router, Stack } from 'expo-router';
import i18next from 'i18next';
import { useTranslation } from 'react-i18next';

import React from 'react';

import { Pressable, Text, TouchableOpacity, View } from 'react-native';

export default function SetLanguage() {
  const { setLocale, locale } = useI18n();
  const { t } = useTranslation();

  const handleLanguageChange = async (selected: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const newLocale = selected as keyof typeof LANGUAGES;
    await i18next.changeLanguage(newLocale);
    await setLocale(newLocale);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.language.title'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="language" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <View className="flex-1 bg-white">
        {/* Description section */}
        <View className="px-6 py-4">
          <Text className="text-base text-gray-500">{t('setting.language.description')}</Text>
        </View>

        {/* Language options */}
        <View className="mt-2">
          {LANGUAGE_OPTIONS.map(option => (
            <Pressable
              key={option.value}
              onPress={() => handleLanguageChange(option.value)}
              className={`flex-row items-center justify-between px-6 py-4 active:bg-gray-50 ${locale === option.value ? 'bg-gray-50' : ''}`}>
              <View className="flex-1">
                <Text className="text-base font-medium text-gray-800">{option.label}</Text>
              </View>
              <View
                className={`h-6 w-6 items-center justify-center rounded-full border-4 ${
                  locale === option.value ? 'border-theme bg-white' : 'border-gray-200 bg-white'
                }`}>
                {locale === option.value && <View className="bg-whit h-2.5 w-2.5 rounded-full" />}
              </View>
            </Pressable>
          ))}
        </View>

        {/* Note section */}
        <View className="mt-6 px-6">
          <Text className="text-sm text-gray-400">{t('setting.language.note')}</Text>
        </View>
      </View>
    </>
  );
}
