import { Ionicons } from '@expo/vector-icons';
import { Stack, router } from 'expo-router';
import { useTranslation } from 'react-i18next';

import React, { useState } from 'react';

import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';

import { COLORS } from '@/constants/colors';

import { useTutorial } from '@/components/tutorial/TutorialContext';

// Define available tutorials with their IDs
const AVAILABLE_TUTORIALS = [
  { id: 'watchlist_tutorial', translationKey: 'watchlist' },
  { id: 'stock_tutorial', translationKey: 'stock' },
  { id: 'ai_chat_tutorial', translationKey: 'aiChat' },
];

// Platform-specific shadow styles
const getShadowStyle = () => {
  if (Platform.OS === 'ios') {
    return {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.2,
      shadowRadius: 3,
    };
  } else {
    return {
      elevation: 5,
    };
  }
};

export default function TutorialsSettings() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});
  const { resetTutorial, isTutorialCompleted } = useTutorial();

  const handleStartTutorial = async (tutorialId: string) => {
    try {
      setLoading(prev => ({ ...prev, [tutorialId]: true }));

      // First reset the tutorial to ensure a fresh start
      await resetTutorial(tutorialId);

      // Then navigate to the appropriate page
      if (tutorialId === 'watchlist_tutorial') {
        // Navigate to home page to start watchlist tutorial
        router.back();
        router.navigate('/(tabs)/home');
      } else if (tutorialId === 'stock_tutorial') {
        // Navigate to a stock page to start stock tutorial
        router.navigate('/stock/AAPL/overview');
      } else if (tutorialId === 'ai_chat_tutorial') {
        // Navigate to AI chat page to start AI chat tutorial
        router.navigate('/setting/ai-chat');
      }
    } catch (error) {
      console.error(t('setting.tutorials.error.startingFailed', { tutorialId }));
      Alert.alert(
        t('setting.tutorials.error.title', 'Error'),
        t('setting.tutorials.error.description', 'There was a problem starting the tutorial.'),
        [{ text: t('common.ok', 'OK') }]
      );
    } finally {
      setLoading(prev => ({ ...prev, [tutorialId]: false }));
    }
  };

  const getTutorialName = (tutorialId: string, translationKey: string): string => {
    return t(`setting.tutorials.names.${translationKey}`, translationKey);
  };

  const getTutorialStatus = (tutorialId: string): string => {
    return isTutorialCompleted(tutorialId)
      ? t('setting.tutorials.status.completed')
      : t('setting.tutorials.status.notCompleted');
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.tutorials.title'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="school" size={24} color={COLORS.theme} />
            </View>
          ),
        }}
      />
      <View className="flex-1 bg-white">
        <ScrollView className="flex-1">
          <View className="p-4">
            <Text className="mb-6 px-1 text-base text-gray-600">
              {t('setting.tutorials.description')}
            </Text>

            {AVAILABLE_TUTORIALS.map(tutorial => {
              const isCompleted = isTutorialCompleted(tutorial.id);
              return (
                <View
                  key={tutorial.id}
                  className="mb-5 rounded-xl border border-gray-100 bg-white p-5"
                  style={getShadowStyle()}>
                  <View className="flex-col">
                    <View className="mb-4 flex-row items-center justify-between">
                      <View className="flex-1">
                        <Text className="text-lg font-bold">
                          {getTutorialName(tutorial.id, tutorial.translationKey)}
                        </Text>
                        <View className="mt-1 flex-row items-center">
                          {isCompleted ? (
                            <View className="flex-row items-center">
                              <Ionicons
                                name="checkmark-circle"
                                size={16}
                                color="#10B981"
                                style={{ marginRight: 4 }}
                              />
                              <Text className="text-sm font-medium text-green-600">
                                {t('setting.tutorials.status.completed')}
                              </Text>
                            </View>
                          ) : (
                            <View className="flex-row items-center">
                              <Ionicons
                                name="time-outline"
                                size={16}
                                color={COLORS.theme}
                                style={{ marginRight: 4 }}
                              />
                              <Text className="text-sm" style={{ color: COLORS.theme }}>
                                {t('setting.tutorials.status.notCompleted')}
                              </Text>
                            </View>
                          )}
                        </View>
                      </View>

                      <View
                        style={{ backgroundColor: COLORS.themeLight }}
                        className="h-14 w-14 items-center justify-center rounded-full">
                        <Ionicons
                          name={
                            tutorial.id === 'watchlist_tutorial'
                              ? 'home'
                              : tutorial.id === 'stock_tutorial'
                                ? 'stats-chart'
                                : 'chatbubble-ellipses'
                          }
                          size={24}
                          color={COLORS.theme}
                        />
                      </View>
                    </View>

                    <Text className="mb-5 text-sm text-gray-600">
                      {t(`setting.tutorials.descriptions.${tutorial.translationKey}`)}
                    </Text>

                    {loading[tutorial.id] ? (
                      <View className="items-center py-2">
                        <ActivityIndicator size="small" color={COLORS.theme} />
                      </View>
                    ) : (
                      <View className="flex-row justify-end">
                        <TouchableOpacity
                          onPress={() => handleStartTutorial(tutorial.id)}
                          className="rounded-md px-6 py-2.5"
                          activeOpacity={0.8}
                          style={{ backgroundColor: COLORS.theme }}>
                          <View className="flex-row items-center">
                            <Ionicons
                              name="play"
                              size={16}
                              color="white"
                              style={{ marginRight: 6 }}
                            />
                            <Text className="font-medium text-white">
                              {isCompleted
                                ? t('setting.tutorials.restart')
                                : t('setting.tutorials.start')}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    </>
  );
}
