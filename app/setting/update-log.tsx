import { Ionicons } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack, router } from 'expo-router';
import { useTranslation } from 'react-i18next';

import React from 'react';

import { View, Text, ScrollView, TouchableOpacity } from 'react-native';

// Define the update log entry type
type UpdateLogEntry = {
  version: string;
  date: string;
  features?: string[];
  fixes?: string[];
  improvements?: string[];
};

export default function UpdateLog() {
  const { t: updateLogT } = useTranslation('update-log');
  const { t } = useTranslation();

  const updateLogs = (updateLogT('logs', { returnObjects: true }) || []) as UpdateLogEntry[];

  // Function to render a list of items with bullet points
  const renderList = (items: string[]) => {
    return items.map((item, index) => (
      <View key={index} className="mb-1.5 flex-row">
        <Text className="mr-2 text-theme">•</Text>
        <Text className="flex-1 text-gray-700">{item}</Text>
      </View>
    ));
  };

  // Function to render a section of the update log (features, improvements, fixes)
  const renderSection = (title: string, items?: string[]) => {
    if (!items || items.length === 0) return null;

    return (
      <View className="mb-3">
        <Text className="mb-1.5 font-medium text-gray-800">{title}</Text>
        <View className="ml-2">{renderList(items)}</View>
      </View>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.helpAndSupport.updateLog'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="time" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <ScrollView className="flex-1 bg-white">
        <View className="px-6 py-4">
          {/* Header section */}
          <View className="mb-6 items-center">
            <View className="mb-3 rounded-full bg-theme/10 p-3">
              <FontAwesome name="history" size={24} color="#F97316" />
            </View>
            <Text className="text-xl font-bold text-gray-800">{t('setting.updateLog.title')}</Text>
            <Text className="mt-1 text-center text-gray-500">
              {t('setting.updateLog.subtitle')}
            </Text>
          </View>

          {/* Update logs */}
          {updateLogs.map((log, index) => (
            <View
              key={log.version}
              className={`mb-4 rounded-xl bg-gray-50 p-5 ${
                index === 0 ? 'border-l-4 border-theme' : ''
              }`}>
              <View className="mb-3 flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-lg font-bold text-gray-800">v{log.version}</Text>
                  {index === 0 && (
                    <View className="ml-2 rounded bg-theme/20 px-2 py-0.5">
                      <Text className="text-xs font-medium text-theme">
                        {t('setting.updateLog.latest')}
                      </Text>
                    </View>
                  )}
                </View>
                <Text className="text-gray-500">{log.date}</Text>
              </View>

              {renderSection(t('setting.updateLog.newFeatures'), log.features)}
              {renderSection(t('setting.updateLog.improvements'), log.improvements)}
              {renderSection(t('setting.updateLog.fixes'), log.fixes)}
            </View>
          ))}
        </View>
      </ScrollView>
    </>
  );
}
