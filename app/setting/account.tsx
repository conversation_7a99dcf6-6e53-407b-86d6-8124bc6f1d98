import { Ionicons } from '@expo/vector-icons';
import { Href, Stack, router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';

import React from 'react';

import { View, Text, TouchableOpacity } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import { FontAwesomeIconName } from '@/components/common/SettingMenuItem';
import { SettingsSection, type SettingsSections } from '@/components/common/SettingsSection';

export default function AccountSettings() {
  const { t } = useTranslation();
  const { user, refetchProfile } = useAuth();

  const handleDismissDeleteAccount = async () => {
    await refetchProfile();
  };

  const handleDeleteAccount = async () => {
    try {
      await WebBrowser.openBrowserAsync(
        `${process.env.EXPO_PUBLIC_WEBSITE_URL}/auth/delete-account`
      );
    } catch (error) {
      console.error('Error opening delete account page:', error);
    } finally {
      await handleDismissDeleteAccount();
    }
  };

  const sections: SettingsSections[] = [
    {
      items: [
        ...(user && user.hasPassword
          ? [
              {
                href: '/auth/change-password' as Href,
                icon: 'lock' as FontAwesomeIconName,
                label: t('setting.account.changePassword'),
              },
            ]
          : []),
        {
          icon: 'trash' as FontAwesomeIconName,
          label: t('setting.account.deleteAccount'),
          onPress: handleDeleteAccount,
          variant: 'danger' as const,
        },
      ],
    },
  ];

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.account.title'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="person" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <View className="flex-1 bg-white">
        <View className="px-4 pt-4">
          {sections.map((section, index) => (
            <SettingsSection key={section.title || `section-${index}`} section={section} />
          ))}
        </View>
      </View>
    </>
  );
}
