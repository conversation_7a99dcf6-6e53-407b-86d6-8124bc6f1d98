// Import utilities
import { bottomSheetRegistry } from '@/lib/bottomSheetRegistry';
import { handleCopy, handleShare } from '@/utils/ai-chat/contentProcessing';
import { Message } from '@/utils/ai-chat/messageHelpers';
import { Ionicons } from '@expo/vector-icons';
import { BottomSheetModal, BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { BlurView } from 'expo-blur';
import { router, useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import React, { useState, useEffect, useRef, useMemo } from 'react';

import {
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  Dimensions,
  Animated,
} from 'react-native';

import { useChatFeedback } from '@/hooks/ai-chat/useChatFeedback';
// Import custom hooks
import { useChatMessages } from '@/hooks/ai-chat/useChatMessages';
import { useTryAgain } from '@/hooks/ai-chat/useTryAgain';
import { useChatHistory } from '@/hooks/useAIChat';

import FeedbackBottomSheet from '@/components/ai-chat/FeedbackBottomSheet';
import FollowUpQuestions from '@/components/ai-chat/FollowUpQuestions';
import MessageActions from '@/components/ai-chat/MessageActions';
import MessageContent from '@/components/ai-chat/MessageContent';
import ResponseNavigation from '@/components/ai-chat/ResponseNavigation';
import RightDrawer from '@/components/ai-chat/RightDrawer';
import TypingIndicator from '@/components/ai-chat/TypingIndicator';
import WelcomeMessage from '@/components/ai-chat/WelcomeMessage';
// Removed unused useAuth import

// Import extracted components
import Toast from '@/components/shared/Toast';
// Import tutorial context
import { useTutorial, TutorialStep } from '@/components/tutorial/TutorialContext';

const showLog = false;

const mockdata = {
  "type": "chart",
  "content": {
    "symbol": "AAPL",
    "chartType": "area",
    "historical": [
      {
        "date": "2025-08-07",
        "close": 220.03,
      },
      {
        "date": "2025-08-06",
        "close": 213.25,
      },
      {
        "date": "2025-08-05",
        "close": 202.92,
      },
      {
        "date": "2025-08-04",
        "close": 203.35,
      },
      {
        "date": "2025-08-01",
        "close": 202.38,
      },
    ]
  }
}

// Tutorial ID for AI chat
const AI_CHAT_TUTORIAL_ID = 'ai_chat_tutorial';

// Custom Header Component
interface CustomHeaderProps {
  title: string;
  onBack: () => void;
  onOpenDrawer: () => void;
  chatHistoryButtonRef: React.RefObject<React.ComponentRef<typeof TouchableOpacity>>;
  isRequestActive: boolean;
  streamingMessage: string;
}

const CustomHeader: React.FC<CustomHeaderProps> = ({
  title,
  onBack,
  onOpenDrawer,
  chatHistoryButtonRef,
  isRequestActive,
  streamingMessage
}) => {
  const insets = useSafeAreaInsets();

  return (
    <>
      <StatusBar style="dark" backgroundColor="white" />
      <View
        className="flex-row items-center justify-between px-4 py-3 bg-white border-b border-gray-100"
        style={{
          paddingTop: insets.top + 8, // Dynamic safe area + extra padding
          minHeight: 44 + insets.top + 16, // Ensure consistent height
        }}>
        {/* Left - Back Button */}
        <TouchableOpacity onPress={onBack} className="p-2">
          <Ionicons name="chevron-back" size={24} color="black" />
        </TouchableOpacity>

        {/* Center - Title */}
        <Text className="text-lg font-semibold text-gray-800">{title}</Text>

        {/* Right - Chat History Button */}
        <TouchableOpacity
          ref={chatHistoryButtonRef}
          onPress={onOpenDrawer}
          disabled={isRequestActive || !!streamingMessage}
          className="rounded-full p-2 pr-2"
          activeOpacity={0.7}
          style={{
            backgroundColor:
              isRequestActive || streamingMessage
                ? 'rgba(255, 147, 57, 0.3)'
                : 'rgba(255, 147, 57, 0.1)',
            opacity: isRequestActive || streamingMessage ? 0.5 : 1,
          }}>
          <Ionicons
            name="chatbubble-ellipses-outline"
            size={24}
            color={
              isRequestActive || streamingMessage ? '#999' : '#FF9339'
            }
          />
        </TouchableOpacity>
      </View>
    </>
  );
};

export default function AIChat() {
  const { t } = useTranslation();
  const { symbol } = useLocalSearchParams<{ symbol?: string }>();

  // State for UI
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [loadedChatId, setLoadedChatId] = useState<string | null>(null);
  const [inputAreaHeight, setInputAreaHeight] = useState(120); // Default fallback
  const [isSwitchingChat, setIsSwitchingChat] = useState(false);
  // Feedback bottom sheet state
  const [feedbackMessageIndex, setFeedbackMessageIndex] = useState<number | null>(null);
  const feedbackBottomSheetRef = useRef<BottomSheetModal>(null);

  // Register feedback bottom sheet with global registry
  useEffect(() => {
    bottomSheetRegistry.register(feedbackBottomSheetRef);
    return () => {
      bottomSheetRegistry.unregister(feedbackBottomSheetRef);
    };
  }, []);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current; // Start visible for welcome message

  // Refs
  const scrollViewRef = useRef<ScrollView>(null);
  const prevMessageCountRef = useRef(0);

  // Get screen dimensions
  const screenHeight = Dimensions.get('window').height;

  // Helper function for consistent scrolling behavior
  const scrollToBottom = (animated = true, delay = 50, onComplete?: () => void) => {
    setTimeout(() => {
      if (showLog) {
        console.log('Scrolling to bottom:', {
          animated,
          delay,
          hasMessages: chatMessages.messages.length > 0,
        });
      }
      scrollViewRef.current?.scrollToEnd({ animated });

      // Execute callback after scroll animation completes
      if (onComplete) {
        setTimeout(onComplete, animated ? 300 : 100); // Slightly longer for instant scrolls to ensure layout
      }
    }, delay);
  };

  // Fade in animation
  const fadeInContent = () => {
    if (showLog) {
      console.log('Starting fade in animation');
    }
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSwitchingChat(false);
      if (showLog) {
        console.log('Fade in completed');
      }
    });
  };

  // Fade out animation
  const fadeOutContent = () => {
    if (showLog) {
      console.log('Starting fade out animation');
    }
    setIsSwitchingChat(true);
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Calculate dynamic padding based on screen height and input area height
  const calculateBottomPadding = useMemo(() => {
    // Base padding is the input area height plus some buffer
    let padding = inputAreaHeight + 16; // Reduced base buffer

    // Ensure minimum padding for accessibility
    return Math.max(padding, 100);
  }, [screenHeight, inputAreaHeight]);

  // Tutorial refs
  const welcomeMessageRef = useRef(null);
  const inputAreaRef = useRef(null);
  const chatHistoryButtonRef = useRef(null);
  const chatHistorySectionRef = useRef(null);
  const dailyUsageSectionRef = useRef(null);

  // Get tutorial context
  const { startTutorial, isTutorialCompleted, activeTutorial, currentStep, setPreparingTutorial } =
    useTutorial();



  // Debug effect to log padding calculations (can be removed in production)
  // useEffect(() => {
  //   const padding = calculateBottomPadding;
  //   console.log('Dynamic padding calculation:', {
  //     screenHeight,
  //     inputAreaHeight,
  //     calculatedPadding: padding,
  //     platform: Platform.OS,
  //   });
  // }, [screenHeight, inputAreaHeight, calculateBottomPadding]);

  // Tutorial steps
  const tutorialSteps = useMemo(() => {
    return [
      {
        targetRef: welcomeMessageRef,
        title: t('tutorial.aiChat.welcome.title'),
        description: t('tutorial.aiChat.welcome.description'),
        position: 'bottom',
        padding: 15,
      },
      {
        targetRef: inputAreaRef,
        title: t('tutorial.aiChat.input.title'),
        description: t('tutorial.aiChat.input.description'),
        position: 'top',
        padding: 15,
        isBottomComponent: true,
      },
      {
        targetRef: chatHistoryButtonRef,
        title: t('tutorial.aiChat.chatHistory.title'),
        description: t('tutorial.aiChat.chatHistory.description'),
        position: 'bottom',
        padding: 15,
        isHeaderComponent: true,
      },
      {
        targetRef: chatHistorySectionRef,
        title: t('tutorial.aiChat.chatHistorySection.title'),
        description: t('tutorial.aiChat.chatHistorySection.description'),
        position: 'bottom',
        padding: 15,
        prepare: async () => {
          return new Promise<void>(resolve => {
            // Open the drawer first
            setIsDrawerOpen(true);

            // Wait for drawer animation and component to be rendered
            setTimeout(() => {
              resolve();
            }, 800);
          });
        },
      },
      {
        targetRef: dailyUsageSectionRef,
        title: t('tutorial.aiChat.dailyUsage.title'),
        description: t('tutorial.aiChat.dailyUsage.description'),
        position: 'top',
        padding: 15,
        isBottomComponent: true,
      },
    ] as TutorialStep[];
  }, [t]);

  // Get chat history
  const {
    data: historyData,
    isLoading: isHistoryLoading,
    refetch: refetchHistory,
  } = useChatHistory({ limit: 20 });

  // Custom hooks for chat functionality
  const chatMessages = useChatMessages(currentChatId, refetchHistory);
  const chatFeedback = useChatFeedback(
    chatMessages.messages,
    chatMessages.setMessages,
    currentChatId,
    chatMessages.refetchChatDetails
  );

  // State is accessed directly from chatMessages hook
  const tryAgain = useTryAgain(
    chatMessages.messages,
    chatMessages.setMessages,
    currentChatId,
    chatMessages.dailyUsageCount,
    chatMessages.dailyUsageLimit,
    undefined, // usageData not available in chatMessages
    chatMessages.chatDetailsData,
    chatMessages.setIsLoading,
    chatMessages.setIsTryingAgain,
    chatMessages.setRetryingMessageIndex,
    chatMessages.setStreamingMessage,
    chatMessages.setIsRequestActive,
    chatMessages.refetchUsage,
    chatMessages.refetchChatDetails
  );

  const chatHistory = historyData?.data?.chats || [];
  const historyPagination = historyData?.data?.pagination;

  // Start blocking immediately if tutorial should run
  useEffect(() => {
    if (!isTutorialCompleted(AI_CHAT_TUTORIAL_ID) && !activeTutorial) {
      if (showLog) {
        console.log('AI chat tutorial should run - starting immediate blocking');
      }
      setPreparingTutorial(true);
    }
  }, [isTutorialCompleted, activeTutorial, setPreparingTutorial]);

  // Initialize tutorial when page is ready (moved here after chatMessages hook)
  useEffect(() => {
    if (
      !isTutorialCompleted(AI_CHAT_TUTORIAL_ID) &&
      !activeTutorial &&
      // Only start tutorial when welcome message is visible (no chat messages)
      chatMessages.messages.length === 0
    ) {
      const timer = setTimeout(() => {
        if (showLog) {
          console.log('Starting AI chat tutorial');
        }
        startTutorial(AI_CHAT_TUTORIAL_ID, tutorialSteps);
        setPreparingTutorial(false);
      }, 1000); // Slightly longer delay to ensure page is fully loaded

      return () => {
        clearTimeout(timer);
        setPreparingTutorial(false);
      };
    }
  }, [
    isTutorialCompleted,
    activeTutorial,
    startTutorial,
    tutorialSteps,
    setPreparingTutorial,
    chatMessages.messages.length,
  ]);

  // For debugging purposes - log tutorial state changes
  useEffect(() => {
    if (activeTutorial === AI_CHAT_TUTORIAL_ID) {
      if (showLog) {
        console.log(`AI Chat Tutorial step: ${currentStep + 1} of ${tutorialSteps.length}`);
      }
    }
  }, [currentStep, activeTutorial, tutorialSteps.length]);

  // Toast helper
  const showToast = (message: string) => {
    setToastMessage(message);
    setToastVisible(true);
  };

  // Handle question selection
  const handleQuestionSelect = async (question: string) => {
    // Direct transition from welcome page - no fade effect needed
    const metadata = (await chatMessages.handleQuestionSelect(question, showToast)) as any;
    if (metadata?.chatId && !currentChatId) {
      if (showLog) {
        console.log('Setting new chat ID from handleQuestionSelect:', metadata.chatId);
      }
      setCurrentChatId(metadata.chatId);
      setLoadedChatId(metadata.chatId);
    }
  };

  // Handle message sending is now done directly through chatMessages.sendMessage

  // Handle copy and share
  const handleCopyMessage = (content: string) => handleCopy(content, showToast, t);
  const handleShareMessage = (content: string) => handleShare(content, showToast, t);

  // Handle feedback
  const handleLike = (index: number) => chatFeedback.handleLike(index, showToast);

  // Handle showing feedback form
  const handleShowFeedbackForm = (messageIndex: number) => {
    setFeedbackMessageIndex(messageIndex);

    if (feedbackBottomSheetRef.current) {
      feedbackBottomSheetRef.current.present();
    } else {
      console.error('❌ Bottom sheet ref is null!');
    }
  };

  // Handle dislike with feedback form
  const handleDislike = (index: number) => {
    return chatFeedback.handleDislike(index, showToast, handleShowFeedbackForm);
  };

  // Handle detailed feedback submission
  const handleFeedbackSubmit = (reasons: string[], comment: string) => {
    if (feedbackMessageIndex !== null) {
      chatFeedback.handleDetailedFeedback(
        feedbackMessageIndex,
        'dislike',
        reasons,
        comment,
        showToast
      );
    }
    feedbackBottomSheetRef.current?.dismiss();
    setFeedbackMessageIndex(null);
  };

  // Handle feedback form close
  const handleFeedbackClose = () => {
    feedbackBottomSheetRef.current?.dismiss();
    setFeedbackMessageIndex(null);
  };

  // Handle try again
  const handleTryAgain = (index: number) => tryAgain.handleTryAgain(index, showToast);

  // Handle response navigation
  const handleResponseNavigation = (index: number, direction: 'prev' | 'next') => {
    chatMessages.setMessages(prev => {
      const newMessages = [...prev];
      const message = newMessages[index];

      if (message.role === 'assistant' && message.responses && message.responses.length > 1) {
        const currentIndex = message.currentResponseIndex || 0;
        let newIndex = currentIndex;

        if (direction === 'prev' && currentIndex > 0) {
          newIndex = currentIndex - 1;
        } else if (direction === 'next' && currentIndex < message.responses.length - 1) {
          newIndex = currentIndex + 1;
        }

        if (newIndex !== currentIndex) {
          message.currentResponseIndex = newIndex;
          message.content = message.responses[newIndex];

          // Load the like/dislike state for this specific response
          if (message.responseLiked && message.responseDisliked) {
            message.liked = message.responseLiked[newIndex] || false;
            message.disliked = message.responseDisliked[newIndex] || false;
          } else {
            // Initialize if arrays don't exist
            message.responseLiked = new Array(message.responses.length).fill(false);
            message.responseDisliked = new Array(message.responses.length).fill(false);
            message.liked = false;
            message.disliked = false;
          }
        }
      }

      return newMessages;
    });
  };

  // Handle new chat
  const handleNewChat = () => {
    // Fade out existing content if there are messages to switch away from
    if (chatMessages.messages.length > 0) {
      fadeOutContent();
      // Wait for fade out to complete before clearing
      setTimeout(() => {
        chatMessages.setMessages([]);
        chatMessages.setInputMessage('');
        setCurrentChatId(null);
        setLoadedChatId(null);
        // Fade back in for welcome message
        fadeInContent();
      }, 250);
    } else {
      // Already on welcome screen, just clear state
      chatMessages.setMessages([]);
      chatMessages.setInputMessage('');
      setCurrentChatId(null);
      setLoadedChatId(null);
    }
  };

  // Handle chat loading
  const loadChatSession = (chatId: string) => {
    if (showLog) {
      console.log('Loading chat session:', { chatId, currentChatId, loadedChatId });
    }
    if (chatId === currentChatId) {
      if (showLog) {
        console.log('Same chat ID, skipping');
      }
      return;
    }

    // Always start fade out animation when switching chats (including from welcome screen)
    fadeOutContent();

    if (showLog) {
      console.log('Setting new chat ID and clearing loaded chat ID');
    }
    setCurrentChatId(chatId);
    // Clear the loaded chat ID so the effect knows to load new messages
    setLoadedChatId(null);
    // Don't clear messages immediately - let the effect handle it when data loads
    chatMessages.setInputMessage('');
  };

  // Handle chat deletion
  const deleteChatSession = (chatId: string) => {
    refetchHistory();
    if (currentChatId === chatId) {
      handleNewChat();
    }
  };

  // Effect to handle chat details data when it's loaded
  useEffect(() => {
    if (chatMessages.chatDetailsData?.data && currentChatId) {
      const chatWithMessages = chatMessages.chatDetailsData.data;

      // Check if we already have messages loaded for this specific chat (this is a feedback update)
      // vs loading a different chat (which requires full reload)
      const isUpdatingExistingMessages =
        chatMessages.messages.length > 0 && loadedChatId === currentChatId;

      if (showLog) {
        console.log('Chat details effect:', {
          currentChatId,
          loadedChatId,
          messagesLength: chatMessages.messages.length,
          isUpdatingExistingMessages,
        });
      }

      if (isUpdatingExistingMessages) {
        // Update only feedback states, preserve current response indices
        chatMessages.setMessages(prevMessages => {
          const updatedMessages = [...prevMessages];
          const apiMessages = chatWithMessages.messages;

          // Update feedback for each assistant message
          updatedMessages.forEach(message => {
            if (message.role === 'assistant' && message.aiMessageIds) {
              // Update feedback states for all response variations
              const responseLiked = message.aiMessageIds.map(aiMessageId => {
                const apiMessage = apiMessages.find(msg => msg.id === aiMessageId);
                return apiMessage?.feedback === 'LIKE' || false;
              });

              const responseDisliked = message.aiMessageIds.map(aiMessageId => {
                const apiMessage = apiMessages.find(msg => msg.id === aiMessageId);
                return apiMessage?.feedback === 'DISLIKE' || false;
              });

              // Update feedback arrays
              message.responseLiked = responseLiked;
              message.responseDisliked = responseDisliked;

              // Update current response feedback (preserve currentResponseIndex)
              const currentIndex = message.currentResponseIndex || 0;
              message.liked = responseLiked[currentIndex] || false;
              message.disliked = responseDisliked[currentIndex] || false;
            }
          });

          return updatedMessages;
        });

        if (showLog) {
          console.log('Updated feedback states for existing messages');
        }
        return;
      }

      // Initial load: Group messages by conversation flow
      const convertedMessages: Message[] = [];
      const apiMessages = chatWithMessages.messages;

      // Process messages in order, grouping AI responses by parentId
      for (let i = 0; i < apiMessages.length; i++) {
        const apiMessage = apiMessages[i];

        if (apiMessage.role === 'USER') {
          // Add user message
          convertedMessages.push({
            role: 'user' as const,
            content: apiMessage.content,
          });
        } else if (apiMessage.role === 'AI') {
          // Check if this AI message's parentId matches the previous user message
          // Find all AI messages with the same parentId
          const aiMessagesWithSameParent = apiMessages.filter(
            (msg: any) => msg.role === 'AI' && msg.parentId === apiMessage.parentId
          );

          // Only process if this is the first AI message we encounter for this parentId
          const isFirstAiMessageForParent = apiMessages
            .slice(0, i)
            .every(
              (prevMsg: any) => !(prevMsg.role === 'AI' && prevMsg.parentId === apiMessage.parentId)
            );

          if (isFirstAiMessageForParent) {
            // Sort AI messages by creation time to maintain order
            const sortedAiMessages = aiMessagesWithSameParent.sort(
              (a: any, b: any) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );

            // Create assistant message with all response variations
            const responses = sortedAiMessages.map((msg: any) => msg.content);
            const aiMessageIds = sortedAiMessages.map((msg: any) => msg.id);
            const responseLiked = sortedAiMessages.map((msg: any) => msg.feedback === 'LIKE');
            const responseDisliked = sortedAiMessages.map((msg: any) => msg.feedback === 'DISLIKE');

            // Use the latest (last) response as the current one
            const currentResponseIndex = responses.length - 1;
            const currentResponse = responses[currentResponseIndex];
            const currentAiMessageId = aiMessageIds[currentResponseIndex];

            convertedMessages.push({
              role: 'assistant' as const,
              content: currentResponse,
              liked: responseLiked[currentResponseIndex],
              disliked: responseDisliked[currentResponseIndex],
              responses: responses,
              currentResponseIndex: currentResponseIndex,
              responseLiked: responseLiked,
              responseDisliked: responseDisliked,
              showFollowUpQuestions: false, // Don't show for historical messages
              aiMessageId: currentAiMessageId,
              aiMessageIds: aiMessageIds,
            });
          }
        }
      }

      // Set the converted messages
      chatMessages.setMessages(convertedMessages);

      // Mark this chat as loaded
      setLoadedChatId(currentChatId);

      if (showLog) {
        console.log('Loaded chat with', convertedMessages.length, 'messages');
      }

      // Scroll to bottom after loading chat, then fade in
      scrollToBottom(false, 200, () => {
        // Only fade in if we're currently switching (faded out)
        if (isSwitchingChat) {
          fadeInContent();
        }
      });
    }
  }, [chatMessages.chatDetailsData, currentChatId]);

  // Effect for intelligent scrolling
  useEffect(() => {
    // Only scroll to bottom when:
    // 1. New messages are added (not when existing messages are modified)
    // 2. During streaming responses
    const currentMessageCount = chatMessages.messages.length;
    const hasNewMessages = currentMessageCount > prevMessageCountRef.current;
    const isStreaming = chatMessages.streamingMessage.length > 0;

    if (hasNewMessages || isStreaming) {
      // Add timeout to ensure dynamic padding has been applied
      scrollToBottom(false, 50); // Instant scroll for cleaner experience
    }

    // Update the previous message count
    prevMessageCountRef.current = currentMessageCount;
  }, [chatMessages.messages, chatMessages.streamingMessage]);

  // Additional effect to handle scrolling after padding changes
  useEffect(() => {
    // If we have messages and padding just changed, scroll to maintain bottom position
    if (chatMessages.messages.length > 0) {
      scrollToBottom(false, 50);
    }
  }, [calculateBottomPadding]);

  // Handle scrolling when input area height changes (affects padding)
  useEffect(() => {
    if (chatMessages.messages.length > 0 && inputAreaHeight > 0) {
      scrollToBottom(false, 100);
    }
  }, [inputAreaHeight]);

  // Reset message count tracking when chat changes
  useEffect(() => {
    prevMessageCountRef.current = 0;
  }, [currentChatId]);

  // Ensure fade is visible when showing welcome message
  useEffect(() => {
    if (chatMessages.messages.length === 0 && !isSwitchingChat && !chatMessages.isLoading) {
      // Make sure welcome message is visible when there are no messages and not loading
      fadeAnim.setValue(1);
    }
  }, [chatMessages.messages.length, isSwitchingChat, chatMessages.isLoading]);

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false, // Hide the default header
        }}
      />
      <BottomSheetModalProvider>
        <View className="flex-1 bg-white">
          {/* Custom Header - Always render to prevent layout shifts */}
          <CustomHeader
            title={t('aiChat.title')}
            onBack={() => router.back()}
            onOpenDrawer={() => setIsDrawerOpen(true)}
            chatHistoryButtonRef={chatHistoryButtonRef}
            isRequestActive={chatMessages.isRequestActive || !!chatMessages.streamingMessage}
            streamingMessage={chatMessages.streamingMessage}
          />

          <KeyboardAvoidingView
            behavior="padding"
            className="flex-1"
            keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
            enabled={true}
            style={{ flex: 1 }}>
            <View className="flex-1">
              {/* Toast notification */}
              <Toast
                visible={toastVisible}
                message={toastMessage}
                onHide={() => setToastVisible(false)}
              />

              <ScrollView
                ref={scrollViewRef}
                className="flex-1 px-4 pt-8"
                contentContainerStyle={{ paddingBottom: calculateBottomPadding }}
                keyboardShouldPersistTaps="handled"
                keyboardDismissMode="interactive"
                showsVerticalScrollIndicator={false}
                automaticallyAdjustKeyboardInsets={false}
                scrollEnabled={!isSwitchingChat}>
                <Animated.View style={{ opacity: fadeAnim }}>
                  {chatMessages.messages.length === 0 && !chatMessages.isChatDetailsLoading ? (
                    <View ref={welcomeMessageRef}>
                      <WelcomeMessage
                        onQuestionSelect={handleQuestionSelect}
                        symbol={symbol}
                        dailyUsageCount={chatMessages.dailyUsageCount}
                        dailyUsageLimit={chatMessages.dailyUsageLimit}
                      />
                    </View>
                  ) : (
                    <>
                      {/* Financial Disclaimer */}
                      <View className="-mt-8 mb-8 flex-row items-center justify-center">
                        <Text className="mr-2 text-xs text-gray-300">——————</Text>
                        <Text className="text-center text-xs text-gray-500">
                          {t('aiChat.disclaimer')}
                        </Text>
                        <Text className="ml-2 text-xs text-gray-300">——————</Text>
                      </View>
                      {chatMessages.messages.map((message, index) => (
                        <View key={index} className="mb-4">
                          <View
                            className="flex-row"
                            style={{
                              justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                            }}>
                            <View
                              style={{
                                maxWidth: message.role === 'user' ? '90%' : '100%',
                                backgroundColor: message.role === 'user' ? '#F7AC13' : '#F5F5F5',
                                borderRadius: 12,
                                borderTopRightRadius: message.role === 'user' ? 2 : 12,
                                padding: 12,
                              }}>
                              {/* Show TypingIndicator for empty assistant messages during try again */}
                              {message.role === 'assistant' &&
                                message.content === '' &&
                                chatMessages.isTryingAgain &&
                                chatMessages.retryingMessageIndex === index ? (
                                <TypingIndicator inline />
                              ) : (
                                <MessageContent
                                  content={message.content}
                                  isUser={message.role === 'user'}
                                />
                              )}
                            </View>
                          </View>

                          {/* Action buttons only for assistant messages */}
                          {message.role === 'assistant' && (
                            <View className="mt-1 pl-3 pr-6">
                              {/* Hide navigation only for the specific message being retried */}
                              {!(chatMessages.retryingMessageIndex === index) && (
                                <ResponseNavigation
                                  message={message}
                                  index={index}
                                  onNavigate={handleResponseNavigation}
                                />
                              )}
                              {/* Hide actions only for the specific message being retried */}
                              {!(chatMessages.retryingMessageIndex === index) && (
                                <MessageActions
                                  message={message}
                                  index={index}
                                  onLike={handleLike}
                                  onDislike={handleDislike}
                                  onCopy={handleCopyMessage}
                                  onShare={handleShareMessage}
                                  onTryAgain={handleTryAgain}
                                  isLatestAssistant={
                                    // Only show retry button for the absolute last message
                                    index === chatMessages.messages.length - 1
                                  }
                                  pendingFeedback={chatFeedback.pendingFeedback}
                                />
                              )}

                              {/* Follow-up questions for newly generated responses */}
                              {message.showFollowUpQuestions &&
                                !(chatMessages.retryingMessageIndex === index) && (
                                  <FollowUpQuestions
                                    onQuestionSelect={handleQuestionSelect}
                                    symbol={symbol}
                                    dailyUsageCount={chatMessages.dailyUsageCount}
                                    dailyUsageLimit={chatMessages.dailyUsageLimit}
                                  />
                                )}
                            </View>
                          )}
                        </View>
                      ))}
                      {/* Show typing indicator when loading but no streaming message yet, and not during try again */}
                      {chatMessages.isLoading &&
                        !chatMessages.streamingMessage &&
                        !chatMessages.isTryingAgain && <TypingIndicator />}
                      {/* Show streaming message bubble only for new messages, not during retry */}
                      {chatMessages.streamingMessage && !chatMessages.isTryingAgain && (
                        <View
                          className="mb-4 flex-row"
                          style={{
                            justifyContent: 'flex-start',
                          }}>
                          <View
                            style={{
                              maxWidth: '100%',
                              backgroundColor: '#F5F5F5',
                              borderRadius: 12,
                              padding: 12,
                            }}>
                            <MessageContent
                              content={chatMessages.streamingMessage}
                              isUser={false}
                            />
                          </View>
                        </View>
                      )}
                    </>
                  )}
                </Animated.View>
              </ScrollView>

              {/* Mask only the area directly under the input container */}
              <View
                style={{
                  position: 'absolute',
                  bottom: 0, // Just covers the input container area
                  left: 14,
                  right: 14,
                  height: 26, // Only cover the input container height
                  backgroundColor: '#ffffff',
                  pointerEvents: 'none',
                }}
              />

              {/* Floating Input Area with Blur */}
              <View
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  paddingHorizontal: 14,
                  paddingBottom: 24,
                  paddingTop: 16,
                }}
                ref={inputAreaRef}
                onLayout={event => {
                  const { height } = event.nativeEvent.layout;
                  setInputAreaHeight(height);
                }}>
                <BlurView
                  intensity={100}
                  tint="extraLight"
                  style={{
                    borderRadius: 24,
                    padding: 12,
                    borderWidth: 1,
                    borderColor: 'rgba(229,229,229,0.8)',
                    overflow: 'hidden',
                    backgroundColor:
                      Platform.OS === 'ios' ? 'rgba(255,255,255,0.1)' : 'rgba(255,255,255,1)',
                  }}>
                  <View className="mb-2">
                    <TextInput
                      className="w-full rounded-3xl px-2 py-2"
                      placeholder={t('aiChat.placeholder')}
                      placeholderTextColor="#666666"
                      value={chatMessages.inputMessage}
                      onChangeText={chatMessages.setInputMessage}
                      multiline
                      maxLength={500}
                      editable={!chatMessages.isLoading && !isSwitchingChat}
                      style={{
                        maxHeight: 140,
                        backgroundColor: 'transparent',
                      }}
                    />
                  </View>

                  {/* Function row */}
                  <View className="flex-row justify-end">
                    {chatMessages.streamingMessage ? (
                      <TouchableOpacity
                        onPress={() => {
                          // Cancel both regular requests and try again requests
                          chatMessages.cancelRequest();
                          tryAgain.cancelTryAgain();
                          if (currentChatId) {
                            chatMessages.refetchChatDetails();
                          }
                          showToast(t('aiChat.messages.requestCancelled'));
                        }}
                        className="rounded-full bg-red-500 px-4 py-2">
                        <Text className="text-white">{t('aiChat.actions.cancel') || 'Cancel'}</Text>
                      </TouchableOpacity>
                    ) : (
                      <TouchableOpacity
                        onPress={async () => {
                          // Direct transition from welcome page - no fade effect needed
                          const metadata = await chatMessages.sendMessage(showToast);
                          if (metadata?.chatId && !currentChatId) {
                            if (showLog) {
                              console.log('Setting new chat ID from sendMessage:', metadata.chatId);
                            }
                            setCurrentChatId(metadata.chatId);
                            setLoadedChatId(metadata.chatId);
                          }
                        }}
                        disabled={
                          chatMessages.isLoading ||
                          !chatMessages.inputMessage.trim() ||
                          isSwitchingChat
                        }
                        className={`rounded-full px-4 py-2 ${chatMessages.isLoading ||
                            !chatMessages.inputMessage.trim() ||
                            isSwitchingChat
                            ? 'bg-gray-300'
                            : 'bg-[#F7AC13]'
                          }`}>
                        {chatMessages.isLoading ? (
                          <ActivityIndicator color="white" size="small" />
                        ) : (
                          <Text className="text-white">{t('aiChat.send')}</Text>
                        )}
                      </TouchableOpacity>
                    )}
                  </View>
                </BlurView>
              </View>
            </View>
          </KeyboardAvoidingView>
        </View>

        {/* Feedback Bottom Sheet */}
        <FeedbackBottomSheet
          ref={feedbackBottomSheetRef}
          onClose={handleFeedbackClose}
          onSubmit={handleFeedbackSubmit}
        />
      </BottomSheetModalProvider>

      {/* Right Drawer */}
      <RightDrawer
        visible={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        onNewChat={handleNewChat}
        chatHistory={chatHistory}
        historyPagination={historyPagination}
        currentChatId={currentChatId}
        onLoadChat={loadChatSession}
        onDeleteChat={deleteChatSession}
        dailyUsageCount={chatMessages.dailyUsageCount}
        dailyUsageLimit={chatMessages.dailyUsageLimit}
        isUsageLoading={false}
        isHistoryLoading={isHistoryLoading}
        isRequestActive={
          chatMessages.isRequestActive || !!chatMessages.streamingMessage || isSwitchingChat
        }
        chatHistorySectionRef={chatHistorySectionRef}
        dailyUsageSectionRef={dailyUsageSectionRef}
      />
    </>
  );
}
