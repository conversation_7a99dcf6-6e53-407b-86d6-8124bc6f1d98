import { Ionicons } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack, router } from 'expo-router';
import { useTranslation } from 'react-i18next';

import React from 'react';

import { View, Text, ScrollView, TouchableOpacity } from 'react-native';

export default function Disclaimer() {
  const { t } = useTranslation();

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('setting.helpAndSupport.disclaimer'),
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} className="pr-2">
              <Ionicons name="chevron-back" size={24} color="black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View className="pr-2">
              <Ionicons name="warning" size={24} color="#F97316" />
            </View>
          ),
        }}
      />
      <ScrollView className="flex-1 bg-white">
        <View className="px-6 py-4">
          {/* Header section */}
          <View className="mb-6 items-center">
            <View className="mb-3 rounded-full bg-theme/10 p-3">
              <FontAwesome name="exclamation-circle" size={24} color="#F97316" />
            </View>
            <Text className="text-xl font-bold text-gray-800">{t('setting.disclaimer.title')}</Text>
          </View>

          {/* Disclaimer content */}
          <View className="mb-4 rounded-xl bg-gray-50 p-5">
            <Text className="mb-4 leading-6 text-gray-700">{t('setting.disclaimer.content')}</Text>
          </View>
        </View>
      </ScrollView>
    </>
  );
}
