import { handleOpenSurvey } from '@/utils/survey';
import { Ionicons } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { LinearGradient } from 'expo-linear-gradient';
import { Stack, router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';

import React, { useEffect, useState, useRef } from 'react';

import { View, Text, ScrollView, TouchableOpacity, ImageBackground, Animated } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

export default function Pricing() {
  const { t } = useTranslation();
  const { user, hasPermissionTo, getAccessToken, refetchProfile } = useAuth();
  const [token, setToken] = useState<string | null>(null);
  const [expandedPlans, setExpandedPlans] = useState<{ [key: string]: boolean }>({});

  // Animated values for each plan
  const animatedValues = useRef<{ [key: string]: Animated.Value }>({});

  useEffect(() => {
    const fetchToken = async () => {
      if (user) {
        const token = await getAccessToken();
        setToken(token);
      }
    };

    fetchToken();
  }, [user, getAccessToken]);

  // Initialize animated values for all plans
  const initializeAnimatedValue = (planId: string) => {
    if (!animatedValues.current[planId]) {
      animatedValues.current[planId] = new Animated.Value(0);
    }
    return animatedValues.current[planId];
  };

  const togglePlanBenefits = (planId: string) => {
    const isCurrentlyExpanded = expandedPlans[planId];
    const animatedValue = initializeAnimatedValue(planId);

    setExpandedPlans(prev => ({
      ...prev,
      [planId]: !isCurrentlyExpanded,
    }));

    // Animate the expand/collapse with spring animation for smoother feel
    Animated.spring(animatedValue, {
      toValue: isCurrentlyExpanded ? 0 : 1,
      useNativeDriver: false, // We need to animate height, so we can't use native driver
      tension: 10,
      friction: 8,
      overshootClamping: true,
    }).start();
  };

  const plans = [
    {
      id: 'basic',
      name: t('setting.pricing.plans.basic.name'),
      price: t('setting.pricing.plans.basic.price'),
      description: t('setting.pricing.plans.basic.description'),
      badge: 'BASIC',
      features: [
        t('setting.pricing.plans.basic.features.fundamental'),
        t('setting.pricing.plans.basic.features.aiAnalysis4'),
        t('setting.pricing.plans.basic.features.tuesdayReport'),
        t('setting.pricing.plans.basic.features.thursdayReport'),
        t('setting.pricing.plans.basic.features.saturdayReport4'),
        t('setting.pricing.plans.basic.features.aiChatLimit'),
      ],
    },
    {
      id: 'advanced',
      name: t('setting.pricing.plans.advanced.name'),
      price: t('setting.pricing.plans.advanced.price'),
      description: t('setting.pricing.plans.advanced.description'),
      badge: 'ADVANCED',
      features: [
        t('setting.pricing.plans.advanced.features.allBasic'),
        t('setting.pricing.plans.advanced.features.aiAnalysis10'),
        t('setting.pricing.plans.advanced.features.saturdayReport6'),
        t('setting.pricing.plans.advanced.features.manageReports'),
        t('setting.pricing.plans.advanced.features.aiChatLimit'),
      ],
    },
    {
      id: 'premium',
      name: t('setting.pricing.plans.premium.name'),
      price: t('setting.pricing.plans.premium.price'),
      description: t('setting.pricing.plans.premium.description'),
      badge: 'PREMIUM',
      features: [
        t('setting.pricing.plans.premium.features.allAdvanced'),
        t('setting.pricing.plans.premium.features.aiAnalysisUnlimited'),
        t('setting.pricing.plans.premium.features.saturdayReport20'),
        t('setting.pricing.plans.premium.features.portfolioTool'),
        t('setting.pricing.plans.premium.features.targetPrice'),
        t('setting.pricing.plans.premium.features.priorityAccess'),
      ],
    },
  ];

  const getCurrentUserPlan = () => {
    if (!user) return 'basic';
    if (hasPermissionTo('free_report_2024')) return 'advanced';
    return 'basic';
  };

  const handleDismissSurvey = async () => {
    await refetchProfile();
  };

  const handlePlanPress = (planId: string) => {
    if (planId === 'premium') {
      // Show coming soon message - could add a modal or toast here
      return;
    }

    if (planId === 'advanced' && !hasPermissionTo('free_report_2024')) {
      // Navigate to survey
      if (user && token) {
        handleOpenSurvey(user, token, handleDismissSurvey);
      }
    }
  };

  const currentPlan = getCurrentUserPlan();

  const handleOpenUrl = async (url: string) => {
    try {
      await WebBrowser.openBrowserAsync(url);
    } catch (error) {
      console.error('Error opening URL:', error);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <ImageBackground
        source={require('@/assets/images/pricing-background.webp')}
        className="flex-1"
        resizeMode="cover">
        {/* Dark overlay for better text readability */}
        <View className="absolute inset-0 bg-black/20" />

        {/* Custom transparent header - Absolute positioned */}
        <View className="absolute left-0 right-0 top-14 z-20">
          <View className="flex-row items-center justify-between px-4">
            <TouchableOpacity
              onPress={() => router.back()}
              className="flex-row items-center"
              activeOpacity={0.7}>
              <View className="rounded-full bg-black/30 p-2 backdrop-blur-sm">
                <Ionicons name="chevron-back" size={24} color="white" />
              </View>
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView
          className="flex-1"
          contentInsetAdjustmentBehavior="never"
          showsVerticalScrollIndicator={false}>
          {/* Header Section - with top padding to account for header */}
          <View className="px-6 py-8 pt-52">
            <View className="items-center">
              <Text className="mb-2 text-center text-3xl font-bold text-white">
                {t('setting.pricing.header.title')}
              </Text>
              <Text className="text-center text-base text-gray-300">
                {t('setting.pricing.header.subtitle')}
              </Text>
            </View>
          </View>

          {/* Plans Section */}
          <View className="px-4 pb-8">
            {plans.map((plan, index) => {
              const isSelected = currentPlan === plan.id;
              const isHighlighted = plan.id === 'premium';
              // const isClickable = plan.id === 'advanced' && !hasPermissionTo('free_report_2024');
              const animatedValue = initializeAnimatedValue(plan.id);

              return (
                <View key={plan.id} className="mb-4">
                  {/* Card with gradient border */}
                  <LinearGradient
                    colors={
                      isHighlighted ? ['#FF8C00', '#FFFF00'] : ['#FFFFFF', '#3B3B3D', '#FFFFFF']
                    }
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1.5 }}
                    style={{ borderRadius: 16 }}>
                    <View
                      style={{
                        borderRadius: 14,
                        margin: 2,
                        backgroundColor: '#3B3B3D',
                        ...(isHighlighted ? {} : { opacity: 0.9 }),
                      }}
                      className="p-6">
                      {/* Plan Badge */}
                      <View className="mb-4 flex-row items-center justify-between">
                        <View className="py-1q rounded-lg bg-white px-3">
                          <Text className="text-gray text-sm font-bold">{plan.badge}</Text>
                        </View>
                      </View>

                      {/* Price */}
                      <View className="mb-4">
                        <Text className="text-3xl font-bold text-white">{plan.price}</Text>
                        <Text className="mt-1 text-sm text-gray-300">{plan.description}</Text>
                      </View>

                      {/* See Benefits Toggle */}
                      <TouchableOpacity
                        onPress={() => togglePlanBenefits(plan.id)}
                        disabled={plan.id === 'premium'}
                        className="flex-row items-center justify-between pb-2 disabled:opacity-50"
                        activeOpacity={0.7}>
                        <Text className="font-bold text-white">
                          {t('setting.pricing.seeBenefits')}
                        </Text>
                        <Animated.View
                          style={{
                            transform: [
                              {
                                rotate: animatedValue.interpolate({
                                  inputRange: [0, 1],
                                  outputRange: ['0deg', '180deg'],
                                }),
                              },
                            ],
                          }}>
                          <Ionicons name="chevron-down" size={20} color="white" />
                        </Animated.View>
                      </TouchableOpacity>

                      {/* Features List - Smooth Expand/Collapse Animation */}
                      <Animated.View
                        className="mb-2"
                        style={{
                          maxHeight: animatedValue.interpolate({
                            inputRange: [0, 1],
                            outputRange: [0, 400], // Optimized height for better animation
                          }),
                          opacity: animatedValue.interpolate({
                            inputRange: [0, 0.2, 1],
                            outputRange: [0, 0.3, 1],
                          }),
                          transform: [
                            {
                              scaleY: animatedValue.interpolate({
                                inputRange: [0, 1],
                                outputRange: [0.95, 1],
                              }),
                            },
                          ],
                          overflow: 'hidden',
                        }}>
                        <View className="space-y-3 py-2">
                          {plan.features.map((feature, featureIndex) => (
                            <View key={featureIndex} className="flex-row items-start">
                              <View className="mr-3 mt-2">
                                <View className="h-2 w-2 rounded-full bg-orange-400" />
                              </View>
                              <Text className="flex-1 text-sm leading-6 text-gray-200">
                                {feature}
                              </Text>
                            </View>
                          ))}
                        </View>
                      </Animated.View>

                      {/* Action Button */}
                      <View>
                        {plan.id === 'premium' ? (
                          <View className="rounded-xl bg-gray-800/80 py-4">
                            <Text className="text-center font-semibold text-gray-300">
                              {t('setting.pricing.comingSoon')}
                            </Text>
                          </View>
                        ) : isSelected ? (
                          <View className="rounded-xl bg-gray-800/80 py-4">
                            <Text className="text-center font-semibold text-gray-300">
                              {t('setting.pricing.currentPlan')}
                            </Text>
                          </View>
                        ) : plan.id === 'advanced' ? (
                          <TouchableOpacity
                            onPress={() => handlePlanPress(plan.id)}
                            activeOpacity={0.8}>
                            <LinearGradient
                              colors={['#FF8C00', '#FFC107']}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 1 }}
                              style={{ borderRadius: 12 }}>
                              <Text className="py-4 text-center font-semibold text-white">
                                {t('setting.pricing.completeSurvey')}
                              </Text>
                            </LinearGradient>
                          </TouchableOpacity>
                        ) : (
                          <View className="rounded-xl bg-gray-800/80 py-4">
                            <Text className="text-center font-semibold text-gray-300">
                              {t('setting.pricing.free')}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </LinearGradient>
                </View>
              );
            })}
          </View>

          {/* Trusted by millions section */}
          {/* <View className="px-6 pb-8">
            <Text className="mb-6 text-center text-2xl font-bold text-white">
              Trusted by millions
            </Text> */}

          {/* Review card */}
          {/* <View className="rounded-2xl bg-gray-900/95 p-6 backdrop-blur-sm">
              <View className="mb-3 flex-row">
                {[...Array(5)].map((_, i) => (
                  <FontAwesome key={i} name="star" size={16} color="#FFD700" />
                ))}
              </View>
              <Text className="leading-6 text-gray-300">
                Awesome App for technical analysis of any trading or investing asset out there in
                the market. Please keep updating and adding new features.
              </Text>
            </View>
          </View> */}

          {/* Footer */}
          <View className="px-6 pb-12">
            <Text className="text-center text-sm font-bold text-gray-300">
              {t('setting.pricing.footer.note')}
            </Text>
            <View className="mt-2 flex-row items-center justify-center">
              <TouchableOpacity
                onPress={() =>
                  handleOpenUrl(
                    `${process.env.EXPO_PUBLIC_WEBSITE_URL || 'https://www.finsmarket.ai'}/mobile/terms-of-use`
                  )
                }
                activeOpacity={0.7}>
                <Text className="text-center text-sm font-bold text-gray-300">
                  {t('setting.helpAndSupport.userAgreement')}
                </Text>
              </TouchableOpacity>
              <Text className="mx-2 text-sm font-bold text-gray-300">|</Text>
              <TouchableOpacity
                onPress={() =>
                  handleOpenUrl(
                    `${process.env.EXPO_PUBLIC_WEBSITE_URL || 'https://www.finsmarket.ai'}/mobile/privacy-policy`
                  )
                }
                activeOpacity={0.7}>
                <Text className="text-center text-sm font-bold text-gray-300">
                  {t('setting.helpAndSupport.privacyPolicy')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </ImageBackground>
    </>
  );
}
