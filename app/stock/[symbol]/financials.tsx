import { useI18n } from '@/i18n/provider';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { FlatList, Text, View, SafeAreaView } from 'react-native';

import { useFinancialStatement, useStockTrend, useViewStockUsage } from '@/hooks/useStock';

import ExceedLimitView from '@/components/common/ExceedLimitView';
import { InfoCard } from '@/components/common/InfoCard';
import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import StockLoading from '@/components/stock/stock-loading';

export default function Financials() {
  const { t } = useTranslation();
  const { locale } = useI18n();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data: financialData, isLoading: isLoadingFinancial } = useFinancialStatement(
    symbol,
    locale
  );
  const { data: trendData, isLoading: isLoadingTrend } = useStockTrend(symbol, locale);
  const { data: stockUsage } = useViewStockUsage();

  // Check if limit reached
  const hasReachedLimit = (stockUsage?.data?.usage ?? 0) >= (stockUsage?.data?.limit ?? 0);
  const isStockViewed = stockUsage?.data?.stocks?.includes(symbol) ?? false;

  if (hasReachedLimit && !isStockViewed) {
    return <ExceedLimitView />;
  }

  const isLoading = isLoadingFinancial || isLoadingTrend;

  if (isLoading) {
    return <StockLoading />;
  }

  if (!financialData?.response || !trendData?.response) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  const { response: financial } = financialData;
  const { response: trend } = trendData;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <FlatList
        ListHeaderComponent={
          <PageDescription description={t('aiAnalysis.financials.description')} />
        }
        data={[{ financial, trend }]}
        contentContainerClassName="px-4 py-4"
        renderItem={({ item }) => (
          <View>
            <Stack.Screen
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: '#FF6B00' },
                headerTintColor: '#fff',
              }}
            />

            {/* Financial Overview Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.financials.overview.label')}
                title={t('aiAnalysis.financials.overview.title')}
                icon="file-text-o"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.financials.overview.summary')}
                  content={item?.financial?.summary}
                />
                <InfoCard
                  title={t('aiAnalysis.financials.overview.opinion')}
                  content={item?.financial?.opinion}
                  className="mt-3"
                />
              </View>
            </View>

            {/* Financial Analysis Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.financials.analysis.label')}
                title={t('aiAnalysis.financials.analysis.title')}
                icon="list"
              />
              <View className="p-5">
                {item?.financial?.analysis?.length > 0 ? (
                  item?.financial?.analysis.map((analysis, index) => (
                    <InfoCard key={index} content={analysis} className={index > 0 ? 'mt-3' : ''} />
                  ))
                ) : (
                  <InfoCard content={t('common.noData')} />
                )}
              </View>
            </View>

            {/* Trend Analysis Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.financials.trend.label')}
                title={t('aiAnalysis.financials.trend.title')}
                icon="line-chart"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.financials.trend.shortTerm')}
                  content={item?.trend?.trend?.short_term}
                />
                <InfoCard
                  title={t('aiAnalysis.financials.trend.longTerm')}
                  content={item?.trend?.trend?.long_term}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.financials.trend.summary')}
                  content={item?.trend?.summary}
                  className="mt-3"
                />
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
      />
    </SafeAreaView>
  );
}
