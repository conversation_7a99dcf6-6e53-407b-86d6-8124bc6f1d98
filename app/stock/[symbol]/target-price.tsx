import { useI18n } from '@/i18n/provider';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { FlatList, Text, View, SafeAreaView } from 'react-native';

import { useTargetGrading, useViewStockUsage } from '@/hooks/useStock';

import ExceedLimitView from '@/components/common/ExceedLimitView';
import { InfoCard } from '@/components/common/InfoCard';
import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import StockLoading from '@/components/stock/stock-loading';

export default function Target() {
  const { t } = useTranslation();
  const { locale } = useI18n();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data, isLoading } = useTargetGrading(symbol, locale);
  const { data: stockUsage } = useViewStockUsage();

  // Check if limit reached
  const hasReachedLimit = (stockUsage?.data?.usage ?? 0) >= (stockUsage?.data?.limit ?? 0);
  const isStockViewed = stockUsage?.data?.stocks?.includes(symbol) ?? false;

  if (hasReachedLimit && !isStockViewed) {
    return <ExceedLimitView />;
  }

  if (isLoading) {
    return <StockLoading />;
  }

  if (!data?.response) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  const { response } = data;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <FlatList
        ListHeaderComponent={
          <PageDescription description={t('aiAnalysis.targetPrice.description')} />
        }
        data={[response]}
        contentContainerClassName="px-4 py-4"
        renderItem={({ item }) => (
          <View>
            <Stack.Screen
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: '#FF6B00' },
                headerTintColor: '#fff',
              }}
            />

            {/* Overview Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.targetPrice.overview.label')}
                title={t('aiAnalysis.targetPrice.overview.title')}
                icon="file-text-o"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.targetPrice.overview.summary')}
                  content={item?.summary}
                />
              </View>
            </View>

            {/* Rating Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.targetPrice.prediction.label')}
                title={t('aiAnalysis.targetPrice.prediction.title')}
                icon="star-o"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.targetPrice.prediction.shortTerm')}
                  content={item?.opinion?.target_price.toString()}
                />
                <InfoCard
                  title={t('aiAnalysis.targetPrice.prediction.longTerm')}
                  content={item?.opinion?.target_price_reason}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.targetPrice.prediction.summary')}
                  content={item?.opinion?.grading}
                  className="mt-3"
                />
              </View>
            </View>

            {/* Analysis Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.targetPrice.analysis.label')}
                title={t('aiAnalysis.targetPrice.analysis.title')}
                icon="bars"
              />
              <View className="p-5">
                {item?.analysis?.length > 0 ? (
                  item?.analysis.map((analysis, index) => (
                    <InfoCard key={index} content={analysis} className={index > 0 ? 'mt-3' : ''} />
                  ))
                ) : (
                  <InfoCard content={t('common.noData')} />
                )}
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
      />
    </SafeAreaView>
  );
}
