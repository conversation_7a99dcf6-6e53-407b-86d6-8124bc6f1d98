import { useStockStore } from '@/stores/stock';
import { User } from '@/stores/user';
import { formatNumber } from '@/utils/format';
import { isMarketOpen } from '@/utils/stock-market';
import { Ionicons } from '@expo/vector-icons';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import {
  MaterialTopTabBarProps,
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from '@react-navigation/material-top-tabs';
import { ParamListBase, TabNavigationState, useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import { Redirect } from 'expo-router';
import { Stack, useLocalSearchParams, withLayoutContext } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { useState, useRef, useEffect, useMemo } from 'react';

import { Animated, View, TouchableOpacity, ScrollView, Text } from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useStockQuote } from '@/hooks/useFMP';
import { useLatestClosingPrices } from '@/hooks/useStock';

import Skeleton from '@/components/common/Skeleteton';
import StockUsageButton from '@/components/common/StockUsageButton';
import { useTutorial } from '@/components/tutorial/TutorialContext';
import { TutorialStep } from '@/components/tutorial/TutorialContext';

const { Navigator } = createMaterialTopTabNavigator();

// Tutorial ID
const STOCK_TUTORIAL_ID = 'stock_tutorial';

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

const HeaderLeft = ({ symbol, user }: { symbol: string; user: User }) => {
  const navigation = useNavigation();
  const { subscribeToStocks, unsubscribeFromStocks, getStock, updateLastClosePrices } =
    useStockStore();
  const stock = getStock(symbol);
  const marketOpen = isMarketOpen();

  const { data: latestClosingPrices } = useLatestClosingPrices([symbol]);

  // Check if we should use store data
  const useStoreData = marketOpen && stock && stock.lastPrice > 0;

  // Only fetch FMP quote when market is closed or store data is not available
  const { data: fmpQuote, isLoading: fmpLoading } = useStockQuote(symbol, {
    enabled: !useStoreData,
  });

  let currentPrice: number | undefined;
  let priceChange: number;
  let priceChangePercentage: number;
  let isLoading: boolean;

  if (useStoreData) {
    // Use store data when market is open
    currentPrice = stock.lastPrice;
    priceChange = stock.lastClosePrice ? stock.lastPrice - stock.lastClosePrice : 0;
    priceChangePercentage = stock.lastClosePrice
      ? ((stock.lastPrice - stock.lastClosePrice) / stock.lastClosePrice) * 100
      : 0;
    isLoading = false;
  } else {
    // Use FMP data when market is closed or store data is not available
    currentPrice = fmpQuote?.price;
    priceChange = fmpQuote?.change || 0;
    priceChangePercentage = fmpQuote?.changesPercentage || 0;
    isLoading = fmpLoading || !fmpQuote;
  }

  useEffect(() => {
    // Subscribe to stock data when component mounts
    if (symbol && user?.id) {
      subscribeToStocks(user.id, [symbol]);
    }

    // Cleanup subscription when component unmounts
    return () => {
      if (symbol) {
        unsubscribeFromStocks();
      }
    };
  }, [symbol, user?.id]); // Only depend on symbol and user.id

  // Add effect to update closing prices
  useEffect(() => {
    if (latestClosingPrices?.success && latestClosingPrices.data) {
      const formattedPrices = latestClosingPrices.data.reduce(
        (acc: { [symbol: string]: { price: number } }, item: { symbol: string; price: number }) => {
          acc[item.symbol] = { price: item.price };
          return acc;
        },
        {} as { [symbol: string]: { price: number } }
      );
      updateLastClosePrices(formattedPrices);
    }
  }, [latestClosingPrices, updateLastClosePrices]);

  return (
    <View className="flex-row items-center">
      <TouchableOpacity onPress={() => navigation.goBack()} className="pr-4">
        <Ionicons name="chevron-back" size={24} color="black" />
      </TouchableOpacity>
      <View className="flex-col">
        <Text className="text-xl font-semibold uppercase">{symbol}</Text>
        {isLoading || !currentPrice ? (
          <Skeleton className="h-4 w-24" />
        ) : (
          <View className="flex-row items-center">
            <Text className="text-base font-medium">${formatNumber(currentPrice)}</Text>
            <View className="flex-row items-center gap-1">
              <Text
                className={`ml-2 text-sm ${
                  priceChangePercentage >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                {priceChangePercentage >= 0 ? '↑' : '↓'}{' '}
                {Math.abs(priceChangePercentage).toFixed(2)}%
              </Text>
              <Text className={`text-sm ${priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ({priceChange >= 0 ? '+' : '-'}${Math.abs(priceChange).toFixed(2)})
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const HeaderRight = ({ symbol }: { symbol: string }) => {
  const { followStock, unfollowStock, isFollowed } = useStockStore();

  const handleToggleFollow = async () => {
    try {
      if (isFollowed(symbol)) {
        await unfollowStock(symbol);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        await followStock({ symbol, name: symbol });
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  return (
    <TouchableOpacity onPress={handleToggleFollow} className="px-4">
      <Ionicons
        name={isFollowed(symbol) ? 'heart' : 'heart-outline'}
        size={24}
        color={isFollowed(symbol) ? 'red' : 'black'}
      />
    </TouchableOpacity>
  );
};

export default function StockLayout() {
  const { user, isAuthenticated } = useAuth();
  const { t } = useTranslation();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const [componentsReady, setComponentsReady] = useState(false);

  // Refs for tutorial
  const stockInfoRef = useRef(null);
  const favoriteButtonRef = useRef(null);
  const tabsRef = useRef(null);
  const tabContentRef = useRef(null);

  // Get tutorial context
  const { startTutorial, isTutorialCompleted, activeTutorial, currentStep, setPreparingTutorial } =
    useTutorial();

  // Start blocking immediately if tutorial should run
  useEffect(() => {
    if (!isTutorialCompleted(STOCK_TUTORIAL_ID) && !activeTutorial) {
      console.log('Stock tutorial should run - starting immediate blocking');
      setPreparingTutorial(true);
    }
  }, [isTutorialCompleted, activeTutorial, setPreparingTutorial]);

  // Set components as ready after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      setComponentsReady(true);
    }, 1000); // Delay to ensure components are fully rendered

    return () => clearTimeout(timer);
  }, []);

  // Tutorial steps
  const tutorialSteps = useMemo(() => {
    return [
      {
        targetRef: stockInfoRef,
        title: t('tutorial.stock.stockInfo.title'),
        description: t('tutorial.stock.stockInfo.description'),
        position: 'bottom',
        padding: 15,
        isHeaderComponent: true,
      },
      {
        targetRef: favoriteButtonRef,
        title: t('tutorial.stock.favorite.title'),
        description: t('tutorial.stock.favorite.description'),
        position: 'bottom',
        padding: 15,
        isHeaderComponent: true,
      },
      {
        targetRef: tabsRef,
        title: t('tutorial.stock.tabs.title'),
        description: t('tutorial.stock.tabs.description'),
        position: 'bottom',
        padding: 15,
      },
      {
        targetRef: tabContentRef,
        title: t('tutorial.stock.content.title'),
        description: t('tutorial.stock.content.description'),
        position: 'top',
        padding: 15,
      },
    ] as TutorialStep[];
  }, [t]);

  // Initialize tutorial when components are ready
  useEffect(() => {
    if (componentsReady && !isTutorialCompleted(STOCK_TUTORIAL_ID) && !activeTutorial) {
      // Components are ready, start the tutorial (blocking already started)
      const timer = setTimeout(() => {
        console.log('Starting stock tutorial');
        startTutorial(STOCK_TUTORIAL_ID, tutorialSteps);
        // Allow interactions after tutorial starts
        setPreparingTutorial(false);
      }, 800);

      return () => {
        clearTimeout(timer);
        setPreparingTutorial(false);
      };
    }
  }, [
    componentsReady,
    isTutorialCompleted,
    activeTutorial,
    startTutorial,
    tutorialSteps,
    setPreparingTutorial,
  ]);

  // For debugging purposes - log tutorial state changes
  useEffect(() => {
    if (activeTutorial === STOCK_TUTORIAL_ID) {
      console.log(`Tutorial step: ${currentStep + 1} of ${tutorialSteps.length}`);
    }
  }, [currentStep, activeTutorial, tutorialSteps.length]);

  if (!isAuthenticated) {
    return <Redirect href="/auth/login" />;
  }

  if (isAuthenticated && !user?.verifiedAt) {
    return <Redirect href="/auth/verify-email" />;
  }

  if (!symbol) {
    return <Redirect href="../" />;
  }

  return (
    <BottomSheetModalProvider>
      <View className="flex-1">
        <Stack.Screen
          options={{
            headerTitle: '',
            headerShadowVisible: false,
            headerLeft: () => (
              <View ref={stockInfoRef}>
                <HeaderLeft symbol={symbol} user={user!} />
              </View>
            ),
            headerRight: () => (
              <View ref={favoriteButtonRef}>
                <HeaderRight symbol={symbol} />
              </View>
            ),
          }}
        />
        <View className="flex-1" ref={tabContentRef}>
          <MaterialTopTabs
            className="flex-1 overflow-y-auto"
            tabBar={props => (
              <View ref={tabsRef}>
                <CustomizedTabBar {...props} />
              </View>
            )}
            screenOptions={{
              animationEnabled: true,
            }}>
            <MaterialTopTabs.Screen
              name="overview"
              options={{ title: t('stock.common.tabBar.overview') }}
              initialParams={{ symbol }}
            />
            <MaterialTopTabs.Screen
              name="fins-ai"
              options={{ title: t('stock.common.tabBar.finsAI') }}
              initialParams={{ symbol }}
            />
            <MaterialTopTabs.Screen
              name="target-price"
              options={{ title: t('stock.common.tabBar.targetPrice') }}
              initialParams={{ symbol }}
            />
            <MaterialTopTabs.Screen
              name="industry"
              options={{ title: t('stock.common.tabBar.industry') }}
              initialParams={{ symbol }}
            />
            <MaterialTopTabs.Screen
              name="news"
              options={{ title: t('stock.common.tabBar.news') }}
              initialParams={{ symbol }}
            />
            <MaterialTopTabs.Screen
              name="financials"
              options={{ title: t('stock.common.tabBar.financials') }}
              initialParams={{ symbol }}
            />
            <MaterialTopTabs.Screen
              name="chart"
              options={{ title: t('stock.common.tabBar.chart') }}
              initialParams={{ symbol }}
            />
          </MaterialTopTabs>
        </View>
        <View>
          <StockUsageButton />
        </View>
      </View>
    </BottomSheetModalProvider>
  );
}

const CustomizedTabBar = ({ state, descriptors, navigation, position }: MaterialTopTabBarProps) => {
  const tabWidths = useRef<{ [key: string]: number }>({}).current;
  const [measurements, setMeasurements] = useState<{ width: number; x: number }[]>([]);
  const scrollViewRef = useRef<ScrollView>(null);

  // Shared spacing variables
  const CONTAINER_PADDING_LEFT = 16; // pl-4 = 16px
  const TAB_GAP = 24; // gap-6 = 24px

  const measureTab = (key: string, width: number) => {
    tabWidths[key] = width;
    // Update measurements when all tabs are measured
    if (Object.keys(tabWidths).length === state.routes.length) {
      const newMeasurements = state.routes.map((route, index) => {
        const width = tabWidths[route.key];
        // Calculate x position including padding and gaps
        const x =
          CONTAINER_PADDING_LEFT + // Initial padding
          Object.keys(tabWidths)
            .slice(0, state.routes.indexOf(route))
            .reduce((sum, key) => sum + tabWidths[key] + TAB_GAP, 0); // Add gap between tabs
        return { width, x };
      });
      setMeasurements(newMeasurements);
    }
  };

  const translateX = useMemo(() => {
    if (measurements.length !== state.routes.length) {
      return new Animated.Value(0);
    }

    return position.interpolate({
      inputRange: state.routes.map((_, i) => i),
      outputRange: measurements.map(m => m.x),
      extrapolate: 'clamp',
    });
  }, [measurements, state.routes, position]);

  // Scroll to the active tab when it changes
  useEffect(() => {
    if (measurements.length === state.routes.length && scrollViewRef.current) {
      const activeTabMeasurement = measurements[state.index];
      scrollViewRef.current.scrollTo({
        x: Math.max(0, activeTabMeasurement.x - CONTAINER_PADDING_LEFT),
        animated: true,
      });
    }
  }, [state.index, measurements, state.routes.length]);

  return (
    <View className="relative h-12 bg-white">
      <ScrollView
        ref={scrollViewRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        className="flex-1">
        <View
          style={{
            paddingLeft: CONTAINER_PADDING_LEFT,
            paddingRight: CONTAINER_PADDING_LEFT,
            gap: TAB_GAP,
            height: '100%',
            paddingVertical: 8, // Add vertical padding for better touch targets
          }}
          className="flex-row justify-start">
          {state.routes.map((route, index) => {
            const { options } = descriptors[route.key];
            const label = options.title || route.name;
            const isFocused = state.index === index;

            const onPress = async () => {
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            const inputRange = state.routes.map((_, i) => i);
            const opacity = position.interpolate({
              inputRange,
              outputRange: inputRange.map(i => (i === index ? 1 : 0.6)),
            });

            return (
              <TouchableOpacity
                key={route.key}
                onPress={onPress}
                onLayout={e => measureTab(route.key, e.nativeEvent.layout.width)}
                className="items-center justify-center"
                accessibilityRole="tab"
                accessibilityState={{ selected: isFocused }}>
                <Animated.Text
                  style={[
                    {
                      opacity,
                      color: isFocused ? '#000000' : '#666666',
                      fontWeight: 'bold',
                    },
                  ]}
                  className="text-center text-sm">
                  {label}
                </Animated.Text>
              </TouchableOpacity>
            );
          })}
        </View>
        {measurements.length === state.routes.length && (
          <View style={{ position: 'absolute', bottom: -1, left: 0, right: 0 }}>
            <Animated.View
              style={[
                {
                  position: 'absolute',
                  height: 3,
                  bottom: 0,
                  width: 1,
                  backgroundColor: '#FF6B00',
                  transform: [
                    { translateX },
                    {
                      scaleX: position.interpolate({
                        inputRange: state.routes.map((_, i) => i),
                        outputRange: measurements.map(m => m.width),
                        extrapolate: 'clamp',
                      }),
                    },
                  ],
                  transformOrigin: 'left',
                },
              ]}
            />
          </View>
        )}
      </ScrollView>
    </View>
  );
};
