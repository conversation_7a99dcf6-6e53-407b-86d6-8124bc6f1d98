import { useI18n } from '@/i18n/provider';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { View, Text, FlatList, SafeAreaView } from 'react-native';

import { useStockTrend, useViewStockUsage } from '@/hooks/useStock';

import ExceedLimitView from '@/components/common/ExceedLimitView';
import { InfoCard } from '@/components/common/InfoCard';
import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import StockLoading from '@/components/stock/stock-loading';

export default function FinsAi() {
  const { t } = useTranslation();
  const { locale } = useI18n();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data, isLoading } = useStockTrend(symbol, locale);
  const { data: stockUsage } = useViewStockUsage();

  // Check if limit reached
  const hasReachedLimit = (stockUsage?.data?.usage ?? 0) >= (stockUsage?.data?.limit ?? 0);
  const isStockViewed = stockUsage?.data?.stocks?.includes(symbol) ?? false;

  if (hasReachedLimit && !isStockViewed) {
    return <ExceedLimitView />;
  }

  if (isLoading) {
    return <StockLoading />;
  }

  if (!data) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <FlatList
        ListHeaderComponent={<PageDescription description={t('aiAnalysis.finsAI.description')} />}
        data={[data]}
        contentContainerClassName="px-4 py-4"
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => (
          <View>
            <Stack.Screen
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: '#FF6B00' },
                headerTintColor: '#fff',
              }}
            />

            {/* Overview Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.finsAI.overview.label')}
                title={t('aiAnalysis.finsAI.overview.title')}
                icon="line-chart"
              />

              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.finsAI.overview.content')}
                  content={item?.response?.overview}
                />
                <InfoCard
                  title={t('aiAnalysis.finsAI.overview.volume')}
                  content={item?.response?.volume}
                  className="mt-3"
                />
              </View>
            </View>

            {/* Perspective Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.finsAI.perspective.label')}
                title={t('aiAnalysis.finsAI.perspective.title')}
                icon="lightbulb-o"
              />

              <View className="gap-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.finsAI.perspective.keyPoints')}
                  content={item?.response?.recommend?.key}
                />
                <InfoCard
                  title={t('aiAnalysis.finsAI.perspective.buySignal')}
                  content={item?.response?.recommend?.buy}
                />
                <InfoCard
                  title={t('aiAnalysis.finsAI.perspective.sellSignal')}
                  content={item?.response?.recommend?.sell}
                />
              </View>
            </View>
          </View>
        )}
      />
    </SafeAreaView>
  );
}
