import { useI18n } from '@/i18n/provider';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack, useLocalSearchParams } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';

import { FlatList, Text, TouchableOpacity, View, SafeAreaView } from 'react-native';

import { useStockNews, useViewStockUsage } from '@/hooks/useStock';

import ExceedLimitView from '@/components/common/ExceedLimitView';
import { InfoCard } from '@/components/common/InfoCard';
import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import StockLoading from '@/components/stock/stock-loading';

export default function News() {
  const { t } = useTranslation();
  const { locale } = useI18n();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data, isLoading } = useStockNews(symbol, locale);
  const { data: stockUsage } = useViewStockUsage();

  // Check if limit reached
  const hasReachedLimit = (stockUsage?.data?.usage ?? 0) >= (stockUsage?.data?.limit ?? 0);
  const isStockViewed = stockUsage?.data?.stocks?.includes(symbol) ?? false;

  if (hasReachedLimit && !isStockViewed) {
    return <ExceedLimitView />;
  }

  const openLink = async (url: string) => {
    await WebBrowser.openBrowserAsync(url);
  };

  if (isLoading) {
    return <StockLoading />;
  }

  if (!data?.response) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  const { response: news } = data;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <FlatList
        ListHeaderComponent={<PageDescription description={t('aiAnalysis.news.description')} />}
        data={[news]}
        contentContainerClassName="px-4 py-4"
        renderItem={({ item }) => (
          <View>
            <Stack.Screen
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: '#FF6B00' },
                headerTintColor: '#fff',
              }}
            />

            {/* News List Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.news.list.label')}
                title={t('aiAnalysis.news.list.title')}
                icon="list-alt"
              />
              <View className="p-5">
                {item?.key_news_6 && item?.key_news_6.length > 0 ? (
                  item?.key_news_6.map((news, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => openLink(news.url)}
                      className={`${
                        index > 0 ? 'mt-4' : ''
                      } rounded-lg border border-gray-100 bg-gray-50 p-4 active:bg-gray-100`}>
                      <View className="flex-row items-start justify-between">
                        <View className="flex-1 pr-4">
                          <Text className="mb-2 text-base font-bold text-gray-800">
                            {news.title}
                          </Text>
                          <Text className="text-sm text-gray-600">{news.text}</Text>
                        </View>
                        <View className="mt-1">
                          <FontAwesome name="external-link" size={16} color="#FF6B00" />
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))
                ) : (
                  <Text className="text-center text-gray-500">
                    {t('aiAnalysis.news.list.noNews')}
                  </Text>
                )}
              </View>
            </View>

            {/* Overview Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.news.overview.label')}
                title={t('aiAnalysis.news.overview.title')}
                icon="newspaper-o"
              />
              <View className="space-y-3 p-5">
                <InfoCard title={t('aiAnalysis.news.overview.summary')} content={item?.overview} />
              </View>
            </View>

            {/* Summary Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.news.analysis.label')}
                title={t('aiAnalysis.news.analysis.title')}
                icon="line-chart"
              />
              <View className="space-y-3 p-5">
                <InfoCard content={item?.summary} />
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
      />
    </SafeAreaView>
  );
}
