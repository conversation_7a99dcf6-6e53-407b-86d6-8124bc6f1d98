import { useI18n } from '@/i18n/provider';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { FlatList, Text, View, SafeAreaView } from 'react-native';

import { useIndustryComparison, useViewStockUsage } from '@/hooks/useStock';

import ExceedLimitView from '@/components/common/ExceedLimitView';
import { InfoCard } from '@/components/common/InfoCard';
import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import StockLoading from '@/components/stock/stock-loading';

export default function Comparison() {
  const { t } = useTranslation();
  const { locale } = useI18n();
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data, isLoading } = useIndustryComparison(symbol, locale);
  const { data: stockUsage } = useViewStockUsage();

  // Check if limit reached
  const hasReachedLimit = (stockUsage?.data?.usage ?? 0) >= (stockUsage?.data?.limit ?? 0);
  const isStockViewed = stockUsage?.data?.stocks?.includes(symbol) ?? false;

  if (hasReachedLimit && !isStockViewed) {
    return <ExceedLimitView />;
  }

  if (isLoading) {
    return <StockLoading />;
  }

  if (!data?.response) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  const { response: comparison } = data;
  const { stock_comparision: stock, industry_comparision: industry } = comparison;

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <FlatList
        ListHeaderComponent={<PageDescription description={t('aiAnalysis.industry.description')} />}
        data={[comparison]}
        contentContainerClassName="px-4 py-4"
        renderItem={({ item }) => (
          <View>
            <Stack.Screen
              options={{
                headerShown: true,
                headerStyle: { backgroundColor: '#FF6B00' },
                headerTintColor: '#fff',
              }}
            />

            {/* Stock Overview Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.industry.overview.label')}
                title={t('aiAnalysis.industry.overview.title')}
                icon="file-text-o"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.industry.overview.summary')}
                  content={stock?.summary}
                />
              </View>
            </View>

            {/* Stock Industry Comparison Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.industry.analysis.label')}
                title={t('aiAnalysis.industry.analysis.title')}
                icon="bar-chart"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.industry.analysis.performance')}
                  content={stock?.sector_comparison?.grading}
                />
                <InfoCard
                  title={t('aiAnalysis.industry.analysis.difference')}
                  content={stock?.sector_comparison?.difference}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.analysis.score')}
                  content={stock?.sector_comparison?.score.toString()}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.analysis.overview')}
                  content={stock?.sector_comparison?.overview}
                  className="mt-3"
                />
              </View>
            </View>

            {/* Stock Technical Analysis Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.industry.trend.label')}
                title={t('aiAnalysis.industry.trend.title')}
                icon="area-chart"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.industry.trend.rsi')}
                  content={stock?.technical?.RSI}
                />
                <InfoCard
                  title={t('aiAnalysis.industry.trend.williamsR')}
                  content={stock?.technical?.['Williams %R']}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.trend.adx')}
                  content={stock?.technical?.ADX}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.trend.standardDeviation')}
                  content={stock?.technical?.['Standard Deviation']}
                  className="mt-3"
                />
              </View>
            </View>

            {/* Industry Overview Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.industry.industryOverview.label')}
                title={t('aiAnalysis.industry.industryOverview.title')}
                icon="industry"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.industry.industryOverview.forecast')}
                  content={industry?.forecast}
                />
              </View>
            </View>

            {/* Market Comparison Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.industry.marketComparison.label')}
                title={t('aiAnalysis.industry.marketComparison.title')}
                icon="line-chart"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.industry.marketComparison.performance')}
                  content={industry?.market_comparison?.grading}
                />
                <InfoCard
                  title={t('aiAnalysis.industry.marketComparison.difference')}
                  content={industry?.market_comparison?.difference}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.marketComparison.score')}
                  content={industry?.market_comparison?.score?.toString()}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.marketComparison.overview')}
                  content={industry?.market_comparison?.overview}
                  className="mt-3"
                />
              </View>
            </View>

            {/* Industry Technical Analysis Card */}
            <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
              <SectionHeader
                label={t('aiAnalysis.industry.technicalAnalysis.label')}
                title={t('aiAnalysis.industry.technicalAnalysis.title')}
                icon="area-chart"
              />
              <View className="space-y-3 p-5">
                <InfoCard
                  title={t('aiAnalysis.industry.technicalAnalysis.rsi')}
                  content={industry?.technical?.RSI}
                />
                <InfoCard
                  title={t('aiAnalysis.industry.technicalAnalysis.williamsR')}
                  content={industry?.technical?.['Williams %R']}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.technicalAnalysis.adx')}
                  content={industry?.technical?.ADX}
                  className="mt-3"
                />
                <InfoCard
                  title={t('aiAnalysis.industry.technicalAnalysis.standardDeviation')}
                  content={industry?.technical?.['Standard Deviation']}
                  className="mt-3"
                />
              </View>
            </View>
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
      />
    </SafeAreaView>
  );
}
