import { formatNumber, formatMarketCap } from '@/utils/format';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useNavigation } from '@react-navigation/native';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { useState } from 'react';

import { ScrollView, Text, TouchableOpacity, View, SafeAreaView } from 'react-native';

import { useStockQuote, useHistoricalPrices, useStockOutlook } from '@/hooks/useFMP';

import { PageDescription } from '@/components/common/PageDescription';
import { SectionHeader } from '@/components/common/SectionHeader';
import StockLineChart from '@/components/stock/stock-line-chart';
import StockLoading from '@/components/stock/stock-loading';

export default function Overview() {
  const { t } = useTranslation();
  const [showAll, setShowAll] = useState(false);
  const { symbol } = useLocalSearchParams<{ symbol: string }>();
  const { data: quoteData, isLoading: isLoadingQuote } = useStockQuote(symbol);
  const { data: historicalData, isLoading: isLoadingHistorical } = useHistoricalPrices(symbol);
  const { data: outlookData, isLoading: isLoadingOutlook } = useStockOutlook(symbol);

  const navigation = useNavigation();

  const handlePresentModalPress = () => {
    // Navigate to the chart tab instead of opening a modal
    navigation.navigate('chart' as never);
  };

  const isLoading = isLoadingQuote || isLoadingHistorical || isLoadingOutlook;

  if (isLoading) {
    return <StockLoading />;
  }

  if (!quoteData || !historicalData || !outlookData) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-lg text-gray-600">{t('common.noData')}</Text>
      </View>
    );
  }

  const statistics = [
    {
      title: t('stock.overview.statistics.marketCap'),
      value: formatMarketCap(quoteData?.marketCap),
    },
    { title: t('stock.overview.statistics.peRatio'), value: formatNumber(quoteData?.pe) },
    { title: t('stock.overview.statistics.eps'), value: formatNumber(quoteData?.eps) },
    { title: t('stock.overview.statistics.beta'), value: formatNumber(outlookData?.profile?.beta) },
    {
      title: t('stock.overview.statistics.yearHigh'),
      value: `$${formatNumber(quoteData?.yearHigh)}`,
    },
    {
      title: t('stock.overview.statistics.yearLow'),
      value: `$${formatNumber(quoteData?.yearLow)}`,
    },
    {
      title: t('stock.overview.statistics.fiftyDayAvg'),
      value: `$${formatNumber(quoteData?.priceAvg50)}`,
    },
    {
      title: t('stock.overview.statistics.twoHundredDayAvg'),
      value: `$${formatNumber(quoteData?.priceAvg200)}`,
    },
    { title: t('stock.overview.statistics.volume'), value: formatNumber(quoteData?.volume, 0) },
    {
      title: t('stock.overview.statistics.avgVolume'),
      value: formatNumber(quoteData?.avgVolume, 0),
    },
    {
      title: t('stock.overview.statistics.sharesOut'),
      value: formatNumber(quoteData?.sharesOutstanding, 0),
    },
    {
      title: t('stock.overview.statistics.lastDiv'),
      value: formatNumber(outlookData?.profile?.lastDiv),
    },
    { title: t('stock.overview.statistics.open'), value: `$${formatNumber(quoteData?.open)}` },
    {
      title: t('stock.overview.statistics.prevClose'),
      value: `$${formatNumber(quoteData?.previousClose)}`,
    },
    {
      title: t('stock.overview.statistics.dayHigh'),
      value: `$${formatNumber(quoteData?.dayHigh)}`,
    },
    { title: t('stock.overview.statistics.dayLow'), value: `$${formatNumber(quoteData?.dayLow)}` },
    {
      title: t('stock.overview.statistics.change'),
      value: `${formatNumber(quoteData?.changesPercentage)}%`,
    },
    { title: t('stock.overview.statistics.price'), value: `$${formatNumber(quoteData?.price)}` },
  ];

  const profile = [
    { title: t('stock.overview.companyProfile.ceo'), value: outlookData?.profile?.ceo },
    { title: t('stock.overview.companyProfile.country'), value: outlookData?.profile?.country },
    { title: t('stock.overview.companyProfile.industry'), value: outlookData?.profile?.industry },
    { title: t('stock.overview.companyProfile.sector'), value: outlookData?.profile?.sector },
    {
      title: t('stock.overview.companyProfile.employees'),
      value: outlookData?.profile?.fullTimeEmployees,
    },
    { title: t('stock.overview.companyProfile.exchange'), value: outlookData?.profile?.exchange },
  ];

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 px-4 py-4">
        <Stack.Screen
          options={{
            headerShown: true,
            headerStyle: { backgroundColor: '#FF6B00' },
            headerTintColor: '#fff',
          }}
        />

        <PageDescription description={t('stock.overview.description')} />

        {/* Key Statistics Card */}
        <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
          <SectionHeader
            label={t('stock.overview.market.label')}
            title={t('stock.overview.market.title')}
            icon="calculator"
          />
          <View className="p-4">
            <View className="flex-row flex-wrap justify-between">
              {statistics.slice(0, showAll ? statistics.length : 9).map((item, index) => (
                <View key={index} className="mb-3 w-[32%] rounded-lg p-1">
                  <Text className="text-xs text-gray-500">{item.title}</Text>
                  <Text className="text-sm font-semibold">{item.value ?? 'No data'}</Text>
                </View>
              ))}
            </View>
            <TouchableOpacity
              className="mt-2 flex-row items-center justify-center gap-2"
              onPress={() => setShowAll(!showAll)}>
              <Text className="text-center font-semibold text-[#FF6B00]">
                {showAll ? t('common.showLess') : t('common.showMore')}
              </Text>
              <FontAwesome
                name={showAll ? 'chevron-up' : 'chevron-down'}
                size={12}
                color="#FF6B00"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Price Chart Card */}
        <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
          <SectionHeader
            label={t('stock.overview.priceChart.label')}
            title={t('stock.overview.priceChart.title')}
            icon="line-chart"
          />
          <View className="pb-2 pt-2">
            {historicalData?.historical && historicalData.historical.length > 0 ? (
              <View>
                <StockLineChart
                  data={{ symbol, historical: historicalData.historical }}
                  height={300}
                  onViewDetailed={handlePresentModalPress}
                />
              </View>
            ) : (
              <View className="items-center justify-center py-8">
                <Text className="text-gray-600">
                  {historicalData === undefined ? 'Loading...' : 'No data'}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Company Profile Card */}
        <View className="mb-4 overflow-hidden rounded-2xl bg-white shadow-sm">
          <SectionHeader
            label={t('stock.overview.companyProfile.label')}
            title={t('stock.overview.companyProfile.title')}
            icon="building"
          />
          <View className="p-4">
            <View className="flex-row flex-wrap justify-between">
              {profile.map((item, index) => (
                <View
                  key={index}
                  className="mb-3 w-[48%] overflow-hidden rounded-lg bg-orange-50 p-3">
                  <Text className="text-sm text-orange-500">{item.title}</Text>
                  <Text className="font-semibold text-gray-800">{item.value ?? 'No data'}</Text>
                </View>
              ))}
            </View>
            <View className="mt-2 overflow-hidden rounded-lg bg-orange-50 p-4">
              <Text className="mb-2 text-sm text-orange-500">
                {t('stock.overview.companyProfile.description')}
              </Text>
              <Text className="text-sm leading-relaxed text-gray-800">
                {outlookData?.profile?.description ?? 'No data'}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
