import FontAwesome from '@expo/vector-icons/FontAwesome';
import { router, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { useState, useRef } from 'react';

import {
  View,
  SafeAreaView,
  Text,
  Image,
  TouchableOpacity,
  useWindowDimensions,
  FlatList,
  ViewToken,
} from 'react-native';

const slides = [
  {
    id: 1,
    image: require('@/assets/onboarding/logo.png'),
    titleKey: 'setting.onboarding.slides.0.title',
    descriptionKey: 'setting.onboarding.slides.0.description',
  },
  {
    id: 2,
    image: require('@/assets/onboarding/data reports.png'),
    titleKey: 'setting.onboarding.slides.1.title',
    descriptionKey: 'setting.onboarding.slides.1.description',
  },
  {
    id: 3,
    image: require('@/assets/onboarding/real time analytics.png'),
    titleKey: 'setting.onboarding.slides.2.title',
    descriptionKey: 'setting.onboarding.slides.2.description',
  },
  {
    id: 4,
    image: require('@/assets/onboarding/join.png'),
    titleKey: 'setting.onboarding.slides.3.title',
    descriptionKey: 'setting.onboarding.slides.3.description',
  },
];

export default function Onboarding() {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const { width } = useWindowDimensions();
  const flatListRef = useRef<FlatList>(null);
  const { from } = useLocalSearchParams<{ from?: string }>();

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      flatListRef.current?.scrollToIndex({
        index: currentSlide + 1,
        animated: true,
      });
      setCurrentSlide(prev => prev + 1);
    } else {
      // Navigate based on where the user came from
      if (from === 'settings') {
        router.back();
      } else {
        router.replace('/(tabs)/home');
      }
    }
  };

  const handleBack = () => {
    if (currentSlide > 0) {
      flatListRef.current?.scrollToIndex({
        index: currentSlide - 1,
        animated: true,
      });
      setCurrentSlide(prev => prev - 1);
    }
  };

  const handleSkip = () => {
    // Navigate based on where the user came from
    if (from === 'settings') {
      router.back();
    } else {
      router.replace('/(tabs)/home');
    }
  };

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };

  const onViewableItemsChanged = useRef(({ viewableItems }: { viewableItems: ViewToken[] }) => {
    if (viewableItems[0]) {
      setCurrentSlide(viewableItems[0].index || 0);
    }
  }).current;

  const renderItem = ({ item }: { item: (typeof slides)[0] }) => (
    <View style={{ width }} className="flex-1 items-center justify-center px-8">
      <View>
        <Image
          source={item.image}
          style={{ width: width * 0.4, height: width * 0.4 }}
          className="mx-auto rounded-xl"
          resizeMode="contain"
        />
        <Text className="mt-8 text-center text-3xl font-bold text-gray-900">
          {t(item.titleKey)}
        </Text>
        <Text className="mt-4 text-center text-base text-gray-600">{t(item.descriptionKey)}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Skip button */}
      <TouchableOpacity
        onPress={handleSkip}
        className="absolute right-4 top-12 z-10 mt-4 rounded-full bg-gray-100 px-6 py-2">
        <Text className="text-gray-600">{t('setting.onboarding.skip')}</Text>
      </TouchableOpacity>

      {/* Content */}
      <View className="flex-1 justify-center">
        <FlatList
          ref={flatListRef}
          data={slides}
          renderItem={renderItem}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
        />
      </View>

      {/* Bottom navigation */}
      <View className="pb-12 pt-6">
        {/* Progress dots */}
        <View className="mb-8 flex-row justify-center">
          {slides.map((_, index) => (
            <View
              key={index}
              className={`h-2 w-2 rounded-full ${
                currentSlide === index ? 'bg-theme' : 'bg-gray-300'
              } ${index < slides.length - 1 ? 'mr-3' : ''}`}
            />
          ))}
        </View>

        {/* Next/Back buttons */}
        <View className="flex-row justify-center space-x-4">
          {currentSlide > 0 && (
            <TouchableOpacity
              onPress={handleBack}
              className="flex-row items-center rounded-full bg-gray-100 px-12 py-4">
              <FontAwesome name="arrow-left" size={16} color="#FF6B00" className="mr-2" />
              <Text className="text-lg font-medium text-theme">{t('setting.onboarding.back')}</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            onPress={handleNext}
            className="ml-2 flex-row items-center rounded-full bg-theme px-12 py-4">
            <Text className="mr-2 text-lg font-medium text-white">
              {currentSlide === slides.length - 1
                ? t('setting.onboarding.getStarted')
                : t('setting.onboarding.next')}
            </Text>
            <FontAwesome
              name={currentSlide === slides.length - 1 ? 'rocket' : 'arrow-right'}
              size={16}
              color="white"
            />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
