import { FontAwesome } from '@expo/vector-icons';
import axios from 'axios';
import { router, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { useState } from 'react';
import React from 'react';

import { View, Text } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import Button from '@/components/shared/Button';
import Input from '@/components/shared/Input';
import { KeyboardDismissWrapper } from '@/components/shared/KeyboardDismissWrapper';

export default function ChangePassword() {
  const { t } = useTranslation();
  const { changePassword, isChangePasswordLoading } = useAuth();

  const [existingPassword, setExistingPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Validation checks
  const isInvalid =
    !existingPassword ||
    !newPassword ||
    !confirmNewPassword ||
    newPassword !== confirmNewPassword ||
    newPassword === existingPassword ||
    newPassword.length < 6;

  const handleChangePassword = async () => {
    try {
      setError('');
      setSuccessMessage('');

      const response = await changePassword({
        existingPassword,
        newPassword,
        confirmNewPassword,
      });

      if (response.success) {
        setSuccessMessage(t('auth.changePassword.success'));
        // Reset form
        setExistingPassword('');
        setNewPassword('');
        setConfirmNewPassword('');
      } else {
        setError(response.message || t('auth.changePassword.error.default'));
      }
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response?.status === 400) {
        setError(error.response?.data.message || t('auth.changePassword.error.default'));
      } else {
        setError(error.message);
      }
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('auth.changePassword.title'),
          headerShadowVisible: false,
          headerLeft: () => (
            <Text onPress={() => router.back()}>
              <FontAwesome name="chevron-left" size={16} color="black" />
            </Text>
          ),
        }}
      />
      <KeyboardDismissWrapper>
        <View className="flex-1 bg-white p-6">
          <View className="flex-1 justify-start gap-2">
            <Text className="mb-6 text-sm text-gray-600">
              {t('auth.changePassword.description')}
            </Text>

            <Input
              label={t('auth.changePassword.currentPassword')}
              value={existingPassword}
              onChangeText={text => {
                setExistingPassword(text);
                setError('');
                setSuccessMessage('');
              }}
              icon="lock"
              isPassword
              autoComplete="password"
            />

            <Input
              label={t('auth.changePassword.newPassword')}
              value={newPassword}
              onChangeText={text => {
                setNewPassword(text);
                setError('');
                setSuccessMessage('');
              }}
              icon="lock"
              isPassword
              autoComplete="new-password"
              description={t('auth.signup.passwordRequirements')}
            />

            <Input
              label={t('auth.changePassword.confirmNewPassword')}
              value={confirmNewPassword}
              onChangeText={text => {
                setConfirmNewPassword(text);
                setError('');
                setSuccessMessage('');
              }}
              icon="lock"
              isPassword
              autoComplete="new-password"
            />

            {newPassword && newPassword.length < 6 && (
              <Text className="text-sm text-yellow-600">
                {t('auth.changePassword.validation.minLength')}
              </Text>
            )}

            {newPassword && confirmNewPassword && newPassword !== confirmNewPassword && (
              <Text className="text-sm text-red-500">
                {t('auth.changePassword.validation.passwordMatch')}
              </Text>
            )}

            {newPassword && existingPassword && newPassword === existingPassword && (
              <Text className="text-sm text-yellow-600">
                {t('auth.changePassword.validation.differentPassword')}
              </Text>
            )}

            <View className="mt-6 justify-between gap-4">
              {error && (
                <View className="rounded-lg bg-red-50 p-3">
                  <Text className="text-center text-sm text-red-500">{error}</Text>
                </View>
              )}

              {successMessage && (
                <View className="rounded-lg bg-green-50 p-3">
                  <Text className="text-center text-sm text-green-700">{successMessage}</Text>
                </View>
              )}

              <Button
                onPress={handleChangePassword}
                disabled={isInvalid || isChangePasswordLoading}
                loading={isChangePasswordLoading}>
                {t('auth.changePassword.button')}
              </Button>
            </View>
          </View>
        </View>
      </KeyboardDismissWrapper>
    </>
  );
}
