import { FontAwesome } from '@expo/vector-icons';
import { Redirect, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import React, { useState, useEffect } from 'react';

import { View, Text, Pressable } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import EmailSvg from '@/components/illustrations/email';
import Button from '@/components/shared/Button';

export default function VerifyEmail() {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const { sendVerificationEmail, isSendingVerification, refetchProfile, isLoadingProfile } =
    useAuth();
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [timer, setTimer] = useState(60);

  const userName =
    user?.firstName && user?.lastName
      ? `${user.firstName} ${user.lastName}`
      : user?.name || user?.email;

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && timer > 0) {
      interval = setInterval(() => {
        setTimer(prevTimer => prevTimer - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsTimerRunning(false);
      setTimer(60);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, timer]);

  const handleSendVerification = async () => {
    if (isTimerRunning) return;

    try {
      await sendVerificationEmail();
      setIsEmailSent(true);
      setIsTimerRunning(true);
    } catch (error) {
      console.error('Failed to send verification email:', error);
    }
  };

  const handleRefresh = async () => {
    await refetchProfile();
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Failed to logout:', error);
    }
  };

  if (!user) {
    return <Redirect href="/auth/login" />;
  }

  if (user?.verifiedAt) {
    return <Redirect href="/home" />;
  }

  return (
    <>
      <SafeAreaView className="flex-1 gap-4" edges={['top']}>
        <Stack.Screen options={{ headerShown: false }} />
        <View className="flex-1 p-4">
          <View className="mb-10 flex-row items-center justify-between">
            <Text className="text-2xl font-bold">{t('auth.email.title')}</Text>
            <Pressable onPress={handleLogout}>
              <Text className="text-base text-gray-500 underline">{t('auth.email.logout')}</Text>
            </Pressable>
          </View>
          {userName && (
            <View className="mb-4">
              <Text className="text-xl font-bold text-gray-800">
                {t('auth.email.greeting', { name: userName })}
              </Text>
              <Text className="mt-4 text-base text-gray-700">{t('auth.email.description')}</Text>
            </View>
          )}
          <View className="mb-10 flex-1 justify-between">
            <View className="mt-10 h-48 w-full items-center">
              <EmailSvg height={'100%'} width={'100%'} />
            </View>

            <View className="gap-2">
              {isEmailSent && (
                <Text className="text-sm text-gray-500">
                  {t('auth.email.emailSentMessage', { email: user?.email })}
                </Text>
              )}
              <View className="flex-row gap-2">
                <Button
                  variant="primary"
                  style={{ flex: 1 }}
                  disabled={isTimerRunning}
                  loading={isSendingVerification}
                  onPress={handleSendVerification}>
                  {isTimerRunning
                    ? t('auth.email.resendTimer', { seconds: timer })
                    : isEmailSent
                      ? t('auth.email.resendButton')
                      : t('auth.email.sendButton')}
                </Button>
                {isEmailSent && (
                  <Button
                    variant="secondary"
                    onPress={handleRefresh}
                    loading={isLoadingProfile}
                    disabled={isLoadingProfile}>
                    <FontAwesome name="refresh" size={16} color="white" />
                  </Button>
                )}
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </>
  );
}
