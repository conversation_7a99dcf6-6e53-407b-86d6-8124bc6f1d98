import { Portal } from '@gorhom/portal';
import * as AppleAuthentication from 'expo-apple-authentication';
import { Link, router, Stack } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useEffect, useRef, useState, useCallback } from 'react';
import React from 'react';

import { View, Text, Dimensions, TouchableOpacity, Animated } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import Button from '@/components/shared/Button';
import Input from '@/components/shared/Input';
import { KeyboardDismissWrapper } from '@/components/shared/KeyboardDismissWrapper';
import LanguagePicker from '@/components/shared/LanguagePicker';

export default function SignUp() {
  const { t } = useTranslation();
  const {
    isAuthenticated,
    signUp,
    isSigningUp,
    signInWithGoogle,
    isGoogleSigningIn,
    signInWithApple,
    isAppleSigningIn,
  } = useAuth();
  const screenHeight = Dimensions.get('window').height;
  const [isAgreementChecked, setIsAgreementChecked] = useState(false);
  const [showAppleButton, setShowAppleButton] = useState(false);

  // Portal modal states
  const [showAgreementModal, setShowAgreementModal] = useState(false);

  // Animation values
  const agreementSlideAnim = useRef(new Animated.Value(screenHeight)).current;
  const agreementBackdropAnim = useRef(new Animated.Value(0)).current;

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');

  const isInvalid =
    !firstName ||
    !lastName ||
    !email ||
    !password ||
    !confirmPassword ||
    password !== confirmPassword;

  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)/home');
    }
  }, [isAuthenticated]);

  useEffect(() => {
    const checkAppleSignIn = async () => {
      try {
        const isAvailable = await AppleAuthentication.isAvailableAsync();
        setShowAppleButton(isAvailable);
      } catch (error) {
        setShowAppleButton(false);
      }
    };
    checkAppleSignIn();
  }, []);

  // Modal control functions
  const handlePresentAgreementModal = useCallback(() => {
    setShowAgreementModal(true);
    Animated.parallel([
      Animated.timing(agreementSlideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(agreementBackdropAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [agreementSlideAnim, agreementBackdropAnim]);

  const handleDismissAgreementModal = useCallback(() => {
    Animated.parallel([
      Animated.timing(agreementSlideAnim, {
        toValue: screenHeight,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(agreementBackdropAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowAgreementModal(false);
    });
  }, [agreementSlideAnim, agreementBackdropAnim, screenHeight]);

  const handleAgreementConfirm = async () => {
    setIsAgreementChecked(true);
    handleDismissAgreementModal();
    try {
      await signUp({ email, password, firstName, lastName, confirmPassword });
    } catch (error: any) {
      console.log(error);
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  const handleSignUp = async () => {
    try {
      setError('');
      if (!isAgreementChecked) {
        handlePresentAgreementModal();
        return;
      }
      await signUp({ email, password, firstName, lastName, confirmPassword });
    } catch (error: any) {
      console.log(error);
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setError('');
      await signInWithGoogle();
    } catch (error: any) {
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  const handleAppleSignIn = async () => {
    try {
      setError('');
      await signInWithApple();
    } catch (error: any) {
      if (error.message === 'Sign in was canceled') {
        // User canceled the sign-in, no need to show error
        return;
      }
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <KeyboardDismissWrapper>
        <SafeAreaView className="flex-1 bg-white">
          <View className="relative flex-1 bg-white p-6">
            <View className="absolute left-4 top-0">
              <LanguagePicker />
            </View>
            <View className="flex-1 justify-center gap-2">
              <Text className="mb-6 text-start text-4xl font-bold text-gray-900">
                {t('auth.signup.title')}
              </Text>

              <Button
                variant="google"
                onPress={handleGoogleSignIn}
                disabled={isGoogleSigningIn}
                loading={isGoogleSigningIn}>
                {t('auth.shared.googleButton')}
              </Button>

              {showAppleButton && (
                <Button
                  variant="apple"
                  onPress={handleAppleSignIn}
                  disabled={isAppleSigningIn}
                  loading={isAppleSigningIn}>
                  {t('auth.shared.appleButton')}
                </Button>
              )}

              <View className="my-8 flex-row items-center">
                <View className="h-[1px] flex-1 bg-gray-300" />
                <Text className="mx-4 text-sm text-gray-500">
                  {t('auth.shared.orContinueWith')}
                </Text>
                <View className="h-[1px] flex-1 bg-gray-300" />
              </View>

              <View className="flex-row gap-3">
                <View className="flex-1">
                  <Input
                    label={t('auth.signup.firstName')}
                    value={firstName}
                    onChangeText={setFirstName}
                    icon="account"
                    autoComplete="given-name"
                  />
                </View>

                <View className="flex-1">
                  <Input
                    label={t('auth.signup.lastName')}
                    value={lastName}
                    onChangeText={setLastName}
                    icon="account"
                    autoComplete="family-name"
                  />
                </View>
              </View>

              <Input
                label={t('auth.shared.email')}
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                icon="email"
                autoComplete="email"
              />

              <Input
                label={t('auth.shared.password')}
                textContentType="password"
                value={password}
                onChangeText={setPassword}
                icon="lock"
                isPassword
                autoComplete="new-password"
                description={t('auth.signup.passwordRequirements')}
              />

              <Input
                label={t('auth.signup.confirmPassword')}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                icon="lock"
                isPassword
                autoComplete="new-password"
              />

              <View className="flex-row items-start">
                <BouncyCheckbox
                  size={16}
                  fillColor="#FF9339"
                  unFillColor="#FFFFFF"
                  iconStyle={{ borderColor: '#FF9339', borderRadius: 5 }}
                  innerIconStyle={{ borderWidth: 2, borderRadius: 5 }}
                  textComponent={
                    <Text className="ml-2 text-sm text-gray-600">
                      {t('auth.login.agreement.checkbox')}{' '}
                      <Text
                        className="text-blue-600"
                        onPress={() =>
                          WebBrowser.openBrowserAsync(
                            `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/terms-of-use`
                          )
                        }>
                        {t('auth.login.agreement.userAgreement')}
                      </Text>{' '}
                      {t('auth.login.agreement.and')}{' '}
                      <Text
                        className="text-blue-600"
                        onPress={() =>
                          WebBrowser.openBrowserAsync(
                            `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/privacy-policy`
                          )
                        }>
                        {t('auth.login.agreement.privacyPolicy')}
                      </Text>
                    </Text>
                  }
                  onPress={(isChecked: boolean) => {
                    setIsAgreementChecked(isChecked);
                  }}
                  isChecked={isAgreementChecked}
                />
              </View>

              <View className="mt-1 justify-between gap-4">
                {error && <Text className="text-center text-sm text-red-500">{error}</Text>}
                <Button onPress={handleSignUp} disabled={isInvalid} loading={isSigningUp}>
                  {t('auth.signup.createAccount')}
                </Button>

                <Link href="/auth/login" replace asChild>
                  <Text className="text-center text-sm font-semibold text-gray-600">
                    {t('auth.signup.hasAccount')}
                  </Text>
                </Link>
              </View>
            </View>
          </View>
        </SafeAreaView>
      </KeyboardDismissWrapper>

      <Portal>
        {showAgreementModal && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
            }}>
            {/* Animated backdrop */}
            <Animated.View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                opacity: agreementBackdropAnim,
              }}>
              <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPress={handleDismissAgreementModal}
              />
            </Animated.View>

            {/* Agreement Modal */}
            <Animated.View
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 0,
                height: '25%',
                backgroundColor: 'white',
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                transform: [{ translateY: agreementSlideAnim }],
                shadowColor: '#000',
                shadowOffset: { width: 0, height: -2 },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 5,
              }}>
              <View className="mt-6 flex-1 p-4">
                <Text className="mb-2 text-xl font-bold">
                  {t('auth.login.agreement.modal.title')}
                </Text>
                <Text className="mb-6 text-base text-gray-600">
                  {t('auth.login.agreement.modal.content')}{' '}
                  <Text
                    className="text-blue-600"
                    onPress={() =>
                      WebBrowser.openBrowserAsync(
                        `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/terms-of-use`
                      )
                    }>
                    {t('auth.login.agreement.userAgreement')}
                  </Text>{' '}
                  {t('auth.login.agreement.and')}{' '}
                  <Text
                    className="text-blue-600"
                    onPress={() =>
                      WebBrowser.openBrowserAsync(
                        `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/privacy-policy`
                      )
                    }>
                    {t('auth.login.agreement.privacyPolicy')}
                  </Text>
                </Text>
                <View className="flex-row justify-end gap-4">
                  <TouchableOpacity
                    onPress={handleDismissAgreementModal}
                    className="rounded-lg border border-gray-300 px-4 py-2">
                    <Text className="text-gray-700">
                      {t('auth.login.agreement.modal.disagree')}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={handleAgreementConfirm}
                    className="rounded-lg bg-theme px-4 py-2">
                    <Text className="text-white">
                      {t('auth.login.agreement.modal.agreeAndContinue')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </View>
        )}
      </Portal>
    </>
  );
}
