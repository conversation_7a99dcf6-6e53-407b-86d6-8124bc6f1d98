import { Portal } from '@gorhom/portal';
import axios from 'axios';
import * as AppleAuthentication from 'expo-apple-authentication';
import { Link, router, Stack } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useEffect, useRef, useState, useCallback } from 'react';
// import { Text } from 'react-native-paper';

import React from 'react';

import {
  View,
  Text,
  Dimensions,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
  Animated,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import Button from '@/components/shared/Button';
import Input from '@/components/shared/Input';
import { KeyboardDismissWrapper } from '@/components/shared/KeyboardDismissWrapper';
import LanguagePicker from '@/components/shared/LanguagePicker';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Needed for React fragement <></>

export default function Login() {
  const { t } = useTranslation();
  const screenHeight = Dimensions.get('window').height;

  const {
    isAuthenticated,
    login,
    isLoggingIn,
    signInWithGoogle,
    isGoogleSigningIn,
    signInWithApple,
    isAppleSigningIn,
    forgotPassword,
    isForgotPasswordLoading,
  } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  // Forgot password states
  const [forgotEmail, setForgotEmail] = useState('');
  const [forgotPasswordError, setForgotPasswordError] = useState('');
  const [cooldown, setCooldown] = useState(0);
  const cooldownTimerRef = useRef<NodeJS.Timeout>();

  // Add new state for success message
  const [successMessage, setSuccessMessage] = useState('');

  const [isAgreementChecked, setIsAgreementChecked] = useState(false);

  const [showAppleButton, setShowAppleButton] = useState(false);

  // Portal modal states
  const [showAgreementModal, setShowAgreementModal] = useState(false);
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);

  // Animation values
  const agreementSlideAnim = useRef(new Animated.Value(screenHeight)).current;
  const agreementBackdropAnim = useRef(new Animated.Value(0)).current;
  const forgotPasswordSlideAnim = useRef(new Animated.Value(screenHeight)).current;
  const forgotPasswordBackdropAnim = useRef(new Animated.Value(0)).current;

  const isInvalid = !email || !password;
  const isForgotEmailValid = EMAIL_REGEX.test(forgotEmail);

  // Modal control functions
  const handlePresentModalPress = useCallback(() => {
    setShowForgotPasswordModal(true);
    Animated.parallel([
      Animated.timing(forgotPasswordSlideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(forgotPasswordBackdropAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, [forgotPasswordSlideAnim, forgotPasswordBackdropAnim]);

  const handleDismissModalPress = useCallback(() => {
    Animated.parallel([
      Animated.timing(forgotPasswordSlideAnim, {
        toValue: screenHeight,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(forgotPasswordBackdropAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowForgotPasswordModal(false);
    });
  }, [forgotPasswordSlideAnim, forgotPasswordBackdropAnim, screenHeight]);

  const handlePresentAgreementModal = useCallback(() => {
    setShowAgreementModal(true);
    Animated.parallel([
      Animated.timing(agreementSlideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(agreementBackdropAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [agreementSlideAnim, agreementBackdropAnim]);

  const handleDismissAgreementModal = useCallback(() => {
    Animated.parallel([
      Animated.timing(agreementSlideAnim, {
        toValue: screenHeight,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(agreementBackdropAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowAgreementModal(false);
    });
  }, [agreementSlideAnim, agreementBackdropAnim, screenHeight]);

  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)/home');
    }
  }, [isAuthenticated]);

  useEffect(() => {
    return () => {
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const checkAppleSignIn = async () => {
      try {
        const isAvailable = await AppleAuthentication.isAvailableAsync();
        setShowAppleButton(isAvailable);
      } catch (error) {
        setShowAppleButton(false);
      }
    };
    checkAppleSignIn();
  }, []);

  const startCooldown = () => {
    setCooldown(60);
    cooldownTimerRef.current = setInterval(() => {
      setCooldown(prev => {
        if (prev <= 1) {
          clearInterval(cooldownTimerRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleForgotPassword = async () => {
    try {
      setForgotPasswordError('');
      setSuccessMessage('');
      const response = await forgotPassword(forgotEmail);

      if (response.success) {
        setForgotEmail('');
        startCooldown();
        setSuccessMessage(
          t('auth.login.forgotPasswordModal.successMessage', { email: forgotEmail })
        );
      } else {
        setForgotPasswordError(response.message || t('auth.shared.errors.failedRequest'));
      }
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response?.status === 400) {
        setForgotPasswordError(
          error.response?.data.message || t('auth.shared.errors.failedRequest')
        );
      } else {
        setForgotPasswordError(error.message);
      }
    }
  };

  const handleAgreementConfirm = async () => {
    setIsAgreementChecked(true);
    handleDismissAgreementModal();
    try {
      await login({ email, password });
    } catch (error: any) {
      console.log(error);
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  const handleLogin = async () => {
    try {
      setError('');
      if (!isAgreementChecked) {
        handlePresentAgreementModal();
        return;
      }
      await login({ email, password });
    } catch (error: any) {
      console.log(error);
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setError('');
      await signInWithGoogle();
    } catch (error: any) {
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  const handleAppleSignIn = async () => {
    try {
      setError('');
      await signInWithApple();
    } catch (error: any) {
      if (error.message === 'Sign in was canceled') {
        // User canceled the sign-in, no need to show error
        return;
      }
      setError(
        error?.translationKey
          ? t(error.translationKey, t('error.common.tryAgain'))
          : t('error.common.tryAgain')
      );
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <KeyboardDismissWrapper>
        <SafeAreaView className="flex-1 bg-white">
          <View className="relative flex-1 bg-white p-6">
            <View className="absolute left-4 top-0">
              <LanguagePicker />
            </View>
            <View className="flex-1 justify-center gap-2">
              <Text className="mb-6 text-start text-4xl font-bold text-gray-900">
                {t('auth.login.title')}
              </Text>

              <Button
                variant="google"
                onPress={handleGoogleSignIn}
                disabled={isGoogleSigningIn}
                loading={isGoogleSigningIn}>
                {t('auth.shared.googleButton')}
              </Button>

              {showAppleButton && (
                <Button
                  variant="apple"
                  onPress={handleAppleSignIn}
                  disabled={isAppleSigningIn}
                  loading={isAppleSigningIn}>
                  {t('auth.shared.appleButton')}
                </Button>
              )}

              <View className="my-8 flex-row items-center">
                <View className="h-[1px] flex-1 bg-gray-300" />
                <Text className="mx-4 text-sm text-gray-500">
                  {t('auth.shared.orContinueWith')}
                </Text>
                <View className="h-[1px] flex-1 bg-gray-300" />
              </View>

              <Input
                label={t('auth.shared.email')}
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                icon="email"
                autoComplete="email"
              />

              <Input
                label={t('auth.shared.password')}
                value={password}
                onChangeText={setPassword}
                icon="lock"
                isPassword
                autoComplete="password"
              />

              <View className="flex-row justify-end">
                <Text className="text-sm text-blue-600" onPress={handlePresentModalPress}>
                  {t('auth.login.forgotPassword')}
                </Text>
              </View>

              <View className="flex-row items-start">
                <BouncyCheckbox
                  size={16}
                  fillColor="#FF9339"
                  unFillColor="#FFFFFF"
                  iconStyle={{ borderColor: '#FF9339', borderRadius: 5 }}
                  innerIconStyle={{ borderWidth: 2, borderRadius: 5 }}
                  textComponent={
                    <Text className="ml-2 text-sm text-gray-600">
                      {t('auth.login.agreement.checkbox')}{' '}
                      <Text
                        className="text-blue-600"
                        onPress={() =>
                          WebBrowser.openBrowserAsync(
                            `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/terms-of-use`
                          )
                        }>
                        {t('auth.login.agreement.userAgreement')}
                      </Text>{' '}
                      {t('auth.login.agreement.and')}{' '}
                      <Text
                        className="text-blue-600"
                        onPress={() =>
                          WebBrowser.openBrowserAsync(
                            `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/privacy-policy`
                          )
                        }>
                        {t('auth.login.agreement.privacyPolicy')}
                      </Text>
                    </Text>
                  }
                  onPress={(isChecked: boolean) => {
                    setIsAgreementChecked(isChecked);
                  }}
                  isChecked={isAgreementChecked}
                />
              </View>

              <View className="mt-1 justify-between gap-4">
                {error && <Text className="text-center text-sm text-red-500">{error}</Text>}
                <Button
                  onPress={handleLogin}
                  disabled={isInvalid || isLoggingIn}
                  loading={isLoggingIn}>
                  {t('auth.login.signIn')}
                </Button>

                <Link href="/auth/signup" replace asChild>
                  <Text className="text-center text-sm font-semibold text-gray-600">
                    {t('auth.login.noAccount')}
                  </Text>
                </Link>
              </View>
            </View>
          </View>
        </SafeAreaView>
      </KeyboardDismissWrapper>

      <Portal>
        {(showAgreementModal || showForgotPasswordModal) && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
            }}>
            {/* Animated backdrop */}
            <Animated.View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                opacity: showAgreementModal ? agreementBackdropAnim : forgotPasswordBackdropAnim,
              }}>
              <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPress={showAgreementModal ? handleDismissAgreementModal : handleDismissModalPress}
              />
            </Animated.View>

            {/* Agreement Modal */}
            {showAgreementModal && (
              <Animated.View
                style={{
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  bottom: 0,
                  height: '25%',
                  backgroundColor: 'white',
                  borderTopLeftRadius: 20,
                  borderTopRightRadius: 20,
                  transform: [{ translateY: agreementSlideAnim }],
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: -2 },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  elevation: 5,
                }}>
                <View className="mt-6 flex-1 p-4">
                  <Text className="mb-2 text-xl font-bold">
                    {t('auth.login.agreement.modal.title')}
                  </Text>
                  <Text className="mb-6 text-base text-gray-600">
                    {t('auth.login.agreement.modal.content')}{' '}
                    <Text
                      className="text-blue-600"
                      onPress={() =>
                        WebBrowser.openBrowserAsync(
                          `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/terms-of-use`
                        )
                      }>
                      {t('auth.login.agreement.userAgreement')}
                    </Text>{' '}
                    {t('auth.login.agreement.and')}{' '}
                    <Text
                      className="text-blue-600"
                      onPress={() =>
                        WebBrowser.openBrowserAsync(
                          `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/privacy-policy`
                        )
                      }>
                      {t('auth.login.agreement.privacyPolicy')}
                    </Text>
                  </Text>
                  <View className="flex-row justify-end gap-4">
                    <TouchableOpacity
                      onPress={handleDismissAgreementModal}
                      className="rounded-lg border border-gray-300 px-4 py-2">
                      <Text className="text-gray-700">
                        {t('auth.login.agreement.modal.disagree')}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={handleAgreementConfirm}
                      className="rounded-lg bg-theme px-4 py-2">
                      <Text className="text-white">
                        {t('auth.login.agreement.modal.agreeAndContinue')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </Animated.View>
            )}

            {/* Forgot Password Modal */}
            {showForgotPasswordModal && (
              <Animated.View
                style={{
                  position: 'absolute',
                  left: 0,
                  right: 0,
                  bottom: 0,
                  height: '75%',
                  backgroundColor: 'white',
                  borderTopLeftRadius: 20,
                  borderTopRightRadius: 20,
                  transform: [{ translateY: forgotPasswordSlideAnim }],
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: -2 },
                  shadowOpacity: 0.25,
                  shadowRadius: 3.84,
                  elevation: 5,
                }}>
                <View className="mt-6 flex-1 p-4">
                  <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                    <View className="flex-1">
                      <Text className="mb-2 text-xl font-bold">
                        {t('auth.login.forgotPasswordModal.title')}
                      </Text>
                      <Text className="mb-4 text-sm text-gray-600">
                        {t('auth.login.forgotPasswordModal.description')}
                      </Text>

                      <Input
                        label={t('auth.login.forgotPasswordModal.emailLabel')}
                        value={forgotEmail}
                        onChangeText={text => {
                          setForgotEmail(text);
                          setSuccessMessage('');
                        }}
                        autoCapitalize="none"
                        keyboardType="email-address"
                        icon="email"
                        autoComplete="email"
                      />

                      {forgotPasswordError ? (
                        <Text className="mt-2 text-sm text-red-500">{forgotPasswordError}</Text>
                      ) : null}

                      {cooldown > 0 ? (
                        <Text className="mt-2 text-sm text-gray-500">
                          {t('auth.login.forgotPasswordModal.cooldown', { seconds: cooldown })}
                        </Text>
                      ) : null}

                      <View className="my-4">
                        <Button
                          onPress={handleForgotPassword}
                          disabled={!isForgotEmailValid || cooldown > 0 || isForgotPasswordLoading}
                          loading={isForgotPasswordLoading}>
                          {t('auth.login.forgotPasswordModal.sendButton')}
                        </Button>
                      </View>

                      {successMessage ? (
                        <View className="rounded-lg bg-green-50 p-3">
                          <Text className="text-center text-sm text-green-700">
                            {successMessage}
                          </Text>
                        </View>
                      ) : null}
                    </View>
                  </TouchableWithoutFeedback>
                </View>
              </Animated.View>
            )}
          </View>
        )}
      </Portal>
    </>
  );
}
