import AsyncStorage from '@react-native-async-storage/async-storage';
import { Redirect } from 'expo-router';

import { useState, useEffect } from 'react';

export default function Welcome() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<boolean | null>(null);

  useEffect(() => {
    checkOnboardingStatus();
  }, []);

  const checkOnboardingStatus = async () => {
    try {
      const status = await AsyncStorage.getItem('hasCompletedOnboarding');
      setHasCompletedOnboarding(status === 'true');
    } catch (error) {
      console.error('Error checking onboarding status:', error);
      setHasCompletedOnboarding(false);
    }
  };

  if (hasCompletedOnboarding === null) {
    // Still loading
    return null;
  }

  if (hasCompletedOnboarding) {
    return <Redirect href="/home" />;
  }

  return <Redirect href="/onboarding" />;

  // return (
  //   <SafeAreaView className="flex-1 bg-white">
  //     <View className="flex-1 px-6 py-8">
  //       {/* Language Toggle */}
  //       <View className="absolute left-4 top-0">
  //         <LanguagePicker />
  //       </View>

  //       {/* Header Section */}
  //       <View className="items-center pb-8">
  //         <Image
  //           source={require('../assets/logo/icon.png')}
  //           className="mb-6 h-20 w-20 rounded-xl"
  //         />
  //         <Text className="mb-2 text-center text-3xl font-bold">{t('common.welcome')}</Text>
  //         <Text className="text-center text-base text-gray-600">{t('common.description')}</Text>
  //       </View>
  //     </View>
  //   </SafeAreaView>
  // );
}
