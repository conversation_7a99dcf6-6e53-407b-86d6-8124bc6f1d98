import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useReportManage } from '@/hooks/useReport';

import SubscribeForm from '@/components/form/SubscribeForm';
import UpdateForm from '@/components/form/UpdateForm';

const UnauthorizedView = ({ onLogin }: { onLogin: () => void }) => {
  const { t } = useTranslation();
  return (
    <View className="flex-1 items-center justify-center px-4">
      <View className="items-center rounded-2xl bg-gray-50 p-8 shadow-sm">
        <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-theme/10">
          <FontAwesome name="newspaper-o" size={40} color="#FF6B00" />
        </View>
        <Text className="mb-2 text-xl font-bold text-gray-900">
          {t('report.unauthorized.title')}
        </Text>
        <Text className="mb-6 text-center text-gray-600">
          {t('report.unauthorized.description')}
        </Text>
        <TouchableOpacity onPress={onLogin} className="w-full rounded-lg bg-theme px-6 py-3">
          <View className="flex-row items-center justify-center">
            <FontAwesome name="sign-in" size={16} color="white" className="mr-2" />
            <Text className="ml-2 font-medium text-white">{t('report.unauthorized.signIn')}</Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default function Report() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <UnauthorizedView onLogin={() => router.push('/auth/login')} />;
  }

  return <AuthorizedContent />;
}

function AuthorizedContent() {
  const { profile, isLoadingProfile } = useAuth();
  const email = profile?.data?.email || '';
  const { data: reportData, isLoading: isLoadingReport } = useReportManage(email);

  // Refetch data when email changes
  // useFocusEffect(
  //   useCallback(() => {
  //     const refreshData = async () => {
  //       if (email) {
  //         queryClient.invalidateQueries({
  //           queryKey: REPORT_KEYS.useReportManage(email),
  //         });
  //       }
  //     };
  //     refreshData();
  //   }, [email, queryClient])
  // );

  // Show loading state while profile or report data is loading
  if (isLoadingProfile || isLoadingReport) {
    return (
      <View className="flex-1 items-center justify-center bg-white">
        <ActivityIndicator size="large" color="#FF6B00" />
      </View>
    );
  }

  if (!profile?.data) {
    return null;
  }

  const { firstName, lastName, email: userEmail } = profile.data;

  return (
    <View className="flex-1 bg-white">
      {reportData?.success ? (
        <UpdateForm email={userEmail} />
      ) : (
        <SubscribeForm
          defaultEmail={userEmail}
          defaultFirstName={firstName}
          defaultLastName={lastName}
        />
      )}
    </View>
  );
}
