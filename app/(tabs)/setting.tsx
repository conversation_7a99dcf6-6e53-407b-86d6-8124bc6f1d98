import notificationService from '@/lib/notification/notification.service';
import { handleOpenSurvey } from '@/utils/survey';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LinearGradient } from 'expo-linear-gradient';
import { Href, router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { SafeAreaView } from 'react-native-safe-area-context';

import React, { useEffect, useState } from 'react';

import { ScrollView, View, Text, ActivityIndicator } from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useViewStockUsage } from '@/hooks/useStock';

import { FontAwesomeIconName } from '@/components/common/SettingMenuItem';
import { SettingsSection, type SettingsSections } from '@/components/common/SettingsSection';

const SHOW_DEVELOPER_TOOLS = true;

export default function Setting() {
  const { t } = useTranslation();
  const {
    isAuthenticated,
    user,
    isLoadingProfile,
    logout,
    getAccessToken,
    hasPermissionTo,
    refetchProfile,
  } = useAuth();
  const { refetch: refetchViewStockUsage } = useViewStockUsage();
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const fetchToken = async () => {
      if (user) {
        const token = await getAccessToken();
        setToken(token);
      }
    };

    fetchToken();
  }, [user, getAccessToken]);

  const displayName =
    user?.name ||
    (user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : t('setting.guest'));

  const initials = displayName
    .split(' ')
    .map((n: string) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleDismissSurvey = async () => {
    await refetchProfile();
    await refetchViewStockUsage();
  };

  const handleTestNotifications = async () => {
    try {
      console.log('[Settings] Testing immediate notifications...');

      // Test immediate notifications
      const success = await notificationService.testAllNotifications();

      if (success) {
        alert('✅ Test notifications sent! Check your device notifications.');
      } else {
        alert('❌ Failed to send test notifications. Check console logs for details.');
      }
    } catch (error) {
      console.error('Testing notifications failed:', error);
      alert(`❌ Failed to send test notifications: ${error}`);
    }
  };

  const handleCheckNotificationStatus = async () => {
    try {
      console.log('[Settings] Checking detailed notification status...');

      const status = await notificationService.areAllNotificationsScheduled();
      await notificationService.listAllScheduledNotifications();

      // Also check badge status
      const badgeCount = await notificationService.getBadgeCount();
      const unreadCount = await notificationService.getUnreadNotificationCount();

      alert(
        `🔍 Notification Status:\n\n` +
          `📅 Weekly Report: ${status.weekly ? '✅ Scheduled' : '❌ Not Scheduled'}\n` +
          `📈 Potential Stocks: ${status.potential ? '✅ Scheduled' : '❌ Not Scheduled'}\n` +
          `⭐ Preferred Stocks: ${status.preferred ? '✅ Scheduled' : '❌ Not Scheduled'}\n\n` +
          `🎯 All Scheduled: ${status.allScheduled ? '✅ Yes' : '❌ No'}\n\n` +
          `🔴 Badge Count: ${badgeCount}\n` +
          `📬 Unread Count: ${unreadCount}\n\n` +
          `Check console logs for detailed information.`
      );
    } catch (error) {
      console.error('[Settings] Error checking status:', error);
      alert(`❌ Failed to check status: ${error}`);
    }
  };

  const handleTestAnnouncement = async () => {
    try {
      const announcementService = require('@/lib/announcement/announcement.service').default;
      await announcementService.testShowAnnouncement();
      alert('✅ Test announcement reset! Please restart the app to see the announcement.');
    } catch (error) {
      console.error('[Settings] Error testing announcement:', error);
      alert(`❌ Failed to test announcement: ${error}`);
    }
  };

  const sections: SettingsSections[] = [
    {
      title: t('setting.account.title'),
      items: [
        ...(user && token
          ? [
              {
                icon: 'file-text-o' as FontAwesomeIconName,
                label: t('setting.account.fillSurvey'),
                onPress: () => handleOpenSurvey(user, token, handleDismissSurvey),
                show: !hasPermissionTo('free_report_2024'),
              },
            ]
          : []),
        {
          href: '/setting/pricing' as Href,
          icon: 'star' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.pricing'),
        },
        {
          href: '/setting/set-language' as Href,
          icon: 'language' as FontAwesomeIconName,
          label: t('setting.preferences.language'),
        },
        {
          href: '/setting/account' as Href,
          icon: 'cog' as FontAwesomeIconName,
          label: t('setting.account.moreSettings'),
        },
      ],
    },
    {
      title: t('setting.helpAndSupport.title'),
      items: [
        {
          href: '/setting/ai-chat',
          icon: 'comments',
          label: t('setting.helpAndSupport.aiChat'),
          highlight: true,
        },
        {
          href: '/setting/about',
          icon: 'info-circle' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.aboutUs'),
        },
        {
          browserUrl: `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/faq`,
          icon: 'question-circle' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.faq'),
        },
        {
          browserUrl: `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/terms-of-use`,
          icon: 'list-ul' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.userAgreement'),
        },
        {
          browserUrl: `${process.env.EXPO_PUBLIC_WEBSITE_URL}/mobile/privacy-policy`,
          icon: 'list-ul' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.privacyPolicy'),
        },
        {
          href: '/setting/contact-us' as Href,
          icon: 'envelope' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.contactUs'),
        },
        {
          icon: 'rocket' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.onboarding'),
          onPress: () => router.push('/onboarding?from=settings' as Href),
        },
        {
          href: '/setting/tutorials' as Href,
          icon: 'book' as FontAwesomeIconName,
          label: t('setting.helpAndSupport.tutorials'),
        },
      ],
    },
    {
      title: t('setting.others.title'),
      items: [
        ...(isAuthenticated
          ? [
              {
                href: '/setting/disclaimer' as Href,
                icon: 'exclamation-circle' as FontAwesomeIconName,
                label: t('setting.helpAndSupport.disclaimer'),
              },
              {
                href: '/setting/update-log' as Href,
                icon: 'history' as FontAwesomeIconName,
                label: t('setting.helpAndSupport.updateLog'),
              },
              {
                icon: 'sign-out' as FontAwesomeIconName,
                label: t('setting.preferences.logout'),
                onPress: handleLogout,
                variant: 'danger' as 'danger',
              },
            ]
          : []),
      ],
    },
    ...(SHOW_DEVELOPER_TOOLS
      ? [
          {
            title: 'Developer Tools',
            items: [
              {
                icon: 'bell' as FontAwesomeIconName,
                label: 'Test Immediate Notifications',
                onPress: handleTestNotifications,
              },
              {
                icon: 'info-circle' as FontAwesomeIconName,
                label: 'Check Detailed Status',
                onPress: handleCheckNotificationStatus,
              },
              {
                icon: 'bullhorn' as FontAwesomeIconName,
                label: 'Test Announcement',
                onPress: handleTestAnnouncement,
              },
              {
                href: '/setting/chart-testing' as Href,
                icon: 'line-chart' as FontAwesomeIconName,
                label: 'Chart Testing',
              },
            ],
          },
        ]
      : []),
  ];

  return (
    <SafeAreaView edges={['top']} className="flex-1">
      <ScrollView className="flex-1 bg-white">
        {isLoadingProfile ? (
          <View className="h-32 items-center justify-center">
            <ActivityIndicator />
          </View>
        ) : (
          <>
            <LinearGradient
              colors={['#FF8C00', '#FFC107']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              locations={[0, 1]}>
              <View className="flex-row items-center gap-4 p-5 py-8">
                <View className="h-12 w-12 items-center justify-center rounded-full bg-white/20">
                  <Text className="text-lg font-bold text-white">{initials}</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-white">{displayName}</Text>
                  {user?.email && <Text className="text-sm text-white/80">{user.email}</Text>}
                </View>
              </View>
            </LinearGradient>

            <View className="px-4 pt-4">
              {sections.map((section, index) => (
                <SettingsSection key={section.title || `section-${index}`} section={section} />
              ))}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
