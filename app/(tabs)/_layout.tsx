import FontAwesome from '@expo/vector-icons/FontAwesome';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Redirect, Tabs } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { useRef } from 'react';

import { View } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import { AppLayoutProvider } from '@/components/common/AppLayoutContext';
import StockUsageButton from '@/components/common/StockUsageButton';

// Create simple components for tab icons
const HomeTabIcon = ({ color, size }: { color: string; size: number }) => (
  <View>
    <FontAwesome name="server" size={size} color={color} />
  </View>
);

const ReportTabIcon = ({ color, size }: { color: string; size: number }) => (
  <View>
    <FontAwesome name="newspaper-o" size={size} color={color} />
  </View>
);

export default function TabsLayout() {
  const { isAuthenticated, user } = useAuth();
  const { t } = useTranslation();
  const stockUsageButtonRef = useRef(null);

  if (!isAuthenticated) {
    return <Redirect href="/auth/login" />;
  }

  if (isAuthenticated && !user?.verifiedAt) {
    return <Redirect href="/auth/verify-email" />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <BottomSheetModalProvider>
        <AppLayoutProvider stockUsageButtonRef={stockUsageButtonRef}>
          <Tabs
            screenOptions={{
              headerShadowVisible: false,
              tabBarActiveTintColor: '#FF9339',
              tabBarInactiveTintColor: '#666666',
            }}>
            <Tabs.Screen
              name="home"
              options={{
                headerShown: false,
                title: t('common.tabs.home'),
                tabBarLabel: t('common.tabs.home'),
                tabBarIcon: props => <HomeTabIcon {...props} />,
              }}
            />
            <Tabs.Screen
              name="report"
              options={{
                title: t('common.tabs.report'),
                tabBarLabel: t('common.tabs.report'),
                tabBarIcon: props => <ReportTabIcon {...props} />,
              }}
            />
            <Tabs.Screen
              name="setting"
              options={{
                title: t('common.tabs.setting'),
                tabBarLabel: t('common.tabs.setting'),
                tabBarIcon: ({ color, size }) => (
                  <FontAwesome name="cog" size={size} color={color} />
                ),
                headerShown: false,
              }}
            />
          </Tabs>
        </AppLayoutProvider>
        <StockUsageButton ref={stockUsageButtonRef} />
      </BottomSheetModalProvider>
    </GestureHandlerRootView>
  );
}
