import { bottomSheetRegistry } from '@/lib/bottomSheetRegistry';
import { useStockStore } from '@/stores/stock';
import { Stock } from '@/types/stock';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetHandle,
  BottomSheetHandleProps,
} from '@gorhom/bottom-sheet';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Snackbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import * as React from 'react';

import {
  ActivityIndicator,
  Dimensions,
  Keyboard,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  TextInput,
  RefreshControl,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useWatchlistRefresh } from '@/hooks/useFMP';
import { useCompanySearch, useLatestClosingPrices } from '@/hooks/useStock';

import { COLORS } from '@/constants/colors';

import { useAppLayout } from '@/components/common/AppLayoutContext';
import EmptyWatchlistSvg from '@/components/illustrations/empty-watchlist';
import SearchStockItem from '@/components/stock/search-stock-item';
import WatchListItem from '@/components/stock/watch-list-item';
import { useTutorial } from '@/components/tutorial/TutorialContext';
import { TutorialStep } from '@/components/tutorial/TutorialContext';

const stockData: Stock[] = [
  { symbol: 'META', name: 'Meta Platforms Inc.' },
  { symbol: 'MSFT', name: 'Microsoft Corporation' },
  { symbol: 'AMZN', name: 'Amazon.com Inc.' },
  { symbol: 'AAPL', name: 'Apple Inc.' },
  { symbol: 'GOOGL', name: 'Alphabet Inc.' },
  { symbol: 'NVDA', name: 'NVIDIA Corporation' },
  { symbol: 'TSLA', name: 'Tesla Inc.' },
];

const RECENT_SEARCHES_KEY = 'recent_searches';
const MAX_RECENT_SEARCHES = 5;

// Tutorial ID
const WATCHLIST_TUTORIAL_ID = 'watchlist_tutorial';

export default function WatchList() {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = React.useState<string>('');
  const [showAll, setShowAll] = useState(false);
  const [imageErrors, setImageErrors] = useState<{ [key: string]: boolean }>({});
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['90%'], []);
  const [componentsReady, setComponentsReady] = useState(false);
  const { stockUsageButtonRef } = useAppLayout();

  // Refs for tutorial
  const searchBarRef = useRef(null);
  const watchlistRef = useRef(null);
  const bottomSheetSearchInputRef = useRef(null);
  const bottomSheetSearchListRef = useRef(null);

  // Register bottom sheet with global registry
  useEffect(() => {
    bottomSheetRegistry.register(bottomSheetModalRef);
    return () => {
      bottomSheetRegistry.unregister(bottomSheetModalRef);
    };
  }, []);

  const {
    subscribeToStocks,
    unsubscribeFromStocks,
    followedStocks,
    followStock,
    unfollowStock,
    isFollowed,
    updateLastClosePrices,
  } = useStockStore();

  // Set up bulk refresh for watchlist quotes when app becomes active
  const { refreshAllQuotes, canRefresh } = useWatchlistRefresh(
    followedStocks.map(stock => stock.symbol)
  );

  // State for pull-to-refresh
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle manual refresh with unified throttling
  const handleRefresh = useCallback(async () => {
    // Prevent refreshing if already in progress
    if (isRefreshing) {
      return;
    }

    // Check if refresh is allowed (uses shared throttling)
    if (!canRefresh()) {
      return; // Silently ignore if throttled
    }

    // Check if we have stocks to refresh
    if (followedStocks.length === 0) {
      return;
    }

    setIsRefreshing(true);

    try {
      // Attempt to refresh all quote data (this handles its own throttling)
      const refreshExecuted = refreshAllQuotes('manual');
      if (refreshExecuted) {
        // Allow enough time for data to load and show refresh indicator
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
    } catch (error) {
      console.error('Error refreshing watchlist:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshAllQuotes, canRefresh, isRefreshing, followedStocks.length]);

  // Get tutorial context directly
  const { startTutorial, isTutorialCompleted, activeTutorial, currentStep, setPreparingTutorial } =
    useTutorial();

  // Start blocking immediately if tutorial should run
  useEffect(() => {
    if (!isTutorialCompleted(WATCHLIST_TUTORIAL_ID) && !activeTutorial) {
      console.log('Watchlist tutorial should run - starting immediate blocking');
      setPreparingTutorial(true);
    }
  }, [isTutorialCompleted, activeTutorial, setPreparingTutorial]);

  // Set components as ready after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      setComponentsReady(true);
    }, 1000); // Increased delay to ensure components are fully rendered

    return () => clearTimeout(timer);
  }, []);

  // Tutorial steps
  const tutorialSteps = React.useMemo(() => {
    return [
      {
        targetRef: watchlistRef,
        title: t('tutorial.watchlist.watchlist.title'),
        description: t('tutorial.watchlist.watchlist.description'),
        position: 'top',
        padding: 15,
      },
      {
        targetRef: stockUsageButtonRef,
        title: t('tutorial.watchlist.stockUsage.title'),
        description: t('tutorial.watchlist.stockUsage.description'),
        position: 'top',
        padding: 100,
      },
      {
        targetRef: searchBarRef,
        title: t('tutorial.watchlist.search.title'),
        description: t('tutorial.watchlist.search.description'),
        position: 'bottom',
        padding: 15,
      },
      {
        targetRef: bottomSheetSearchInputRef,
        title: t('tutorial.watchlist.searchInput.title'),
        description: t('tutorial.watchlist.searchInput.description'),
        position: 'bottom',
        padding: 15,
        prepare: async () => {
          // Open the bottom sheet before trying to measure the search input
          return new Promise<void>(resolve => {
            // Present the bottom sheet
            bottomSheetModalRef.current?.present();

            // Poll until the input ref is available and rendered
            const checkInputReady = () => {
              if (bottomSheetSearchInputRef.current) {
                // Wait for bottom sheet animation to complete (typical animation is 300-500ms)
                // Use 500ms to be safe with slower devices
                setTimeout(resolve, 500);
              } else {
                setTimeout(checkInputReady, 100);
              }
            };

            // Start polling after a short delay to allow bottom sheet to start opening
            setTimeout(checkInputReady, 500);
          });
        },
      },
      {
        targetRef: bottomSheetSearchListRef,
        title: t('tutorial.watchlist.searchResults.title'),
        description: t('tutorial.watchlist.searchResults.description'),
        position: 'bottom',
        padding: 15,
        prepare: async () => {
          // Set search input to "A" and wait for results to load
          return new Promise<void>(resolve => {
            // Set search input to "A"
            setSearchQuery('A');

            // Poll until search results are available
            const checkResultsReady = () => {
              if (bottomSheetSearchListRef.current) {
                setTimeout(resolve, 100);
              } else {
                setTimeout(checkResultsReady, 100);
              }
            };

            // Start polling for results
            checkResultsReady();
          });
        },
      },
    ] as TutorialStep[];
  }, [t, stockUsageButtonRef]);

  // Initialize tutorial when components are ready
  useEffect(() => {
    if (
      componentsReady &&
      !isTutorialCompleted(WATCHLIST_TUTORIAL_ID) &&
      !activeTutorial &&
      stockUsageButtonRef?.current
    ) {
      // Components are ready, start the tutorial (blocking already started)
      const timer = setTimeout(() => {
        console.log('startTutorial');
        startTutorial(WATCHLIST_TUTORIAL_ID, tutorialSteps);
        // Allow interactions after tutorial starts
        setPreparingTutorial(false);
      }, 800);

      return () => {
        clearTimeout(timer);
        setPreparingTutorial(false);
      };
    }
  }, [
    componentsReady,
    isTutorialCompleted,
    activeTutorial,
    startTutorial,
    tutorialSteps,
    stockUsageButtonRef,
    setPreparingTutorial,
  ]);

  // For debugging purposes - log tutorial state changes
  useEffect(() => {
    if (activeTutorial === WATCHLIST_TUTORIAL_ID) {
      console.log(`Tutorial step: ${currentStep} of ${tutorialSteps.length}`);
    }
  }, [currentStep, activeTutorial, tutorialSteps.length]);

  // Handle tutorial completion or closure
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (!activeTutorial && bottomSheetModalRef.current) {
      // If tutorial is not active anymore, dismiss the bottom sheet
      // Add a small delay to avoid race conditions
      timer = setTimeout(() => {
        bottomSheetModalRef.current?.dismiss();
      }, 500);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [activeTutorial]);

  const screenHeight = Dimensions.get('window').height;
  const router = useRouter();

  // SnackBar
  const [snackBarMessage, setSnackBarMessage] = React.useState<string>('');
  const [snackBarVisible, setSnackBarVisible] = React.useState(false);
  const [snackBarAction, setSnackBarAction] = React.useState<'add' | 'remove'>('add');
  const onDismissSnackBar = () => setSnackBarVisible(false);

  const { data: searchResults, isLoading } = useCompanySearch(searchQuery);

  const [recentSearches, setRecentSearches] = useState<{ symbol: string; name: string }[]>([]);

  const { data: latestClosingPrices } = useLatestClosingPrices(
    followedStocks.map(stock => stock.symbol)
  );

  // 加载最近搜索记录
  useEffect(() => {
    loadRecentSearches();
  }, []);

  // 加载最近搜索记录
  const loadRecentSearches = async () => {
    try {
      const savedSearches = await AsyncStorage.getItem(RECENT_SEARCHES_KEY);
      if (savedSearches) {
        setRecentSearches(JSON.parse(savedSearches));
      }
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  // 添加到最近搜索
  const addToRecentSearches = async (stock: { symbol: string; name: string }) => {
    try {
      const updatedSearches = [
        stock,
        ...recentSearches.filter(item => item.symbol !== stock.symbol),
      ].slice(0, MAX_RECENT_SEARCHES);

      setRecentSearches(updatedSearches);
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error saving recent search:', error);
    }
  };

  // Handle latest closing prices
  useEffect(() => {
    if (latestClosingPrices?.success && latestClosingPrices.data) {
      const formattedPrices = latestClosingPrices.data.reduce(
        (acc, item) => {
          acc[item.symbol] = { price: item.price };
          return acc;
        },
        {} as { [symbol: string]: { price: number } }
      );
      updateLastClosePrices(formattedPrices);
    }
  }, [latestClosingPrices]);

  // Handle real-time stock subscription
  useEffect(() => {
    if (user?.id && followedStocks.length > 0) {
      subscribeToStocks(
        user.id,
        followedStocks.map(stock => stock.symbol)
      );
    }

    return () => {
      unsubscribeFromStocks();
    };
  }, [followedStocks, user?.id]);

  const showSnackbar = (message: string, action: 'add' | 'remove') => {
    setSnackBarVisible(false);
    setTimeout(() => {
      setSnackBarMessage(message);
      setSnackBarAction(action);
      setSnackBarVisible(true);
    }, 300);
  };

  const handleToggleFollow = async (stock: { symbol: string; name: string }) => {
    if (isFollowed(stock.symbol)) {
      await unfollowStock(stock.symbol);
      showSnackbar(t('stock.watchlist.list.messages.removed', { symbol: stock.symbol }), 'remove');
    } else {
      await followStock(stock);
      showSnackbar(t('stock.watchlist.list.messages.added', { symbol: stock.symbol }), 'add');
    }
  };

  const onChangeSearch = (query: string) => {
    setSearchQuery(query);
    setShowAll(false);
  };

  const handleStockPress = async (stock: { symbol: string; name: string }) => {
    await addToRecentSearches(stock);
    bottomSheetModalRef.current?.dismiss();
    router.push(`../stock/${stock.symbol}`);
  };

  const handleImageError = (symbol: string) => {
    setImageErrors(prevErrors => ({ ...prevErrors, [symbol]: true }));
  };

  const handlePresentModalPress = useCallback(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} />,
    []
  );

  const handleSheetChanges = useCallback((index: number) => {
    if (index === -1) {
      Keyboard.dismiss();
      setSearchQuery('');
    }
  }, []);

  const renderHandle = useCallback(
    (props: BottomSheetHandleProps) => (
      <BottomSheetHandle
        {...props}
        style={{
          backgroundColor: 'white',
          borderTopLeftRadius: 15,
          borderTopRightRadius: 15,
        }}
      />
    ),
    []
  );

  return (
    <SafeAreaView className="flex-1 gap-4" edges={['top']}>
      <View className="p-4 pb-0">
        <TouchableWithoutFeedback onPress={handlePresentModalPress}>
          <View
            ref={searchBarRef}
            className="rounded-full border-2 border-theme/50 bg-white p-3 shadow shadow-theme/20">
            <View className="flex-row items-center justify-between">
              <View className="flex-1 flex-row items-center">
                <FontAwesome name="search" size={16} color={COLORS.theme} />
                <Text className="ml-3 text-base text-gray-400">
                  {t('stock.watchlist.search.placeholder')}
                </Text>
              </View>
              <FontAwesome name="level-up" size={16} color={COLORS.gray} />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </View>
      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={0}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        handleComponent={renderHandle}
        enablePanDownToClose={false}
        enableDynamicSizing={false}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore">
        <BottomSheetView className="flex-1">
          <View className="pl-4 pr-4 pt-2">
            <View
              className="rounded-full border-2 border-theme/50 bg-white p-3 shadow shadow-theme/20"
              ref={bottomSheetSearchInputRef}>
              <View className="flex-row items-center justify-between">
                <View className="flex-1 flex-row items-center">
                  <FontAwesome name="search" size={16} color={COLORS.theme} />
                  <TextInput
                    placeholder={t('stock.watchlist.search.placeholder')}
                    placeholderTextColor="#9CA3AF"
                    value={searchQuery}
                    onChangeText={onChangeSearch}
                    style={{
                      marginLeft: 12,
                      fontSize: 16,
                      color: COLORS.theme,
                      flex: 1,
                      fontWeight: 'semibold',
                    }}
                  />
                </View>
                <FontAwesome name="level-up" size={16} color={COLORS.gray} />
              </View>
            </View>
          </View>
          <ScrollView
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={true}
            keyboardShouldPersistTaps="handled"
            nestedScrollEnabled={true}
            bounces={true}
            scrollEnabled={true}
            alwaysBounceVertical={false}
            onScrollBeginDrag={() => Keyboard.dismiss()}
            contentContainerStyle={{
              paddingBottom: 150,
              flexGrow: 0,
            }}>
            <View style={{ minHeight: 'auto' }}>
              {searchQuery ? (
                isLoading ? (
                  <View className="mt-8">
                    <ActivityIndicator size="large" />
                  </View>
                ) : searchResults ? (
                  <>
                    <View ref={bottomSheetSearchListRef}>
                      <Text className="p-4 pb-2 text-xl font-semibold">
                        {t('stock.watchlist.search.title')}
                      </Text>
                      <View>
                        {searchResults
                          .slice(0, showAll ? searchResults.length : 5)
                          .map((stock, index) => (
                            <SearchStockItem
                              key={stock.symbol}
                              stock={stock}
                              isFollowed={isFollowed(stock.symbol)}
                              onPress={handleStockPress}
                              onToggleFollow={handleToggleFollow}
                              imageErrors={imageErrors}
                              onImageError={handleImageError}
                            />
                          ))}
                        {searchResults.length > 5 && (
                          <>
                            {showAll ? (
                              <TouchableOpacity onPress={() => setShowAll(false)}>
                                <Text className="p-4 pt-0 font-semibold text-blue-500">
                                  {t('stock.watchlist.search.showLess')}
                                </Text>
                              </TouchableOpacity>
                            ) : (
                              <TouchableOpacity onPress={() => setShowAll(true)}>
                                <Text className="p-4 pt-0 font-semibold text-blue-500">
                                  {t('stock.watchlist.search.showMore')}
                                </Text>
                              </TouchableOpacity>
                            )}
                          </>
                        )}
                        {searchResults.length === 0 && (
                          <View>
                            <Text className="p-4 pt-2 text-lg">
                              {t('stock.watchlist.search.noResults')}
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </>
                ) : null
              ) : (
                <>
                  {recentSearches.length > 0 && (
                    <>
                      <Text className="p-4 pb-2 text-xl font-semibold">
                        {t('stock.watchlist.recentSearches.title')}
                      </Text>
                      {recentSearches.map((stock, index) => (
                        <SearchStockItem
                          key={stock.symbol}
                          stock={stock}
                          isFollowed={isFollowed(stock.symbol)}
                          onPress={handleStockPress}
                          onToggleFollow={handleToggleFollow}
                          imageErrors={imageErrors}
                          onImageError={handleImageError}
                        />
                      ))}
                    </>
                  )}
                  <Text className="p-4 pb-2 text-xl font-semibold">
                    {t('stock.watchlist.popular.title')}
                  </Text>
                  {stockData.map((stock, index) => (
                    <SearchStockItem
                      key={stock.symbol}
                      stock={stock}
                      isFollowed={isFollowed(stock.symbol)}
                      onPress={handleStockPress}
                      onToggleFollow={handleToggleFollow}
                      imageErrors={imageErrors}
                      onImageError={handleImageError}
                    />
                  ))}
                </>
              )}
            </View>
          </ScrollView>
          <Snackbar
            visible={snackBarVisible}
            onDismiss={onDismissSnackBar}
            action={{
              label: 'View',
              labelStyle: { color: '#FFFFFF' },
              onPress: () => {
                bottomSheetModalRef.current?.dismiss();
              },
            }}
            style={{
              backgroundColor: COLORS.theme,
              borderRadius: 8,
            }}
            wrapperStyle={{ bottom: screenHeight * 0.05 }}
            duration={3000}>
            <View className="flex-row items-center">
              <FontAwesome
                name={snackBarAction === 'add' ? 'check-circle' : 'minus-circle'}
                size={16}
                color="#FFFFFF"
                style={{ marginRight: 8 }}
              />
              <Text className="text-white">{snackBarMessage}</Text>
            </View>
          </Snackbar>
        </BottomSheetView>
      </BottomSheetModal>
      <View className="p-2 pb-0">
        <Text className="ml-2 text-2xl font-bold">{t('stock.watchlist.list.title')}</Text>
      </View>
      <View className="flex-1">
        <ScrollView
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[COLORS.theme]}
              tintColor={COLORS.theme}
            />
          }>
          <View ref={watchlistRef}>
            {followedStocks.length === 0 ? (
              <View className="flex-1 items-center justify-center px-6 py-8">
                <View className="mt-10 h-48 w-full">
                  <EmptyWatchlistSvg height={'100%'} width={'100%'} />
                </View>
                <Text className="mb-2 mt-8 text-center text-2xl font-bold text-gray-900">
                  {t('stock.watchlist.empty.title')}
                </Text>
                <Text className="mb-8 text-center text-base text-gray-600">
                  {t('stock.watchlist.empty.description')}
                </Text>
                <TouchableOpacity
                  onPress={handlePresentModalPress}
                  className="flex-row items-center justify-center rounded-lg bg-theme px-6 py-3">
                  <FontAwesome name="search" size={16} color="white" className="mr-2" />
                  <Text className="font-medium text-white">
                    {t('stock.watchlist.empty.searchButton')}
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              followedStocks.map((stock, index) => (
                <WatchListItem key={stock.symbol} stock={stock} index={index} />
              ))
            )}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}
