# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# env
.env
.env.local
.env.staging

# dependencies
node_modules/
package-lock.json
yarn.lock

# Expo
.expo/
dist/
web-build/
expo-env.d.ts
credentials.json
*.apk
*.aab

# Build
ios/
android/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

package-lock.json

# personal
.backup/