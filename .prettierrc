{"printWidth": 100, "tabWidth": 2, "singleQuote": true, "bracketSameLine": true, "trailingComma": "es5", "arrowParens": "avoid", "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^(react/(.*)$)|^(react$)", "^(react-native/(.*)$)|^(react-native$)", "^(expo/(.*)$)|^(expo$)", ">THIRD_PARTY_MODULES<", "^types$", "^@/hooks/(.*)$", "^@/service/(.*)$", "^@/type/(.*)$", "^@/constants/(.*)$", "^@/components/(.*)$", "^@/assets/(.*)$", "^@/app/(.*)$", "^@/styles/(.*)$", "^(?!.*[.]css$)[./].*$", ".css$"], "importOrderSeparation": true}