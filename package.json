{"name": "finsmarket-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "lint:fix": "expo lint --fix", "prepare": "husky", "format": "prettier --write ."}, "jest": {"preset": "jest-expo"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"], "*.{ts,tsx}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.0.6", "@gorhom/portal": "^1.0.14", "@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^5.64.2", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "expo": "~52.0.38", "expo-apple-authentication": "~7.1.3", "expo-application": "~6.0.2", "expo-auth-session": "~6.0.3", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.3", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.3", "expo-dev-client": "~5.0.14", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.2", "expo-haptics": "~14.0.0", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-mail-composer": "~14.0.2", "expo-notifications": "~0.29.14", "expo-random": "^14.0.1", "expo-router": "~4.0.19", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.18", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-updates": "~0.27.3", "expo-web-browser": "~14.0.1", "i18next": "^24.2.2", "lodash": "^4.17.21", "lottie-react-native": "^7.3.1", "nativewind": "^4.1.23", "postcss": "^8.4.49", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-native": "0.76.9", "react-native-animated-bottom-drawer": "^0.0.23", "react-native-bouncy-checkbox": "^4.1.2", "react-native-chart-kit": "^6.12.0", "react-native-collapsible": "^1.6.2", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.20.2", "react-native-keyboard-controller": "^1.18.2", "react-native-markdown-display": "^7.0.2", "react-native-modal": "^13.0.1", "react-native-pager-view": "6.5.1", "react-native-paper": "^5.13.1", "react-native-paper-tabs": "^0.11.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sse": "^1.2.1", "react-native-svg": "15.8.0", "react-native-tab-view": "^4.0.5", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@react-native-community/cli": "latest", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.14", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "eslint": "^8.57.1", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-config-universe": "^13.0.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~52.0.6", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "overrides": {"@expo/image-utils": "0.6.3"}, "private": true}