import { ApiResponseParams } from '@/types/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin, SignInResponse } from '@react-native-google-signin/google-signin';
import * as AppleAuthentication from 'expo-apple-authentication';

import { secureStore, SecureStoreKeys } from '../secure-store';
import { apiClient, handleApiError } from './client';
import { API_PATHS } from './paths';

type LoginResponse = ApiResponseParams<{
  accessToken: string;
}>;

export type LoginFormValues = {
  email: string;
  password: string;
};

export type SignUpFormValues = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
};

// Initialize Google Sign-In - Configuration: https://react-native-google-signin.github.io/docs/original
GoogleSignin.configure({
  iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID!,
  webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID!,
  offlineAccess: true,
  scopes: ['email', 'profile', 'openid'],
});

export const authService = {
  async login(credentials: LoginFormValues) {
    try {
      const { data } = await apiClient.post<LoginResponse>(API_PATHS.Auth.login, credentials);
      if (data.success && data.data?.accessToken) {
        await secureStore.set(SecureStoreKeys.authToken, data.data.accessToken);
        await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      }
      // Each request will have the token attached to it if it exists
      return data;
    } catch (error) {
      throw handleApiError(error);
    }
  },
  async signUp(credentials: SignUpFormValues) {
    try {
      const { data } = await apiClient.post(API_PATHS.Auth.register, credentials);
      if (data.success && data.data?.accessToken) {
        await secureStore.set(SecureStoreKeys.authToken, data.data.accessToken);
        await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      }
      return data;
    } catch (error) {
      throw handleApiError(error);
    }
  },
  async logout() {
    try {
      await apiClient.post(API_PATHS.Auth.logout);
      await secureStore.remove(SecureStoreKeys.authToken);
      // Sign out from Google
      await GoogleSignin.signOut();
    } catch (error) {
      throw handleApiError(error);
    }
  },
  async getCurrentUser() {
    try {
      const { data } = await apiClient.get(API_PATHS.Auth.profile);
      return data;
    } catch (error) {
      throw handleApiError(error);
    }
  },
  async signInWithGoogle() {
    try {
      // Check if Google Play Services are available for Android
      await GoogleSignin.hasPlayServices();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const _: SignInResponse = await GoogleSignin.signIn();

      // Get the ID token from Google
      const { idToken } = await GoogleSignin.getTokens();
      if (!idToken) {
        throw new Error('No ID token received from Google');
      }

      // Send the ID token to our server for authentication and sync with our database
      const { data } = await apiClient.post<LoginResponse>(API_PATHS.Auth.googleAuth, {
        idToken,
      });

      if (!data.success || !data.data?.accessToken) {
        throw new Error('Failed to authenticate with Google');
      }

      await secureStore.set(SecureStoreKeys.authToken, data.data.accessToken);
      await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      return { success: true, data: { accessToken: data.data.accessToken } };
    } catch (error) {
      throw handleApiError(error);
    }
  },
  async sendVerificationEmail() {
    try {
      const { data } = await apiClient.post(API_PATHS.Auth.verifyEmail);
      return data;
    } catch (error) {
      throw handleApiError(error);
    }
  },
  async signInWithApple() {
    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      // Send the identity token to your backend for verification
      const { data } = await apiClient.post<LoginResponse>(API_PATHS.Auth.appleAuth, {
        idToken: credential.identityToken,
        fullName: credential.fullName,
        email: credential.email,
        userIdentifier: credential.user,
      });

      if (data.success && data.data?.accessToken) {
        await secureStore.set(SecureStoreKeys.authToken, data.data.accessToken);
        await AsyncStorage.setItem('hasCompletedOnboarding', 'true');
      }

      return data;
    } catch (error: any) {
      if (error?.code === 'ERR_REQUEST_CANCELED') {
        // Handle user cancellation
        throw new Error('Sign in was canceled');
      }
      throw handleApiError(error);
    }
  },
};
