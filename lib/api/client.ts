import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

import { secureStore, SecureStoreKeys } from '../secure-store';

const SHOW_LOGS = false;

const API_URL = process.env.EXPO_PUBLIC_API_URL || 'https://usai.zentoai.com/api';

// Create axios instance with default config
export const apiClient = axios.create({
  baseURL: API_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-Platform': 'mobile',
  },
});

// Request interceptor
// Each request will have the token attached to it if it exists
apiClient.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const token = await secureStore.get(SecureStoreKeys.authToken);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  response => {
    if (process.env.NODE_ENV === 'development' && SHOW_LOGS) {
      logResponse(response);
    }
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Remove token on 401 errors
      await secureStore.remove(SecureStoreKeys.authToken);
      // You can add logic here to redirect to login or refresh token
    }

    return Promise.reject(error);
  }
);

// API error types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  translationKey?: string;
}

// Helper function to handle API errors
export const handleApiError = (error: unknown): ApiError => {
  if (process.env.NODE_ENV === 'development') {
    logError(error);
  }
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<{
      message?: string;
      code?: string;
      translationKey?: string;
    }>;
    return {
      message: axiosError.response?.data?.message || 'An unexpected error occurred',
      code: axiosError.response?.data?.code,
      status: axiosError.response?.status,
      translationKey: axiosError.response?.data?.translationKey,
    };
  }

  const message = (error as any)?.message || 'An unexpected error occurred';
  return message;
};

const logResponse = (response: AxiosResponse) => {
  const { config, status, data } = response;
  const fullUrl = `${config.baseURL || ''}${config.url}`;

  console.log('\n🔍 API Response Log:');
  console.log('📍 URL:', fullUrl);
  console.log('📊 Status:', status);
  console.log('📦 Response Data:', 'uncomment this to see the response data');
  console.log('📦 Response Data:', JSON.stringify(data, null, 2));
  console.log('\n------------------------\n');
};

const logError = (error: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('\n❌ API Error Log:');

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const { config, status, data } = error.response;
      const fullUrl = `${config.baseURL || ''}${config.url}`;

      console.log('📍 URL:', fullUrl);
      console.log('📊 Status:', status);
      console.log('📦 Response Data:', JSON.stringify(data, null, 2));
      console.log(
        '🔄 Request Data:',
        config.data ? JSON.stringify(JSON.parse(config.data), null, 2) : 'No request data'
      );
    } else if (error.request) {
      // The request was made but no response was received
      console.log('📍 URL:', error.request._currentUrl);
      console.log('⚠️ No response received from server');
      console.log(
        '🔄 Request Data:',
        error.config?.data
          ? JSON.stringify(JSON.parse(error.config.data), null, 2)
          : 'No request data'
      );
    } else {
      // Something happened in setting up the request that triggered an Error
      console.log('🚫 Error Message:', error.message);
    }

    // if (error.stack) {
    //   console.log('📚 Stack Trace:');
    //   console.log(error.stack);
    // }

    console.log('------------------------\n');
  }
};
