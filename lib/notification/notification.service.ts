import AsyncStorage from '@react-native-async-storage/async-storage';

import { Platform } from 'react-native';

const SHOW_LOGS = false;

// Notification storage keys
const NOTIFICATION_KEYS = {
  weeklyReportNotification: 'weekly_report_notification_id',
  potentialStocksNotification: 'potential_stocks_notification_id',
  preferredStocksNotification: 'preferred_stocks_notification_id',
  testNotification: 'test_notification_id',
  notificationLanguage: 'notification_language',
  debugMode: 'notification_debug_mode',
  badgeCount: 'notification_badge_count',
  readNotifications: 'read_notifications', // Array of read notification IDs
};

// Notification types and their configurations
const NOTIFICATION_TYPES = {
  weekly_report: {
    key: NOTIFICATION_KEYS.weeklyReportNotification,
    type: 'weekly_report',
    targetDay: 6, // Saturday (0 = Sunday, 6 = Saturday)
    targetHour: 10,
    targetMinute: 0,
  },
  potential_stocks: {
    key: NOTIFICATION_KEYS.potentialStocksNotification,
    type: 'potential_stocks',
    targetDay: 2, // Tuesday (0 = Sunday, 2 = Tuesday)
    targetHour: 14,
    targetMinute: 30,
  },
  preferred_stocks: {
    key: NOTIFICATION_KEYS.preferredStocksNotification,
    type: 'preferred_stocks',
    targetDay: 4, // Thursday (0 = Sunday, 4 = Thursday)
    targetHour: 14,
    targetMinute: 30,
  },
} as const;

// Safely import notifications module
let Notifications: any = null;
try {
  Notifications = require('expo-notifications');
} catch (error) {
  console.log('expo-notifications module not available:', error);
}

class NotificationService {
  private static instance: NotificationService;
  private debugMode: boolean = false;

  private constructor() {
    // Initialize notifications when service is created
    if (Notifications) {
      this.initializeNotifications();

      // Check debug mode setting
      AsyncStorage.getItem(NOTIFICATION_KEYS.debugMode).then(value => {
        this.debugMode = value === 'true';
      });
    }
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Initialize notifications - request permissions
  private async initializeNotifications() {
    if (!Notifications) return false;

    try {
      // Set up the notification handler - this handles how notifications are displayed
      Notifications.setNotificationHandler({
        handleNotification: async (notification: any) => {
          // Always handle badge updates for background notifications
          try {
            const badgeCount = notification.request.content.data?.badgeCount;
            if (badgeCount && typeof badgeCount === 'number') {
              // Update system badge immediately for background notifications
              await Notifications.setBadgeCountAsync(badgeCount);
              if (SHOW_LOGS) {
                console.log(
                  `[Badge] Updated system badge to ${badgeCount} for background notification`
                );
              }
            }
          } catch (error) {
            console.error('[Badge] Error updating badge in notification handler:', error);
          }

          return {
            shouldShowAlert: true,
            shouldPlaySound: true,
            shouldSetBadge: true, // Enable badge notifications
          };
        },
      });

      // Request permissions (if not already granted)
      const { status: existingStatus } = await Notifications.getPermissionsAsync();

      let finalStatus = existingStatus;
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        this.logDebug('Permission for notifications was denied');
        return false;
      }

      // Initialize badge count
      await this.initializeBadgeCount();

      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return false;
    }
  }

  // Initialize badge count on app start
  private async initializeBadgeCount() {
    try {
      // Get current badge count from storage
      const storedBadgeCount = await AsyncStorage.getItem(NOTIFICATION_KEYS.badgeCount);
      const badgeCount = storedBadgeCount ? parseInt(storedBadgeCount, 10) : 0;

      // Set the badge count on app start
      await Notifications.setBadgeCountAsync(badgeCount);
      if (SHOW_LOGS) {
        console.log(`[Badge] Initialized badge count: ${badgeCount}`);
      }
    } catch (error) {
      console.error('Error initializing badge count:', error);
    }
  }

  // Increment badge count
  public async incrementBadgeCount(): Promise<void> {
    try {
      const currentCount = await this.getBadgeCount();
      const newCount = currentCount + 1;

      await Notifications.setBadgeCountAsync(newCount);
      await AsyncStorage.setItem(NOTIFICATION_KEYS.badgeCount, newCount.toString());

      if (SHOW_LOGS) {
        console.log(`[Badge] Incremented badge count to: ${newCount}`);
      }
    } catch (error) {
      console.error('Error incrementing badge count:', error);
    }
  }

  // Decrement badge count
  public async decrementBadgeCount(): Promise<void> {
    try {
      const currentCount = await this.getBadgeCount();
      const newCount = Math.max(0, currentCount - 1); // Don't go below 0

      await Notifications.setBadgeCountAsync(newCount);
      await AsyncStorage.setItem(NOTIFICATION_KEYS.badgeCount, newCount.toString());

      if (SHOW_LOGS) {
        console.log(`[Badge] Decremented badge count to: ${newCount}`);
      }
    } catch (error) {
      console.error('Error decrementing badge count:', error);
    }
  }

  // Get current badge count
  public async getBadgeCount(): Promise<number> {
    try {
      const storedBadgeCount = await AsyncStorage.getItem(NOTIFICATION_KEYS.badgeCount);
      return storedBadgeCount ? parseInt(storedBadgeCount, 10) : 0;
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  }

  // Clear badge count (when user opens app or marks all as read)
  public async clearBadgeCount(): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(0);
      await AsyncStorage.setItem(NOTIFICATION_KEYS.badgeCount, '0');

      if (SHOW_LOGS) {
        console.log('[Badge] Cleared badge count');
      }
    } catch (error) {
      console.error('Error clearing badge count:', error);
    }
  }

  // Mark notification as read
  public async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      const readNotificationsStr = await AsyncStorage.getItem(NOTIFICATION_KEYS.readNotifications);
      const readNotifications = readNotificationsStr ? JSON.parse(readNotificationsStr) : [];

      if (!readNotifications.includes(notificationId)) {
        readNotifications.push(notificationId);
        await AsyncStorage.setItem(
          NOTIFICATION_KEYS.readNotifications,
          JSON.stringify(readNotifications)
        );

        // Decrement badge count
        await this.decrementBadgeCount();

        if (SHOW_LOGS) {
          console.log(`[Badge] Marked notification ${notificationId} as read`);
        }
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // Check if notification is read
  public async isNotificationRead(notificationId: string): Promise<boolean> {
    try {
      const readNotificationsStr = await AsyncStorage.getItem(NOTIFICATION_KEYS.readNotifications);
      const readNotifications = readNotificationsStr ? JSON.parse(readNotificationsStr) : [];

      return readNotifications.includes(notificationId);
    } catch (error) {
      console.error('Error checking if notification is read:', error);
      return false;
    }
  }

  // Get unread notification count
  public async getUnreadNotificationCount(): Promise<number> {
    try {
      if (!Notifications) return 0;

      const deliveredNotifications = await Notifications.getPresentedNotificationsAsync();
      const readNotificationsStr = await AsyncStorage.getItem(NOTIFICATION_KEYS.readNotifications);
      const readNotifications = readNotificationsStr ? JSON.parse(readNotificationsStr) : [];

      const unreadCount = deliveredNotifications.filter(
        (notification: any) => !readNotifications.includes(notification.request.identifier)
      ).length;

      return unreadCount;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      return 0;
    }
  }

  // Core method to schedule a recurring weekly notification
  private async scheduleWeeklyNotification(
    notificationType: keyof typeof NOTIFICATION_TYPES,
    title: string,
    body: string,
    currentLanguage?: string
  ): Promise<string | null> {
    if (!Notifications) {
      this.logDebug('Notifications module not available');
      return null;
    }

    const config = NOTIFICATION_TYPES[notificationType];

    try {
      // Request permissions first
      const permissionResult = await this.requestPermissions();
      if (!permissionResult) {
        this.logDebug('Notification permissions not granted');
        return null;
      }

      // Check if notification already exists (skip in debug mode)
      const existingId = await AsyncStorage.getItem(config.key);
      const storedLanguage = await AsyncStorage.getItem(NOTIFICATION_KEYS.notificationLanguage);

      // Check if there are already notifications of this type in the system
      let hasExistingNotification = false;
      if (!this.debugMode && Notifications) {
        try {
          const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
          hasExistingNotification = scheduledNotifications.some(
            (n: any) => n.content?.data?.type === config.type
          );
        } catch (error) {
          console.error('Error checking existing notifications:', error);
        }
      }

      // FORCE RESCHEDULE: Always cancel and reschedule to ensure correct timing
      // This fixes the issue where old notifications had wrong trigger format (604800 seconds)
      if (existingId || hasExistingNotification) {
        this.logDebug(
          `Existing ${config.type} notification found - cancelling to reschedule with correct timing`
        );
        await this.cancelNotificationByType(notificationType);
      }

      // Set up Android notification channel
      if (Platform.OS === 'android') {
        await this.setupNotificationChannels();
      }

      // Get current badge count and increment for this new notification
      const currentBadgeCount = await this.getBadgeCount();
      const newBadgeCount = currentBadgeCount + 1;

      // Schedule notification
      let notificationId: string;

      if (this.debugMode) {
        // Debug mode - immediate notification for testing
        notificationId = await Notifications.scheduleNotificationAsync({
          content: {
            title,
            body,
            badge: newBadgeCount, // Include badge count in notification payload
            data: {
              type: config.type,
              badgeCount: newBadgeCount, // Store badge count in data for background handling
            },
            ...(Platform.OS === 'android' ? { channelId: 'reports' } : {}),
          },
          trigger: null, // Immediate notification for testing
        });

        // Pre-increment stored badge count for immediate test notifications
        await AsyncStorage.setItem(NOTIFICATION_KEYS.badgeCount, newBadgeCount.toString());

        this.logDebug(
          `Immediate ${config.type} test notification scheduled with badge ${newBadgeCount}. ID: ${notificationId}`
        );
      } else {
        // Production mode - calculate time until specific target day/time
        const nextTargetTime = this.getNextTargetTime(
          config.targetDay,
          config.targetHour,
          config.targetMinute
        );
        const secondsUntilTarget = Math.floor((nextTargetTime.getTime() - Date.now()) / 1000);

        this.logDebug(`Next ${config.type} notification: ${nextTargetTime.toLocaleString()}`);
        this.logDebug(`Seconds until target: ${secondsUntilTarget}`);

        notificationId = await Notifications.scheduleNotificationAsync({
          content: {
            title,
            body,
            badge: newBadgeCount, // Include badge count in notification payload
            data: {
              type: config.type,
              badgeCount: newBadgeCount, // Store badge count in data for background handling
            },
            ...(Platform.OS === 'android' ? { channelId: 'reports' } : {}),
          },
          trigger: {
            type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
            seconds: Math.max(secondsUntilTarget, 10), // Time until target day/time (minimum 10 seconds)
            repeats: false, // Single notification - let the app handle weekly rescheduling
          },
        });

        // Pre-increment stored badge count for scheduled notifications
        await AsyncStorage.setItem(NOTIFICATION_KEYS.badgeCount, newBadgeCount.toString());

        this.logDebug(
          `${config.type} notification scheduled for ${nextTargetTime.toLocaleString()} with badge ${newBadgeCount}. ID: ${notificationId}`
        );
      }

      // Save the notification ID and language
      await AsyncStorage.setItem(config.key, notificationId);
      if (currentLanguage) {
        await AsyncStorage.setItem(NOTIFICATION_KEYS.notificationLanguage, currentLanguage);
      }

      if (SHOW_LOGS) {
        console.log(
          `[Notification] Successfully scheduled ${config.type} with badge count ${newBadgeCount}, ID: ${notificationId}`
        );
      }
      return notificationId;
    } catch (error) {
      console.error(`Error scheduling ${config.type} notification:`, error);
      return null;
    }
  }

  // Calculate the next target time for a given day/hour/minute
  private getNextTargetTime(targetDay: number, targetHour: number, targetMinute: number): Date {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate days until target day
    let daysUntilTarget = targetDay - currentDay;

    // If target day is today, check if target time has passed
    if (daysUntilTarget === 0) {
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      // If target time has passed today, schedule for next week
      if (
        currentHour > targetHour ||
        (currentHour === targetHour && currentMinute >= targetMinute)
      ) {
        daysUntilTarget = 7;
      }
    }

    // If target day is in the past this week, schedule for next week
    if (daysUntilTarget < 0) {
      daysUntilTarget += 7;
    }

    // Create target date
    const targetDate = new Date();
    targetDate.setDate(now.getDate() + daysUntilTarget);
    targetDate.setHours(targetHour, targetMinute, 0, 0);

    return targetDate;
  }

  // Cancel notification by type
  private async cancelNotificationByType(
    notificationType: keyof typeof NOTIFICATION_TYPES
  ): Promise<boolean> {
    if (!Notifications) return false;

    const config = NOTIFICATION_TYPES[notificationType];

    try {
      const notificationId = await AsyncStorage.getItem(config.key);

      if (notificationId) {
        await Notifications.cancelScheduledNotificationAsync(notificationId);
        await AsyncStorage.removeItem(config.key);
        this.logDebug(`Cancelled ${config.type} notification with ID: ${notificationId}`);
      }

      return true;
    } catch (error) {
      console.error(`Error canceling ${config.type} notification:`, error);
      return false;
    }
  }

  // Public methods for each notification type
  public async scheduleWeeklyReportNotification(
    title: string,
    body: string,
    currentLanguage?: string
  ): Promise<string | null> {
    return this.scheduleWeeklyNotification('weekly_report', title, body, currentLanguage);
  }

  public async schedulePotentialStocksNotification(
    title: string,
    body: string,
    currentLanguage?: string
  ): Promise<string | null> {
    return this.scheduleWeeklyNotification('potential_stocks', title, body, currentLanguage);
  }

  public async schedulePreferredStocksNotification(
    title: string,
    body: string,
    currentLanguage?: string
  ): Promise<string | null> {
    return this.scheduleWeeklyNotification('preferred_stocks', title, body, currentLanguage);
  }

  // Cancel methods for each type
  public async cancelReportNotification(): Promise<boolean> {
    return this.cancelNotificationByType('weekly_report');
  }

  public async cancelPotentialStocksNotification(): Promise<boolean> {
    return this.cancelNotificationByType('potential_stocks');
  }

  public async cancelPreferredStocksNotification(): Promise<boolean> {
    return this.cancelNotificationByType('preferred_stocks');
  }

  // Request notification permissions explicitly
  public async requestPermissions(): Promise<boolean> {
    if (!Notifications) return false;

    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();

      if (existingStatus !== 'granted') {
        this.logDebug('Requesting notification permissions...');
        const { status } = await Notifications.requestPermissionsAsync();
        if (status !== 'granted') {
          this.logDebug('Permission denied');
          return false;
        }
        this.logDebug('Permission granted');
      } else {
        this.logDebug('Permission already granted');
      }

      return true;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }

  // Check if notification permissions are granted
  public async areNotificationsEnabled(): Promise<boolean> {
    if (!Notifications) return false;

    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }

  // Set up notification channels for Android
  public async setupNotificationChannels() {
    if (!Notifications || Platform.OS !== 'android') return;

    try {
      await Notifications.setNotificationChannelAsync('reports', {
        name: 'Report Notifications',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF6B00',
      });
      this.logDebug('Android notification channel set up');
    } catch (error) {
      console.error('Error setting up notification channels:', error);
    }
  }

  // Log only in debug mode
  private logDebug(message: string): void {
    if (this.debugMode) {
      console.log(`[Notification] ${message}`);
    }
  }

  // Log for test operations - always logs regardless of debug mode
  private logTest(message: string): void {
    console.log(`[Notification Test] ${message}`);
  }

  // Schedule report notification - alias for scheduleWeeklyReportNotification for backward compatibility
  public async scheduleReportNotification(
    title: string,
    body: string,
    currentLanguage?: string
  ): Promise<string | null> {
    return this.scheduleWeeklyReportNotification(title, body, currentLanguage);
  }

  // Schedule all report-related notifications
  public async scheduleAllReportNotifications(
    reportTitle: string,
    reportBody: string,
    potentialTitle: string,
    potentialBody: string,
    preferredTitle: string,
    preferredBody: string,
    currentLanguage?: string
  ): Promise<boolean> {
    try {
      if (SHOW_LOGS) {
        console.log(
          `[Notification] Scheduling all report notifications with language: ${currentLanguage || 'default'}`
        );
      }
      if (SHOW_LOGS) {
        console.log(
          `[Notification] Notification titles: Weekly="${reportTitle}", Potential="${potentialTitle}", Preferred="${preferredTitle}"`
        );
      }

      // Reset badge count when scheduling fresh notifications
      await this.clearBadgeCount();
      if (SHOW_LOGS) {
        console.log('[Badge] Reset badge count before scheduling new notifications');
      }

      const weeklyId = await this.scheduleReportNotification(
        reportTitle,
        reportBody,
        currentLanguage
      );
      const potentialId = await this.schedulePotentialStocksNotification(
        potentialTitle,
        potentialBody,
        currentLanguage
      );
      const preferredId = await this.schedulePreferredStocksNotification(
        preferredTitle,
        preferredBody,
        currentLanguage
      );

      const successCount = [weeklyId, potentialId, preferredId].filter(id => id !== null).length;
      if (SHOW_LOGS) {
        console.log(
          `[Notification] Successfully scheduled ${successCount}/3 notifications with language: ${currentLanguage || 'default'}`
        );
      }

      return successCount === 3;
    } catch (error) {
      console.error('Error scheduling all notifications:', error);
      return false;
    }
  }

  // Cancel all report-related notifications
  public async cancelAllReportNotifications(): Promise<boolean> {
    if (!Notifications) return false;

    try {
      if (SHOW_LOGS) {
        console.log('[Notification] Cancelling all report notifications...');
      }

      // Get all scheduled notifications first
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const ourNotificationTypes = ['weekly_report', 'potential_stocks', 'preferred_stocks'];

      if (SHOW_LOGS) {
        console.log(
          `[Notification] Found ${scheduledNotifications.length} total scheduled notifications`
        );
      }

      // Method 1: Cancel ALL notifications of our types from the system (most reliable)
      let canceledCount = 0;
      const canceledIds: string[] = [];

      for (const notification of scheduledNotifications) {
        const notificationType = notification.content?.data?.type;
        if (ourNotificationTypes.includes(notificationType)) {
          try {
            await Notifications.cancelScheduledNotificationAsync(notification.identifier);
            if (SHOW_LOGS) {
              console.log(
                `[Notification] Cancelled ${notificationType} notification: ${notification.identifier}`
              );
            }
            canceledIds.push(notification.identifier);
            canceledCount++;
          } catch (error) {
            console.error(
              `[Notification] Failed to cancel notification ${notification.identifier}:`,
              error
            );
          }
        }
      }

      if (SHOW_LOGS) {
        console.log(`[Notification] Cancelled ${canceledCount} notifications from system`);
      }

      // Method 2: Also cancel using stored IDs (for any edge cases)
      // But only if they weren't already canceled above
      const storedIds = [
        await AsyncStorage.getItem(NOTIFICATION_KEYS.weeklyReportNotification),
        await AsyncStorage.getItem(NOTIFICATION_KEYS.potentialStocksNotification),
        await AsyncStorage.getItem(NOTIFICATION_KEYS.preferredStocksNotification),
      ].filter(id => id !== null) as string[];

      for (const storedId of storedIds) {
        if (!canceledIds.includes(storedId)) {
          try {
            await Notifications.cancelScheduledNotificationAsync(storedId);
            if (SHOW_LOGS) {
              console.log(`[Notification] Cancelled stored notification: ${storedId}`);
            }
            canceledCount++;
          } catch (error) {
            // This is expected if the notification was already canceled or doesn't exist
            if (SHOW_LOGS) {
              console.log(
                `[Notification] Stored notification ${storedId} was already canceled or doesn't exist`
              );
            }
          }
        }
      }

      // Method 3: Clear all stored IDs to ensure clean state
      await Promise.all([
        AsyncStorage.removeItem(NOTIFICATION_KEYS.weeklyReportNotification),
        AsyncStorage.removeItem(NOTIFICATION_KEYS.potentialStocksNotification),
        AsyncStorage.removeItem(NOTIFICATION_KEYS.preferredStocksNotification),
        AsyncStorage.removeItem(NOTIFICATION_KEYS.notificationLanguage),
        AsyncStorage.removeItem(NOTIFICATION_KEYS.readNotifications), // Clear read notifications
      ]);

      // Clear badge count since we're canceling all notifications
      await this.clearBadgeCount();

      if (SHOW_LOGS) {
        console.log(`[Notification] Total notifications canceled: ${canceledCount}`);
        console.log('[Notification] Cleared all stored notification IDs, language, and badge data');
      }

      // Verify cleanup by checking remaining notifications
      const remainingNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const remainingOurNotifications = remainingNotifications.filter((n: any) =>
        ourNotificationTypes.includes(n.content?.data?.type)
      );

      if (remainingOurNotifications.length > 0) {
        if (SHOW_LOGS) {
          console.warn(
            `[Notification] Warning: ${remainingOurNotifications.length} of our notifications still remain after cleanup`
          );
        }
        remainingOurNotifications.forEach((n: any) => {
          if (SHOW_LOGS) {
            console.warn(`[Notification] Remaining: ${n.content?.data?.type} - ${n.identifier}`);
          }
        });
      } else {
        if (SHOW_LOGS) {
          console.log('[Notification] Cleanup verified: No remaining notifications of our types');
        }
      }

      return true;
    } catch (error) {
      console.error('Error canceling all notifications:', error);
      return false;
    }
  }

  // Test function - trigger immediate notifications for all types
  public async testAllNotifications(): Promise<boolean> {
    try {
      // Store original debug mode state
      const originalDebugMode = this.debugMode;

      // Force debug mode on to ensure immediate notifications
      this.debugMode = true;
      await AsyncStorage.setItem(NOTIFICATION_KEYS.debugMode, 'true');

      this.logTest('Testing all notifications...');

      // Force cancel all existing notifications first
      this.logTest('Cancelling any existing notifications...');
      await this.cancelAllReportNotifications();

      this.logTest('Scheduling test notifications...');

      // Schedule each notification with test messages
      const weeklyId = await this.scheduleWeeklyReportNotification(
        'TEST: Weekly Report',
        'This is a test of the weekly report notification'
      );

      const potentialId = await this.schedulePotentialStocksNotification(
        'TEST: Potential Stocks',
        'This is a test of the potential stocks notification'
      );

      const preferredId = await this.schedulePreferredStocksNotification(
        'TEST: Preferred Stocks',
        'This is a test of the preferred stocks notification'
      );

      const successCount = [weeklyId, potentialId, preferredId].filter(id => id !== null).length;
      this.logTest(`Successfully scheduled ${successCount}/3 test notifications`);

      // Properly restore debug mode state
      this.debugMode = originalDebugMode;
      await AsyncStorage.setItem(NOTIFICATION_KEYS.debugMode, originalDebugMode ? 'true' : 'false');
      this.logTest(`Debug mode restored to: ${this.debugMode}`);

      return successCount === 3;
    } catch (error) {
      this.logTest(`Error testing notifications: ${error}`);
      return false;
    }
  }

  // Check if all notification types are currently scheduled
  public async areAllNotificationsScheduled(): Promise<{
    weekly: boolean;
    potential: boolean;
    preferred: boolean;
    allScheduled: boolean;
    languageMatch: boolean;
    storedLanguage: string | null;
  }> {
    try {
      const weeklyId = await AsyncStorage.getItem(NOTIFICATION_KEYS.weeklyReportNotification);
      const potentialId = await AsyncStorage.getItem(NOTIFICATION_KEYS.potentialStocksNotification);
      const preferredId = await AsyncStorage.getItem(NOTIFICATION_KEYS.preferredStocksNotification);

      // Also verify that these notifications actually exist in the system
      let weeklyExists = false;
      let potentialExists = false;
      let preferredExists = false;

      if (Notifications) {
        try {
          const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();

          if (weeklyId) {
            weeklyExists = scheduledNotifications.some((n: any) => n.identifier === weeklyId);
          }
          if (potentialId) {
            potentialExists = scheduledNotifications.some((n: any) => n.identifier === potentialId);
          }
          if (preferredId) {
            preferredExists = scheduledNotifications.some((n: any) => n.identifier === preferredId);
          }

          this.logDebug(`Scheduled notifications count: ${scheduledNotifications.length}`);
          this.logDebug(`Weekly notification exists in system: ${weeklyExists}`);
          this.logDebug(`Potential notification exists in system: ${potentialExists}`);
          this.logDebug(`Preferred notification exists in system: ${preferredExists}`);
        } catch (error) {
          console.error('Error checking scheduled notifications:', error);
          // Fallback to just checking stored IDs if system check fails
          weeklyExists = !!weeklyId;
          potentialExists = !!potentialId;
          preferredExists = !!preferredId;
        }
      } else {
        // Fallback to just checking stored IDs if Notifications module not available
        weeklyExists = !!weeklyId;
        potentialExists = !!potentialId;
        preferredExists = !!preferredId;
      }

      const result = {
        weekly: weeklyExists,
        potential: potentialExists,
        preferred: preferredExists,
        allScheduled: false,
        languageMatch: false,
        storedLanguage: null as string | null,
      };

      result.allScheduled = result.weekly && result.potential && result.preferred;

      this.logDebug(
        `Notification status check - Weekly: ${result.weekly}, Potential: ${result.potential}, Preferred: ${result.preferred}`
      );

      return result;
    } catch (error) {
      console.error('Error checking notification status:', error);
      return {
        weekly: false,
        potential: false,
        preferred: false,
        allScheduled: false,
        languageMatch: false,
        storedLanguage: null,
      };
    }
  }

  // Check if the stored notification language matches the current app language
  public async checkNotificationLanguage(currentLanguage: string): Promise<{
    languageMatch: boolean;
    storedLanguage: string | null;
    needsUpdate: boolean;
  }> {
    try {
      const storedLanguage = await AsyncStorage.getItem(NOTIFICATION_KEYS.notificationLanguage);

      // If no language is stored, we need to update to set the current language
      if (storedLanguage === null) {
        this.logDebug(
          `Language check - Current: ${currentLanguage}, Stored: None, Need to set language`
        );
        return {
          languageMatch: false,
          storedLanguage: null,
          needsUpdate: true, // Changed: Always update when no language is stored
        };
      }

      const languageMatch = storedLanguage === currentLanguage;
      const needsUpdate = !languageMatch; // Changed: Simplified logic

      this.logDebug(
        `Language check - Current: ${currentLanguage}, Stored: ${storedLanguage}, Match: ${languageMatch}, NeedsUpdate: ${needsUpdate}`
      );

      return {
        languageMatch,
        storedLanguage,
        needsUpdate,
      };
    } catch (error) {
      console.error('Error checking notification language:', error);
      return {
        languageMatch: false,
        storedLanguage: null,
        needsUpdate: true, // Changed: Default to needing update on error
      };
    }
  }

  // Update notification language and reschedule if needed
  public async updateNotificationLanguage(
    currentLanguage: string,
    reportTitle: string,
    reportBody: string,
    potentialTitle: string,
    potentialBody: string,
    preferredTitle: string,
    preferredBody: string
  ): Promise<boolean> {
    try {
      if (SHOW_LOGS) {
        console.log(`[Notification] Updating notification language to: ${currentLanguage}`);
      }

      // Cancel existing notifications
      await this.cancelAllReportNotifications();

      // Reschedule with new language
      const success = await this.scheduleAllReportNotifications(
        reportTitle,
        reportBody,
        potentialTitle,
        potentialBody,
        preferredTitle,
        preferredBody,
        currentLanguage
      );

      if (success) {
        if (SHOW_LOGS) {
          console.log(
            `[Notification] Successfully updated notifications to language: ${currentLanguage}`
          );
        }
      } else {
        if (SHOW_LOGS) {
          console.error(
            `[Notification] Failed to update notifications to language: ${currentLanguage}`
          );
        }
      }

      return success;
    } catch (error) {
      console.error('Error updating notification language:', error);
      return false;
    }
  }

  // Debug method to list all scheduled notifications
  public async listAllScheduledNotifications(): Promise<void> {
    if (!Notifications) {
      console.log('[Notification Debug] Notifications module not available');
      return;
    }

    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      if (SHOW_LOGS) {
        console.log(
          `[Notification Debug] Total scheduled notifications: ${scheduledNotifications.length}`
        );
      }

      scheduledNotifications.forEach((notification: any, index: number) => {
        if (SHOW_LOGS) {
          console.log(`[Notification Debug] ${index + 1}. ID: ${notification.identifier}`);
          console.log(`[Notification Debug]    Title: ${notification.content?.title || 'N/A'}`);
          console.log(`[Notification Debug]    Type: ${notification.content?.data?.type || 'N/A'}`);
          console.log(`[Notification Debug]    Trigger: ${JSON.stringify(notification.trigger)}`);
          console.log('---');
        }
      });

      // Also show stored notification IDs
      const weeklyId = await AsyncStorage.getItem(NOTIFICATION_KEYS.weeklyReportNotification);
      const potentialId = await AsyncStorage.getItem(NOTIFICATION_KEYS.potentialStocksNotification);
      const preferredId = await AsyncStorage.getItem(NOTIFICATION_KEYS.preferredStocksNotification);

      if (SHOW_LOGS) {
        console.log('[Notification Debug] Stored notification IDs:');
        console.log(`[Notification Debug] Weekly: ${weeklyId || 'Not set'}`);
        console.log(`[Notification Debug] Potential: ${potentialId || 'Not set'}`);
        console.log(`[Notification Debug] Preferred: ${preferredId || 'Not set'}`);
      }
    } catch (error) {
      console.error('[Notification Debug] Error listing scheduled notifications:', error);
    }
  }

  // Debug method to cancel ALL scheduled notifications (for debugging)
  public async cancelAllScheduledNotifications(): Promise<boolean> {
    if (!Notifications) return false;

    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      if (SHOW_LOGS) {
        console.log('[Notification Debug] Cancelled ALL scheduled notifications');
      }

      // Also clear our stored IDs
      await AsyncStorage.removeItem(NOTIFICATION_KEYS.weeklyReportNotification);
      await AsyncStorage.removeItem(NOTIFICATION_KEYS.potentialStocksNotification);
      await AsyncStorage.removeItem(NOTIFICATION_KEYS.preferredStocksNotification);
      await AsyncStorage.removeItem(NOTIFICATION_KEYS.notificationLanguage);

      return true;
    } catch (error) {
      console.error('[Notification Debug] Error cancelling all notifications:', error);
      return false;
    }
  }
}

export default NotificationService.getInstance();
