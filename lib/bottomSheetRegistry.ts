import { BottomSheetModal } from '@gorhom/bottom-sheet';

// Simple global registry to store bottom sheet refs
const bottomSheetRefs: React.RefObject<BottomSheetModal>[] = [];

export const bottomSheetRegistry = {
  // Register a bottom sheet ref
  register: (ref: React.RefObject<BottomSheetModal>) => {
    if (!bottomSheetRefs.includes(ref)) {
      bottomSheetRefs.push(ref);
    }
  },

  // Unregister a bottom sheet ref
  unregister: (ref: React.RefObject<BottomSheetModal>) => {
    const index = bottomSheetRefs.indexOf(ref);
    if (index > -1) {
      bottomSheetRefs.splice(index, 1);
    }
  },

  // Dismiss all registered bottom sheets
  dismissAll: async () => {
    const dismissPromises = bottomSheetRefs.map(ref => {
      return new Promise<void>(resolve => {
        try {
          if (ref.current) {
            ref.current.dismiss();
          }
        } catch (error) {
          console.warn('Error dismissing bottom sheet:', error);
        }
        // Wait for dismiss animation (300ms is typical for bottom sheets)
        setTimeout(resolve, 300);
      });
    });

    if (dismissPromises.length > 0) {
      await Promise.all(dismissPromises);
      // Additional small delay to ensure all modals are fully dismissed
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  },
};
