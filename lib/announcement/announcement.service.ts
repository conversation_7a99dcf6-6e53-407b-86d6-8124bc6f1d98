import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Application from 'expo-application';

const SHOW_LOGS = false;

// Storage key for tracking last shown version
const LAST_SHOWN_VERSION_KEY = 'last_shown_announcement_version';

export interface AnnouncementData {
  version: string;
  date: string;
  title: string;
  features: string[];
  improvements?: string[];
  fixes?: string[];
}

class AnnouncementService {
  private static instance: AnnouncementService;

  private constructor() {}

  public static getInstance(): AnnouncementService {
    if (!AnnouncementService.instance) {
      AnnouncementService.instance = new AnnouncementService();
    }
    return AnnouncementService.instance;
  }

  /**
   * Get the current app version
   */
  public getCurrentAppVersion(): string {
    return Application.nativeApplicationVersion || '1.0.0';
  }

  /**
   * Parse version string into components for comparison
   */
  private parseVersion(version: string): { major: number; minor: number; patch: number } {
    const parts = version.split('.').map(Number);
    return {
      major: parts[0] || 0,
      minor: parts[1] || 0,
      patch: parts[2] || 0,
    };
  }

  /**
   * Compare two version strings
   */
  private compareVersions(version1: string, version2: string): number {
    const v1 = this.parseVersion(version1);
    const v2 = this.parseVersion(version2);

    if (v1.major !== v2.major) return v1.major - v2.major;
    if (v1.minor !== v2.minor) return v1.minor - v2.minor;
    return v1.patch - v2.patch;
  }

  /**
   * Get the last shown announcement version
   */
  public async getLastShownVersion(): Promise<string | null> {
    try {
      const lastShownVersion = await AsyncStorage.getItem(LAST_SHOWN_VERSION_KEY);
      return lastShownVersion;
    } catch (error) {
      console.error('Error getting last shown version:', error);
      return null;
    }
  }

  /**
   * Mark an announcement version as shown
   */
  public async markAnnouncementAsShown(version: string): Promise<void> {
    try {
      await AsyncStorage.setItem(LAST_SHOWN_VERSION_KEY, version);
    } catch (error) {
      console.error('Error marking announcement as shown:', error);
    }
  }

  /**
   * Find the latest announcement from the update log
   */
  private getLatestAnnouncement(announcements: AnnouncementData[]): AnnouncementData | null {
    if (announcements.length === 0) return null;

    // Sort by version descending to get the latest
    const sortedAnnouncements = [...announcements].sort((a, b) =>
      this.compareVersions(b.version, a.version)
    );

    return sortedAnnouncements[0];
  }

  /**
   * Check if an announcement should be shown for the current version
   * Simple logic: Show if current app version is different from last shown version
   */
  public async shouldShowAnnouncement(announcements: AnnouncementData[]): Promise<{
    shouldShow: boolean;
    announcement: AnnouncementData | null;
  }> {
    try {
      const currentVersion = this.getCurrentAppVersion();
      const lastShownVersion = await this.getLastShownVersion();

      // Get the latest announcement
      const latestAnnouncement = this.getLatestAnnouncement(announcements);

      if (!latestAnnouncement) {
        return { shouldShow: false, announcement: null };
      }

      // Show announcement if:
      // 1. We've never shown any announcement before, OR
      // 2. The current app version is different from the last shown version
      const shouldShow = !lastShownVersion || currentVersion !== lastShownVersion;

      if (SHOW_LOGS) {
        console.log('[Announcement] Current version:', currentVersion);
        console.log('[Announcement] Last shown version:', lastShownVersion);
        console.log('[Announcement] Latest announcement version:', latestAnnouncement.version);
        console.log('[Announcement] Should show:', shouldShow);
      }

      return {
        shouldShow: shouldShow,
        announcement: shouldShow ? latestAnnouncement : null,
      };
    } catch (error) {
      console.error('Error checking if announcement should show:', error);
      return { shouldShow: false, announcement: null };
    }
  }

  /**
   * Reset announcement history (for testing)
   */
  public async resetShownAnnouncements(): Promise<void> {
    try {
      await AsyncStorage.removeItem(LAST_SHOWN_VERSION_KEY);
    } catch (error) {
      console.error('Error resetting shown announcements:', error);
    }
  }

  /**
   * Test function to force show announcement for current version
   */
  public async testShowAnnouncement(): Promise<void> {
    const currentVersion = this.getCurrentAppVersion();
    await this.resetShownAnnouncements();
    console.log(
      `[Announcement] Test mode: Reset announcement history for version ${currentVersion}`
    );
  }

  // Legacy methods for backward compatibility
  public async getShownAnnouncements(): Promise<string[]> {
    const lastShown = await this.getLastShownVersion();
    return lastShown ? [lastShown] : [];
  }

  public updateConfig(config: any): void {
    // Keep for compatibility, but no longer used
    console.log('[Announcement] Config update ignored - using simple logic now');
  }
}

export default AnnouncementService.getInstance();
