import * as ExpoSecureStore from 'expo-secure-store';

export const SecureStoreKeys = {
  authToken: 'auth_token',
} as const;

type SecureStoreKey = (typeof SecureStoreKeys)[keyof typeof SecureStoreKeys];

export class SecureStore {
  private static instance: SecureStore;

  private constructor() {}

  public static getInstance(): SecureStore {
    if (!SecureStore.instance) {
      SecureStore.instance = new SecureStore();
    }
    return SecureStore.instance;
  }

  public async set(key: SecureStoreKey, value: string): Promise<void> {
    await ExpoSecureStore.setItemAsync(key, value);
  }

  public async get(key: SecureStoreKey): Promise<string | null> {
    return await ExpoSecureStore.getItemAsync(key);
  }

  public async remove(key: SecureStoreKey): Promise<void> {
    await ExpoSecureStore.deleteItemAsync(key);
  }
}

// Export a default instance for easier usage
export const secureStore = SecureStore.getInstance();
