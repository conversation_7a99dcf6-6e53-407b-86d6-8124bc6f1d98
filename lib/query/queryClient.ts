import { QueryClient } from '@tanstack/react-query';
import axios from 'axios';

import { handleApiError } from '../api/client';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        // Don't retry on 401 errors
        if (
          axios.isAxiosError(error) &&
          (error.response?.status === 401 ||
            error.response?.status === 403 ||
            error.response?.status === 404)
        ) {
          return false;
        }
        return failureCount < 2;
      },
    },
    mutations: {
      // Global error handler for mutations
      onError: error => {
        if (process.env.NODE_ENV === 'development') {
          console.error('Mutation Error:', handleApiError(error));
        }
      },
    },
  },
});
