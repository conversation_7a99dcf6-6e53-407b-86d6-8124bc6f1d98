import { User } from '@/stores/user';
import * as WebBrowser from 'expo-web-browser';

export const handleOpenSurvey = async (user: User, accessToken: string, callback?: () => void) => {
  if (!user?.firstName || !user?.lastName || !user?.email) {
    return; // Don't open if user data is missing
  }

  if (!accessToken) {
    return; // Don't open if user data is missing
  }

  const params = new URLSearchParams({
    first_name: user.firstName,
    last_name: user.lastName,
    email: user.email,
    platform: 'mobile', // Add platform param to identify mobile app
    sessionTokenId: accessToken,
  });

  const surveyUrl = `${process.env.EXPO_PUBLIC_WEBSITE_URL}/survey?${params.toString()}`;
  try {
    await WebBrowser.openBrowserAsync(surveyUrl);
  } catch (error) {
    console.error('Error opening survey:', error);
  } finally {
    if (callback) {
      await callback();
    }
  }
};
