import { isWithinInterval, set } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

export const COMPETITION_TIMEZONE = 'America/New_York';
export const MARKET_OPEN_HOUR = 9;
export const MARKET_OPEN_MINUTE = 30;
export const MARKET_CLOSE_HOUR = 16;
export const MARKET_CLOSE_MINUTE = 0;

export function isMarketOpen(date: Date = new Date()): boolean {
  const zonedDate = toZonedTime(date, COMPETITION_TIMEZONE);
  // if (isHoliday(zonedDate)) {
  //   return false;
  // }

  const dayOfWeek = zonedDate.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false;
  }

  const marketOpen = set(zonedDate, {
    hours: MARKET_OPEN_HOUR,
    minutes: MARKET_OPEN_MINUTE,
    seconds: 0,
  });
  const marketClose = set(zonedDate, {
    hours: MARKET_CLOSE_HOUR,
    minutes: MARKET_CLOSE_MINUTE,
    seconds: 0,
  });

  return isWithinInterval(zonedDate, { start: marketOpen, end: marketClose });
}
