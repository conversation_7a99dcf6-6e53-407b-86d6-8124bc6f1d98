import React from 'react';

export interface Message {
  role: 'user' | 'assistant';
  content: string;
  liked?: boolean;
  disliked?: boolean;
  responses?: string[]; // For assistant messages, store multiple response variations
  currentResponseIndex?: number; // Track which response is currently being shown
  responseLiked?: boolean[]; // Like state for each response variation
  responseDisliked?: boolean[]; // Dislike state for each response variation
  showFollowUpQuestions?: boolean; // Show follow-up questions for newly generated responses
  aiMessageId?: string; // AI message ID for feedback API
  aiMessageIds?: string[]; // AI message IDs for each response variation
}

// Helper method for adding new assistant message
export const addNewAssistantMessage = (
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  content: string,
  aiMessageId?: string
) => {
  setMessages(prev => [
    ...prev,
    {
      role: 'assistant',
      content: content,
      liked: false,
      disliked: false,
      responses: [content],
      currentResponseIndex: 0,
      responseLiked: [false],
      responseDisliked: [false],
      showFollowUpQuestions: true,
      aiMessageId: aiMessageId,
      aiMessageIds: aiMessageId ? [aiMessageId] : undefined,
    },
  ]);
};

// Helper method for creating a new response slot (for retry operations)
export const createNewResponseSlot = (
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  messageIndex: number,
  initialContent: string = ''
) => {
  setMessages(prev => {
    const newMessages = [...prev];
    const assistantMessage = newMessages[messageIndex];

    if (assistantMessage.role === 'assistant') {
      // Initialize responses array if it doesn't exist
      if (!assistantMessage.responses) {
        assistantMessage.responses = [assistantMessage.content];
        assistantMessage.responseLiked = [assistantMessage.liked || false];
        assistantMessage.responseDisliked = [assistantMessage.disliked || false];
        assistantMessage.aiMessageIds = assistantMessage.aiMessageId
          ? [assistantMessage.aiMessageId]
          : [];
      }

      // Add a new empty response slot
      assistantMessage.responses.push(initialContent);
      assistantMessage.responseLiked = assistantMessage.responseLiked || [];
      assistantMessage.responseDisliked = assistantMessage.responseDisliked || [];
      assistantMessage.aiMessageIds = assistantMessage.aiMessageIds || [];
      assistantMessage.responseLiked.push(false);
      assistantMessage.responseDisliked.push(false);
      assistantMessage.aiMessageIds.push(''); // Will be updated when response completes

      // Update to show the newest response slot
      assistantMessage.currentResponseIndex = assistantMessage.responses.length - 1;
      assistantMessage.content = initialContent;

      // Update the main like/dislike to reflect the current response
      assistantMessage.liked = false;
      assistantMessage.disliked = false;
      assistantMessage.aiMessageId = '';

      // Don't show follow-up questions during loading
      assistantMessage.showFollowUpQuestions = false;
    }

    return newMessages;
  });
};

// Helper method for updating the current response slot (for retry operations)
export const updateCurrentResponseSlot = (
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  messageIndex: number,
  newResponse: string,
  aiMessageId?: string
) => {
  setMessages(prev => {
    const newMessages = [...prev];
    const assistantMessage = newMessages[messageIndex];

    if (assistantMessage.role === 'assistant' && assistantMessage.responses) {
      const currentIndex = assistantMessage.currentResponseIndex || 0;

      // Update the current response slot
      assistantMessage.responses[currentIndex] = newResponse;
      assistantMessage.content = newResponse;

      // Update AI message ID for the current response
      if (aiMessageId && assistantMessage.aiMessageIds) {
        assistantMessage.aiMessageIds[currentIndex] = aiMessageId;
      }
      assistantMessage.aiMessageId = aiMessageId;

      // Show follow-up questions for completed responses
      assistantMessage.showFollowUpQuestions = true;
    }

    return newMessages;
  });
};

// Helper method for adding response to existing message (legacy - for compatibility)
export const addResponseToExistingMessage = (
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  messageIndex: number,
  newResponse: string,
  aiMessageId?: string
) => {
  setMessages(prev => {
    const newMessages = [...prev];
    const assistantMessage = newMessages[messageIndex];

    if (assistantMessage.role === 'assistant') {
      // Initialize responses array if it doesn't exist
      if (!assistantMessage.responses) {
        assistantMessage.responses = [assistantMessage.content];
        assistantMessage.responseLiked = [assistantMessage.liked || false];
        assistantMessage.responseDisliked = [assistantMessage.disliked || false];
        assistantMessage.aiMessageIds = assistantMessage.aiMessageId
          ? [assistantMessage.aiMessageId]
          : [];
      }

      // Add the new response and its initial like/dislike state
      assistantMessage.responses.push(newResponse);
      assistantMessage.responseLiked = assistantMessage.responseLiked || [];
      assistantMessage.responseDisliked = assistantMessage.responseDisliked || [];
      assistantMessage.aiMessageIds = assistantMessage.aiMessageIds || [];
      assistantMessage.responseLiked.push(false);
      assistantMessage.responseDisliked.push(false);
      assistantMessage.aiMessageIds.push(aiMessageId || '');

      // Update to show the newest response
      assistantMessage.currentResponseIndex = assistantMessage.responses.length - 1;
      assistantMessage.content = newResponse;

      // Update the main like/dislike to reflect the current response
      assistantMessage.liked = false;
      assistantMessage.disliked = false;
      assistantMessage.aiMessageId = aiMessageId;

      // Show follow-up questions for new responses
      assistantMessage.showFollowUpQuestions = true;
    }

    return newMessages;
  });
};

// Helper method for finding corresponding user message
export const findCorrespondingUserMessage = (
  messages: Message[],
  assistantIndex: number
): { message: Message; index: number } | null => {
  for (let i = assistantIndex - 1; i >= 0; i--) {
    if (messages[i].role === 'user') {
      return { message: messages[i], index: i };
    }
  }
  return null;
};

// Helper method for adding error message
export const addErrorMessage = (
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  errorMessage: string,
  messageIndex?: number
) => {
  if (messageIndex !== undefined) {
    // Add error to existing message
    addResponseToExistingMessage(setMessages, messageIndex, errorMessage);
  } else {
    // Add new error message
    addNewAssistantMessage(setMessages, errorMessage);
  }
};

// Helper method for hiding follow-up questions from all messages
export const hideAllFollowUpQuestions = (
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>
) => {
  setMessages(prev => {
    return prev.map(message => ({
      ...message,
      showFollowUpQuestions: false,
    }));
  });
};
