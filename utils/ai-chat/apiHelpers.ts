import { FetchResponse } from 'expo/build/winter/fetch/FetchResponse';

import { sendChatMessage } from '@/hooks/useAIChat';

import { Message } from './messageHelpers';

const showLog = false;

// Helper method for making API calls
export const makeApiCall = async (
  userMessage?: Message,
  chatId?: string,
  retryMessageId?: string,
  signal?: AbortSignal
) => {
  // Use the new API
  const response = await sendChatMessage({
    chatId: chatId,
    message: retryMessageId ? undefined : userMessage?.content, // Don't send message if retrying
    retryMessageId: retryMessageId ? retryMessageId : undefined,
    signal: signal, // Pass the abort signal
  });

  return response;
};

// Helper method for processing streaming response
export const processStreamingResponse = async (
  response: FetchResponse,
  onStreamUpdate: (content: string) => void,
  signal?: AbortSignal
): Promise<{ content: string; metadata?: any }> => {
  let accumulatedMessage = '';
  let metadata: any = null;

  try {
    // Check if response.body exists and is readable
    if (!response.body) {
      throw new Error('Response body is not available for streaming');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let buffer = '';

    if (showLog) {
      console.log('📥 Starting stream processing with getReader');
    }

    while (true) {
      // Check if cancelled before reading
      if (signal?.aborted) {
        if (showLog) {
          console.log('📥 Stream cancelled before read');
        }
        reader.releaseLock();
        return { content: accumulatedMessage, metadata };
      }

      const { value, done } = await reader.read();

      // Check if cancelled after reading
      if (signal?.aborted) {
        if (showLog) {
          console.log('📥 Stream cancelled after read');
        }
        reader.releaseLock();
        return { content: accumulatedMessage, metadata };
      }

      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      // Process SSE format lines
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep incomplete line in buffer

      for (const line of lines) {
        // Check if cancelled during line processing
        if (signal?.aborted) {
          if (showLog) {
            console.log('📥 Stream cancelled during line processing');
          }
          try {
            reader.releaseLock();
          } catch (lockError) {
            if (showLog) {
              console.log('📥 Reader already released');
            }
          }
          return { content: accumulatedMessage, metadata };
        }

        if (line.startsWith('data: ')) {
          const data = line.slice(6).trim();

          if (data === '[DONE]') {
            if (showLog) {
              console.log('📥 Received [DONE] signal');
            }
            return { content: accumulatedMessage, metadata };
          }

          if (data === '') continue;

          try {
            const parsed = JSON.parse(data);

            if (parsed.type === 'metadata') {
              metadata = parsed;
              if (showLog) {
                console.log('📥 Received metadata:', metadata);
              }
            } else if (parsed.type === 'message') {
              accumulatedMessage += parsed.content;
              onStreamUpdate(accumulatedMessage);

              // Reduced delay for better responsiveness
              await new Promise(resolve => setTimeout(resolve, 20));
            }
          } catch (parseError) {
            console.warn('📖 Failed to parse SSE data:', data, parseError);
          }
        }
      }
    }

    if (showLog) {
      console.log('📥 Stream processing completed');
    }
    // console.log('📥 Accumulated message:', accumulatedMessage);
  } catch (error) {
    console.error('📖 Stream processing error:', error);

    // If it's an abort error, return what we have so far
    if (
      error instanceof Error &&
      (error.name === 'AbortError' || error.message.includes('FetchRequestCanceledException'))
    ) {
      if (showLog) {
          console.log(
          '📥 Stream processing aborted (likely due to navigation or user cancellation), returning partial content'
        );
      }
      return { content: accumulatedMessage, metadata };
    }

    throw error;
  }

  return { content: accumulatedMessage, metadata };
};
