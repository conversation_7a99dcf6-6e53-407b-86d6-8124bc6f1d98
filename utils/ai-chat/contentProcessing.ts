import * as Clipboard from 'expo-clipboard';

import { Share } from 'react-native';

// Clean up message content for copying/sharing
export const cleanContent = (content: string) => {
  // Replace escaped newlines with actual newlines
  let cleanedContent = content.replace(/\\n/g, '\n');
  // Handle any other escaped characters
  cleanedContent = cleanedContent.replace(/\\"/g, '"');
  cleanedContent = cleanedContent.replace(/\\'/g, "'");
  // Remove any markdown formatting if present
  cleanedContent = cleanedContent.replace(/\*\*(.*?)\*\*/g, '$1'); // Bold
  cleanedContent = cleanedContent.replace(/\*(.*?)\*/g, '$1'); // Italic
  cleanedContent = cleanedContent.replace(/```([\s\S]*?)```/g, '$1'); // Code block
  cleanedContent = cleanedContent.replace(/`(.*?)`/g, '$1'); // Inline code

  return cleanedContent.trim();
};

// Handle copying content to clipboard
export const handleCopy = async (content: string, showToast: (message: string) => void, t: any) => {
  try {
    const processedContent = cleanContent(content);
    await Clipboard.setStringAsync(processedContent);
    showToast(t('aiChat.messages.copied'));
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    showToast(t('aiChat.messages.failedToCopy'));
  }
};

// Handle sharing content
export const handleShare = async (
  content: string,
  showToast: (message: string) => void,
  t: any
) => {
  try {
    const processedContent = cleanContent(content);
    await Share.share({
      message: processedContent,
    });
  } catch (error) {
    console.error('Error sharing message:', error);
    showToast(t('aiChat.messages.failedToShare'));
  }
};
