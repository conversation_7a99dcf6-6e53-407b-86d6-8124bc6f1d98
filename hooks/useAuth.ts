import { authService, LoginFormValues, SignUpFormValues } from '@/lib/api/auth.service';
import { apiClient, handleApiError } from '@/lib/api/client';
import { secureStore, SecureStoreKeys } from '@/lib/secure-store';
import { useUserStore } from '@/stores/user';
import {
  ForgotPasswordResponse,
  ResetPasswordFormValues,
  ResetPasswordResponse,
  ChangePasswordFormValues,
  ChangePasswordResponse,
} from '@/types/api/auth';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

import { useEffect } from 'react';

// Query keys for auth-related queries
export const AUTH_KEYS = {
  all: ['auth'] as const,
  profile: () => [...AUTH_KEYS.all, 'profile'] as const,
} as const;

export function useAuth() {
  const queryClient = useQueryClient();
  const { user, setUser, logout: clearUser, hasPermissionTo } = useUserStore();
  const isAuthenticated = !!user;

  // Check if user has token on mount and token changes
  useEffect(() => {
    const checkAuth = async () => {
      const token = await secureStore.get(SecureStoreKeys.authToken);
      if (!token) {
        clearUser();
      }
    };
    checkAuth();
  }, [clearUser]);

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginFormValues) => {
      const data = await authService.login(credentials);
      return data;
    },
    onSuccess: () => {
      // Trigger profile fetch after successful login
      refetchProfile();
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const signUpMutation = useMutation({
    mutationFn: async (credentials: SignUpFormValues) => {
      const data = await authService.signUp(credentials);
      return data;
    },
    onSuccess: () => {
      // Trigger profile fetch after successful signup
      refetchProfile();
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await authService.logout();
    },
    onSuccess: () => {
      clearUser();
      // Clear all queries from the cache on logout
      queryClient.clear();
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const googleSignInMutation = useMutation({
    mutationFn: async () => {
      const data = await authService.signInWithGoogle();
      return data;
    },
    onSuccess: () => {
      // Trigger profile fetch after successful Google sign-in
      refetchProfile();
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const appleSignInMutation = useMutation({
    mutationFn: async () => {
      const data = await authService.signInWithApple();
      return data;
    },
    onSuccess: () => {
      // Trigger profile fetch after successful Apple sign-in
      refetchProfile();
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const sendVerificationEmailMutation = useMutation({
    mutationFn: async () => {
      const data = await authService.sendVerificationEmail();
      return data;
    },
    onSuccess: () => {
      // Optionally refetch profile to get updated verification status
      refetchProfile();
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const forgotPasswordMutation = useMutation({
    mutationFn: async (email: string) => {
      const response = await apiClient.post<ForgotPasswordResponse>(
        '/auth/mobile/forgot-password',
        { email }
      );
      return response.data;
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const resetPasswordMutation = useMutation({
    mutationFn: async (values: ResetPasswordFormValues) => {
      const response = await apiClient.post<ResetPasswordResponse>(
        '/auth/mobile/reset-password',
        values
      );
      return response.data;
    },
    onError: error => {
      handleApiError(error);
    },
  });

  const changePasswordMutation = useMutation({
    mutationFn: async (values: ChangePasswordFormValues) => {
      const response = await apiClient.post<ChangePasswordResponse>(
        '/auth/mobile/change-password',
        values
      );
      return response.data;
    },
    onError: error => {
      handleApiError(error);
    },
  });

  // Only fetch profile when authenticated
  const {
    data: profile,
    isFetching: isLoadingProfile,
    refetch: refetchProfile,
  } = useQuery({
    queryKey: AUTH_KEYS.profile(),
    queryFn: async () => {
      const data = await authService.getCurrentUser();
      if (data.success && data.data) {
        setUser(data.data);
      }
      return data;
    },
    // Important: Only fetch when authenticated
    enabled: isAuthenticated,
    // Add these options to reduce API calls
    staleTime: 60 * 60 * 1000, // Consider data fresh for 1 hour
    refetchOnWindowFocus: false, // Disable refetch on window focus
    retry: (failureCount, error) => {
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        clearUser(); // Update auth state on 401
        return false;
      }
      return failureCount < 2;
    },
  });

  const getAccessToken = async (): Promise<string | null> => {
    const token = await secureStore.get(SecureStoreKeys.authToken);
    if (!token) {
      return null;
    }
    return token;
  };

  return {
    login: loginMutation.mutateAsync,
    signUp: signUpMutation.mutateAsync,
    logout: logoutMutation.mutateAsync,
    signInWithGoogle: googleSignInMutation.mutateAsync,
    signInWithApple: appleSignInMutation.mutateAsync,
    user,
    profile,
    isLoadingProfile,
    isLoggingIn: loginMutation.isPending,
    isSigningUp: signUpMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
    isGoogleSigningIn: googleSignInMutation.isPending,
    isAppleSigningIn: appleSignInMutation.isPending,
    isAuthenticated,
    isEmailVerified: !!user?.verifiedAt,
    refetchProfile,
    getAccessToken,
    hasPermissionTo,
    sendVerificationEmail: sendVerificationEmailMutation.mutateAsync,
    isSendingVerification: sendVerificationEmailMutation.isPending,
    forgotPassword: forgotPasswordMutation.mutateAsync,
    resetPassword: resetPasswordMutation.mutateAsync,
    changePassword: changePasswordMutation.mutateAsync,
    isForgotPasswordLoading: forgotPasswordMutation.isPending,
    isResetPasswordLoading: resetPasswordMutation.isPending,
    isChangePasswordLoading: changePasswordMutation.isPending,
  };
}
