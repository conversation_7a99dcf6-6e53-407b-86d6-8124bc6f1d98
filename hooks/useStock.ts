import { Language, LANGUAGES } from '@/i18n';
import { apiClient, handleApiError } from '@/lib/api/client';
import { useStockStore } from '@/stores/stock';
import {
  AnalysisStockTrendResponse,
  ResearcherFinancialStatementResponse,
  ResearcherTargetGradingResponse,
  AnalysisStockAndIndustryComparisonResponse,
  ResearcherStockNewsResponse,
  CompanySearchResponse,
  UserViewStockUsageResponse,
  LatestClosingPricesResponse,
} from '@/types/api/stock';
import { useIsFocused } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';

// TODO: refactor this
// Create a debounced refetch function that will only execute once within a 1.5-second window
let refetchTimeout: NodeJS.Timeout | null = null;
const debouncedRefetchUsage = (refetchFn: () => void) => {
  if (refetchTimeout) {
    clearTimeout(refetchTimeout);
  }
  refetchTimeout = setTimeout(() => {
    refetchFn();
    refetchTimeout = null;
  }, 1500);
};

// Query keys for stock-related queries
export const STOCK_KEYS = {
  all: ['stocks'] as const,
  search: (query: string) => [...STOCK_KEYS.all, 'search', query] as const,
  trend: (symbol: string, locale: Language = LANGUAGES.en) =>
    [...STOCK_KEYS.all, 'trend', symbol, locale] as const,
  financialStatement: (symbol: string, locale: Language = LANGUAGES.en) =>
    [...STOCK_KEYS.all, 'financial-statement', symbol, locale] as const,
  targetGrading: (symbol: string, locale: Language = LANGUAGES.en) =>
    [...STOCK_KEYS.all, 'target-grading', symbol, locale] as const,
  industryComparison: (symbol: string, locale: Language = LANGUAGES.en) =>
    [...STOCK_KEYS.all, 'industry-comparison', symbol, locale] as const,
  stockNews: (symbol: string, locale: Language = LANGUAGES.en) =>
    [...STOCK_KEYS.all, 'news', symbol, locale] as const,
  viewUsage: () => [...STOCK_KEYS.all, 'view_usage'] as const,
  latestClosingPrices: (symbols: string[]) =>
    [...STOCK_KEYS.all, 'latest-closing-prices', symbols] as const,
} as const;

// Company Search Hook
export function useCompanySearch(query: string) {
  return useQuery({
    queryKey: STOCK_KEYS.search(query),
    queryFn: async () => {
      if (!query) return [];
      // const response = await apiClient.get<CompanySearchResponse>(`/stocks/search?query=${query}`);
      const response = await apiClient.get<CompanySearchResponse>(
        `/stocks/search?query=${query}&source=server`
      );
      return response.data;
    },
    enabled: !!query,
  });
}

// Stock Trend Analysis Hook
export function useStockTrend(symbol: string, locale: Language = LANGUAGES.en) {
  const isFocused = useIsFocused();
  const { isStockViewedToday } = useStockStore();
  const isViewed = isStockViewedToday(symbol);
  const { refetch: refetchUsage } = useViewStockUsage();

  return useQuery({
    queryKey: STOCK_KEYS.trend(symbol, locale),
    queryFn: async () => {
      try {
        const response = await apiClient.get<AnalysisStockTrendResponse>(
          `/fins-ai/analysis_stock_trend?symbol=${symbol}&language_code=${locale}`
        );
        debouncedRefetchUsage(refetchUsage);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: isFocused || isViewed,
  });
}

// Financial Statement Hook
export function useFinancialStatement(symbol: string, locale: Language = LANGUAGES.en) {
  const isFocused = useIsFocused();
  const { isStockViewedToday } = useStockStore();
  const isViewed = isStockViewedToday(symbol);
  const { refetch: refetchUsage } = useViewStockUsage();

  return useQuery({
    queryKey: STOCK_KEYS.financialStatement(symbol, locale),
    queryFn: async () => {
      try {
        const response = await apiClient.get<ResearcherFinancialStatementResponse>(
          `/fins-ai/researcher_financial_statement?symbol=${symbol}&language_code=${locale}`
        );
        debouncedRefetchUsage(refetchUsage);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: isFocused || isViewed,
  });
}

// Target Grading Hook
export function useTargetGrading(symbol: string, locale: Language = LANGUAGES.en) {
  const isFocused = useIsFocused();
  const { isStockViewedToday } = useStockStore();
  const isViewed = isStockViewedToday(symbol);
  const { refetch: refetchUsage } = useViewStockUsage();

  return useQuery({
    queryKey: STOCK_KEYS.targetGrading(symbol, locale),
    queryFn: async () => {
      try {
        const response = await apiClient.get<ResearcherTargetGradingResponse>(
          `/fins-ai/researcher_target_grading?symbol=${symbol}&language_code=${locale}`
        );
        debouncedRefetchUsage(refetchUsage);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: isFocused || isViewed,
  });
}

// Industry Comparison Hook
export function useIndustryComparison(symbol: string, locale: Language = LANGUAGES.en) {
  const isFocused = useIsFocused();
  const { isStockViewedToday } = useStockStore();
  const isViewed = isStockViewedToday(symbol);
  const { refetch: refetchUsage } = useViewStockUsage();

  return useQuery({
    queryKey: STOCK_KEYS.industryComparison(symbol, locale),
    queryFn: async () => {
      try {
        const response = await apiClient.get<AnalysisStockAndIndustryComparisonResponse>(
          `/fins-ai/analysis_stock_and_industry_comparison?symbol=${symbol}&language_code=${locale}`
        );
        debouncedRefetchUsage(refetchUsage);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: isFocused || isViewed,
  });
}

// Stock News Hook
export function useStockNews(symbol: string, locale: Language = LANGUAGES.en) {
  const isFocused = useIsFocused();
  const { isStockViewedToday } = useStockStore();
  const isViewed = isStockViewedToday(symbol);
  const { refetch: refetchUsage } = useViewStockUsage();

  return useQuery({
    queryKey: STOCK_KEYS.stockNews(symbol, locale),
    queryFn: async () => {
      try {
        const response = await apiClient.get<ResearcherStockNewsResponse>(
          `/fins-ai/researcher_stock_news?symbol=${symbol}&language_code=${locale}`
        );
        debouncedRefetchUsage(refetchUsage);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: isFocused || isViewed,
  });
}

// Stock Usage View Hook
export function useViewStockUsage() {
  const { syncViewedStocksFromServer } = useStockStore();

  const query = useQuery({
    queryKey: STOCK_KEYS.viewUsage(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<UserViewStockUsageResponse>('/user/view_stock_usage');
        // Sync the viewed stocks from server to local state
        syncViewedStocksFromServer(response.data.data.stocks);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  return query;
}

// Latest Closing Prices Hook
export function useLatestClosingPrices(symbols: string[]) {
  return useQuery({
    queryKey: STOCK_KEYS.latestClosingPrices(symbols),
    queryFn: async () => {
      try {
        const response = await apiClient.get<LatestClosingPricesResponse>(
          `/stocks/latest_closing_prices?symbols=${symbols.join(',')}`
        );
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: symbols.length > 0,
  });
}
