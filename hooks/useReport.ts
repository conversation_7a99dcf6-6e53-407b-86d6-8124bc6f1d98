import { apiClient, handleApiError } from '@/lib/api/client';
import { ReportSubscribeResponse } from '@/types/api/report';
import { ReportManageReponseDTO } from '@/types/api/transformer';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';

import { useAuth } from '@/hooks/useAuth';

// Query keys for report-related queries
export const REPORT_KEYS = {
  all: ['reports'] as const,
  reportManage: (email: string) => [...REPORT_KEYS.all, 'user_report', email] as const,
} as const;

// Subscription parameters
interface SubscribeParams {
  firstName: string;
  lastName: string;
  email: string;
  language: string;
  stocks: string[];
}

// User Report Hook
export function useReportManage(email: string) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: REPORT_KEYS.reportManage(email),
    queryFn: async () => {
      try {
        const response = await apiClient.post<ReportManageReponseDTO>('/report/manage', {
          email,
        });
        return response.data;
      } catch (error) {
        // Print detailed error information
        if (axios.isAxiosError(error)) {
          console.error('[Report Manage Error Details]', {
            config: error.config,
            response: error.response,
            message: error.message,
            code: error.code,
          });
        }
        return {
          success: false,
          data: {
            user: null,
            reportList: [],
          },
        };
      }
    },
    enabled: isAuthenticated && !!email,
    retry: false,
    staleTime: 30000,
  });
}

// Subscribe Report Hook
export function useReportSubscribe() {
  return useMutation({
    mutationFn: async (params: SubscribeParams) => {
      try {
        const response = await apiClient.post<ReportSubscribeResponse>('/report/subscribe', params);
        return response.data;
      } catch (error) {
        handleApiError(error);
        throw error;
      }
    },
  });
}

// Simplified Report Hook for current user
export function useReport() {
  const { profile } = useAuth();
  const email = profile?.data?.email || '';
  const reportQuery = useReportManage(email);

  return {
    reportData: reportQuery.data,
    isLoading: reportQuery.isLoading,
    isError: reportQuery.isError,
    refetch: reportQuery.refetch,
  };
}
