import { apiClient } from '@/lib/api/client';
import { QuoteResponse, HistoricalPricesResponse, OutlookResponse } from '@/types/api/fmp';
import { useQuery, UseQueryOptions, useQueryClient } from '@tanstack/react-query';

import { useEffect, useCallback, useRef } from 'react';

import { AppState, AppStateStatus } from 'react-native';

const SHOW_LOGS = false;

// Query keys for FMP-related queries
export const FMP_KEYS = {
  all: ['fmp'] as const,
  quote: (symbol: string) => [...FMP_KEYS.all, 'quote', symbol] as const,
  historical: (symbol: string) => [...FMP_KEYS.all, 'historical', symbol] as const,
  outlook: (symbol: string) => [...FMP_KEYS.all, 'outlook', symbol] as const,
} as const;

// Utility to log stock quote fetches (only in development)
const logQuoteFetch = (symbol: string, type: 'fetching' | 'success' | 'error', data?: any) => {
  if (__DEV__ && SHOW_LOGS) {
    const timestamp = new Date().toLocaleTimeString();
    switch (type) {
      case 'fetching':
        console.log(`[${timestamp}] 📈 Fetching quote for ${symbol}`);
        break;
      case 'success':
        console.log(
          `[${timestamp}] ✅ Quote updated for ${symbol}: $${data?.price?.toFixed(2)} (${data?.changesPercentage?.toFixed(2)}%)`
        );
        break;
      case 'error':
        console.log(`[${timestamp}] ❌ Failed to fetch quote for ${symbol}:`, data);
        break;
    }
  }
};

// Hook to manage bulk refresh of stock quotes when app becomes active
export function useWatchlistRefresh(symbols: string[]) {
  const queryClient = useQueryClient();
  const lastRefreshTime = useRef<number>(0);
  const UNIFIED_REFRESH_THROTTLE_MS = 30 * 1000; // 30 seconds for both manual and auto refresh

  const refreshAllQuotes = useCallback(
    (source: 'manual' | 'auto' = 'manual') => {
      const now = Date.now();
      const timeSinceLastRefresh = now - lastRefreshTime.current;

      // Check if we should throttle this refresh
      if (timeSinceLastRefresh < UNIFIED_REFRESH_THROTTLE_MS) {
        const remainingTime = Math.ceil(
          (UNIFIED_REFRESH_THROTTLE_MS - timeSinceLastRefresh) / 1000
        );
        console.log(`⏳ ${source} refresh throttled, ${remainingTime}s remaining`);
        return false; // Indicate refresh was throttled
      }

      // Proceed with refresh
      if (symbols.length > 0) {
        logQuoteFetch(`[${symbols.join(', ')}]`, 'fetching');
        lastRefreshTime.current = now;
        symbols.forEach(symbol => {
          queryClient.invalidateQueries({
            queryKey: FMP_KEYS.quote(symbol),
          });
        });
        console.log(`🔄 ${source} refresh executed for:`, symbols);
        return true; // Indicate refresh was executed
      }

      return false; // No symbols to refresh
    },
    [symbols, queryClient]
  );

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && symbols.length > 0) {
        refreshAllQuotes('auto');
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [symbols, refreshAllQuotes]);

  // Return additional utility functions
  const canRefresh = useCallback(() => {
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTime.current;
    return timeSinceLastRefresh >= UNIFIED_REFRESH_THROTTLE_MS;
  }, []);

  const getTimeUntilNextRefresh = useCallback(() => {
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTime.current;
    const remaining = UNIFIED_REFRESH_THROTTLE_MS - timeSinceLastRefresh;
    return Math.max(0, Math.ceil(remaining / 1000));
  }, []);

  return {
    refreshAllQuotes,
    canRefresh,
    getTimeUntilNextRefresh,
    THROTTLE_MS: UNIFIED_REFRESH_THROTTLE_MS,
  };
}

// Stock Quote Hook with enhanced refresh strategies
export function useStockQuote(symbol: string, options?: Partial<UseQueryOptions<QuoteResponse>>) {
  return useQuery({
    queryKey: FMP_KEYS.quote(symbol),
    queryFn: async () => {
      logQuoteFetch(symbol, 'fetching');
      try {
        const response = await apiClient.get<QuoteResponse[]>(`/stocks/quote?symbol=${symbol}`);
        const quote = response.data[0];
        if (quote) {
          logQuoteFetch(symbol, 'success', quote);
        }
        return quote;
      } catch (error) {
        logQuoteFetch(symbol, 'error', error);
        throw error;
      }
    },
    // Enhanced cache configuration for better data freshness
    staleTime: 2 * 60 * 1000, // Consider data stale after 2 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (was cacheTime in older versions)
    refetchOnWindowFocus: true, // Refetch when app comes to foreground
    refetchOnMount: true, // Refetch when component mounts
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes to ensure freshness
    refetchIntervalInBackground: false, // Don't refetch when app is in background
    retry: 2, // Retry failed requests up to 2 times
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    ...options,
  });
}

// Historical Prices Hook
export function useHistoricalPrices(symbol: string) {
  return useQuery({
    queryKey: FMP_KEYS.historical(symbol),
    queryFn: async () => {
      try {
        const response = await apiClient.get<HistoricalPricesResponse>(
          `/stocks/historical_prices?symbol=${symbol}`
        );
        return response.data;
      } catch (error) {
        console.error('API Error:', error);
        throw error;
      }
    },
  });
}

// Stock Outlook Hook
export function useStockOutlook(symbol: string) {
  return useQuery({
    queryKey: FMP_KEYS.outlook(symbol),
    queryFn: async () => {
      try {
        const response = await apiClient.get<OutlookResponse>(`/stocks/outlook?symbol=${symbol}`);
        return response.data;
      } catch (error) {
        console.error('API Error:', error);
        throw error;
      }
    },
  });
}
