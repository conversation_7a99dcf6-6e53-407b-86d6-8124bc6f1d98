import { apiClient, handleApiError } from '@/lib/api/client';
import { secureStore, SecureStoreKeys } from '@/lib/secure-store';
import {
  ChatUsageResponse,
  ChatHistoryResponse,
  ChatDetailsResponse,
  SubmitFeedbackRequest,
  SubmitFeedbackResponse,
  ChatHistoryParams,
} from '@/types/api/ai-chat';
import { useQuery } from '@tanstack/react-query';

import { fetch } from 'expo/fetch';

const showLogs = false;

// Query keys for AI Chat-related queries
export const AI_CHAT_KEYS = {
  all: ['ai-chat'] as const,
  usage: () => [...AI_CHAT_KEYS.all, 'usage'] as const,
  history: (params?: ChatHistoryParams) => [...AI_CHAT_KEYS.all, 'history', params] as const,
  chat: (chatId: string | undefined) => [...AI_CHAT_KEYS.all, 'chat', chatId || 'none'] as const,
} as const;

// Chat Usage Hook
export function useChatUsage() {
  return useQuery({
    queryKey: AI_CHAT_KEYS.usage(),
    queryFn: async () => {
      try {
        const response = await apiClient.get<ChatUsageResponse>('/chat/usage');
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
}

// Chat History Hook
export function useChatHistory(params?: ChatHistoryParams) {
  return useQuery({
    queryKey: AI_CHAT_KEYS.history(params),
    queryFn: async () => {
      try {
        const queryParams = new URLSearchParams();
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.offset) queryParams.append('offset', params.offset.toString());

        const url = `/chat/history${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await apiClient.get<ChatHistoryResponse>(url);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
}

// Chat Details Hook
export function useChatDetails(chatId: string | undefined) {
  return useQuery({
    queryKey: AI_CHAT_KEYS.chat(chatId),
    queryFn: async () => {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }
      try {
        const response = await apiClient.get<ChatDetailsResponse>(`/chat/${chatId}`);
        return response.data;
      } catch (error) {
        throw handleApiError(error);
      }
    },
    enabled: !!chatId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });
}

// Utility functions for chat operations (these will be used in mutations)

// Send chat message (streaming) - returns Response object for streaming
export async function sendChatMessage(data: {
  chatId?: string;
  message?: string;
  retryMessageId?: string;
  signal?: AbortSignal;
}) {
  try {
    // We use fetch instead of apiClient.post because this endpoint returns streaming data (SSE)
    // and apiClient/axios doesn't handle streaming responses well

    // Get the token directly from secure storage (same as apiClient interceptor does)
    const token = await secureStore.get(SecureStoreKeys.authToken);

    // Debug logging to check auth token
    if (showLogs) {
      console.log('🔐 Auth token:', token ? 'Present' : 'Missing');
      console.log('🌐 API URL:', `${apiClient.defaults.baseURL}/chat`);
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Platform': 'mobile', // Match the default headers from apiClient
    };

    // Add Authorization header if token is available
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    } else {
      console.warn('⚠️ No auth token found in secure storage');
    }

    if (showLogs) {
      console.log('📤 Request headers:', headers);
    }

    // Extract signal from data to pass to fetch
    const { signal, ...requestData } = data;

    if (showLogs) {
      console.log('📤 Abort signal provided:', !!signal);
      console.log('📤 Abort signal aborted:', signal?.aborted);
      console.log('📤 Request body:', JSON.stringify(requestData));
    }

    const response = await fetch(`${apiClient.defaults.baseURL}/chat`, {
      method: 'POST',
      headers: {
        ...headers,
        Accept: 'text/event-stream',
      },
      body: JSON.stringify(requestData),
      signal: signal, // Pass the AbortSignal to fetch
    });

    if (showLogs) {
      console.log('📥 Response status:', response.status);
      console.log('📥 Abort signal after fetch:', signal?.aborted);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));
    }

    if (!response.ok) {
      // Try to read error response
      try {
        const errorText = await response.text();
        if (showLogs) {
          console.log('📥 Error response body:', errorText);
        }
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      } catch (readError) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }

    // Don't check response.body here - React Native's fetch doesn't always populate it
    // for streaming responses, but response.text() still works
    if (showLogs) {
      console.log('📥 Response ready for processing');
    }

    // Check if the response is actually a streaming response
    const contentType = response.headers.get('content-type');
    if (showLogs) {
      console.log('📥 Content-Type:', contentType);
    }

    return response;
  } catch (error) {
    if (showLogs) {
      console.error('🚫 sendChatMessage error:', error);
    }
    throw handleApiError(error);
  }
}

// Submit feedback
export async function submitChatFeedback(
  data: SubmitFeedbackRequest
): Promise<SubmitFeedbackResponse> {
  try {
    const response = await apiClient.post<SubmitFeedbackResponse>('/chat/feedback', data);
    return response.data;
  } catch (error) {
    throw handleApiError(error);
  }
}
