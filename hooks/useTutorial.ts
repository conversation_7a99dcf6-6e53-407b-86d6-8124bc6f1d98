import AsyncStorage from '@react-native-async-storage/async-storage';

import { useState, useEffect } from 'react';

import { InteractionManager } from 'react-native';

const TUTORIAL_COMPLETED_KEY = 'tutorial_completed';

export interface TutorialState {
  showTutorial: boolean;
  currentStep: number;
  completedTutorials: string[];
}

export const useTutorial = (tutorialId: string, totalSteps: number) => {
  const [state, setState] = useState<TutorialState>({
    showTutorial: false,
    currentStep: 0,
    completedTutorials: [],
  });
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Delay the initialization to ensure components are fully rendered
    const initTutorial = async () => {
      try {
        const completedTutorialsJson = await AsyncStorage.getItem(TUTORIAL_COMPLETED_KEY);
        const completedTutorials = completedTutorialsJson ? JSON.parse(completedTutorialsJson) : [];

        // Wait for UI to be ready before showing tutorial
        InteractionManager.runAfterInteractions(() => {
          // Add a small delay to ensure components are fully rendered
          setTimeout(() => {
            setState({
              showTutorial: !completedTutorials.includes(tutorialId),
              currentStep: 0,
              completedTutorials,
            });
            setIsInitialized(true);
          }, 500);
        });
      } catch (error) {
        console.error('Error checking tutorial status:', error);
        setIsInitialized(true);
      }
    };

    initTutorial();
  }, [tutorialId]);

  const startTutorial = () => {
    setState(prev => ({
      ...prev,
      showTutorial: true,
      currentStep: 0,
    }));
  };

  const nextStep = () => {
    if (state.currentStep < totalSteps - 1) {
      setState(prev => ({
        ...prev,
        currentStep: prev.currentStep + 1,
      }));
    }
  };

  const previousStep = () => {
    if (state.currentStep > 0) {
      setState(prev => ({
        ...prev,
        currentStep: prev.currentStep - 1,
      }));
    }
  };

  const closeTutorial = () => {
    setState(prev => ({
      ...prev,
      showTutorial: false,
    }));
  };

  const completeTutorial = async () => {
    try {
      const updatedCompletedTutorials = [...state.completedTutorials];
      if (!updatedCompletedTutorials.includes(tutorialId)) {
        updatedCompletedTutorials.push(tutorialId);
      }

      await AsyncStorage.setItem(TUTORIAL_COMPLETED_KEY, JSON.stringify(updatedCompletedTutorials));

      setState({
        showTutorial: false,
        currentStep: 0,
        completedTutorials: updatedCompletedTutorials,
      });
    } catch (error) {
      console.error('Error completing tutorial:', error);
    }
  };

  const resetTutorial = async () => {
    try {
      const updatedCompletedTutorials = state.completedTutorials.filter(id => id !== tutorialId);

      await AsyncStorage.setItem(TUTORIAL_COMPLETED_KEY, JSON.stringify(updatedCompletedTutorials));

      setState({
        showTutorial: true,
        currentStep: 0,
        completedTutorials: updatedCompletedTutorials,
      });
    } catch (error) {
      console.error('Error resetting tutorial:', error);
    }
  };

  return {
    showTutorial: state.showTutorial && isInitialized,
    currentStep: state.currentStep,
    startTutorial,
    nextStep,
    previousStep,
    closeTutorial,
    completeTutorial,
    resetTutorial,
  };
};
