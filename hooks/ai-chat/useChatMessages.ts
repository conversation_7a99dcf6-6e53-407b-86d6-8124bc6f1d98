import { makeApiCall, processStreamingResponse } from '@/utils/ai-chat/apiHelpers';
import {
  Message,
  addNewAssistantMessage,
  addErrorMessage,
  hideAllFollowUpQuestions,
  createNewResponseSlot,
  updateCurrentResponseSlot,
  findCorrespondingUserMessage,
} from '@/utils/ai-chat/messageHelpers';
import { useTranslation } from 'react-i18next';

import { useState, useRef, useEffect } from 'react';

import { useChatUsage, useChatDetails } from '@/hooks/useAIChat';
import { useAuth } from '@/hooks/useAuth';

export const useChatMessages = (currentChatId: string | null, refetchHistory?: () => void) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState('');
  const [isTryingAgain, setIsTryingAgain] = useState(false);
  const [retryingMessageIndex, setRetryingMessageIndex] = useState<number | null>(null);
  const [isRequestActive, setIsRequestActive] = useState(false);

  // AbortController for cancelling ongoing API requests
  const abortControllerRef = useRef<AbortController | null>(null);

  // Get usage data
  const { data: usageData, refetch: refetchUsage } = useChatUsage();
  const {
    data: chatDetailsData,
    isLoading: isChatDetailsLoading,
    refetch: refetchChatDetails,
  } = useChatDetails(currentChatId || undefined);

  // Function to get daily usage limit based on user permissions
  const getDailyUsageLimit = (): number => {
    if (!user?.permissions) return 10;

    const hasPremiumPermission = user.permissions.some(
      (perm: any) => perm.permission === 'free_report_2024'
    );

    return hasPremiumPermission ? 20 : 10;
  };

  const dailyUsageCount = usageData?.data?.usage || 0;
  const dailyUsageLimit = usageData?.data?.limit || getDailyUsageLimit();

  const handleApiCall = async (userMessage: Message, showToast: (message: string) => void) => {
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController for this request
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    setIsRequestActive(true);

    try {
      // Check daily usage limit before making API call
      if (!user?.email) {
        throw new Error('User not authenticated');
      }

      // Use current usage from API data instead of local storage
      const currentUsage = dailyUsageCount;
      const userDailyLimit = dailyUsageLimit;

      // Check against the user's actual limit
      if (currentUsage >= userDailyLimit) {
        // Calculate hours until reset from API data
        const resetDateTime = usageData?.data?.resetDateTime;
        let hoursUntilReset = 24; // Default fallback

        if (resetDateTime) {
          const resetTime = new Date(resetDateTime);
          const now = new Date();
          const diffInMs = resetTime.getTime() - now.getTime();
          hoursUntilReset = Math.max(1, Math.ceil(diffInMs / (1000 * 60 * 60))); // At least 1 hour
        }

        const errorMessage = t('aiChat.messages.limitReached', {
          limit: userDailyLimit,
          current: currentUsage,
          hours: hoursUntilReset,
        });
        addErrorMessage(setMessages, errorMessage);
        setIsLoading(false);
        setStreamingMessage('');
        showToast(
          t('aiChat.messages.limitReachedShort', { current: currentUsage, limit: userDailyLimit })
        );
        return;
      }

      const response = await makeApiCall(
        userMessage,
        currentChatId || undefined,
        undefined,
        abortController.signal
      );

      // Check if request was aborted
      if (abortController.signal.aborted) {
        console.log('Request was aborted');
        return;
      }

      // API will handle usage increment server-side, so we just need to refetch usage
      refetchUsage();

      // Show usage info occasionally - calculate expected usage after this API call
      setTimeout(() => {
        // The API call we just made will increment usage by 1
        const expectedUsage = currentUsage + 1;
        if (expectedUsage >= userDailyLimit - 5 && expectedUsage < userDailyLimit) {
          showToast(
            t('aiChat.messages.usesRemaining', { remaining: userDailyLimit - expectedUsage })
          );
        }
      }, 500);

      const { content, metadata } = await processStreamingResponse(
        response,
        setStreamingMessage,
        abortController.signal
      );

      // Check if request was aborted after processing
      if (abortController.signal.aborted) {
        console.log('Request was aborted during processing');
        // If we have partial content, preserve it instead of showing error
        if (content) {
          addNewAssistantMessage(setMessages, content, metadata?.aiMessageId);
        }
        return;
      }

      if (content) {
        // Add the assistant message to component state
        addNewAssistantMessage(setMessages, content, metadata?.aiMessageId);

        // Refetch history to update the chat list
        if (refetchHistory) {
          refetchHistory();
        }

        // Return metadata for chat ID updates
        return metadata;
      }
    } catch (error) {
      // Check if error is due to abort
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was aborted');
        // Don't show error message for user-initiated cancellation
        return;
      }

      console.error('Error:', error);
      if (error instanceof Error && error.message === 'User not authenticated') {
        addErrorMessage(setMessages, t('aiChat.messages.authError'));
      } else {
        addErrorMessage(setMessages, t('aiChat.messages.error'));
      }
    } finally {
      // Clean up request state
      setIsRequestActive(false);
      if (abortControllerRef.current === abortController) {
        abortControllerRef.current = null;
      }
      setIsLoading(false);
      setStreamingMessage('');
    }
  };

  const sendMessage = async (showToast: (message: string) => void) => {
    if (!inputMessage.trim() || isLoading) return;

    // Hide follow-up questions from all messages
    hideAllFollowUpQuestions(setMessages);

    const userMessage: Message = {
      role: 'user',
      content: inputMessage.trim(),
    };

    // Add user message to state
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setStreamingMessage('');

    const metadata = await handleApiCall(userMessage, showToast);
    return metadata;
  };

  const handleQuestionSelect = async (question: string, showToast: (message: string) => void) => {
    setInputMessage(question);
    // Auto-submit the question
    return new Promise(resolve => {
      setTimeout(async () => {
        // Hide follow-up questions from all messages
        hideAllFollowUpQuestions(setMessages);

        const userMessage: Message = {
          role: 'user',
          content: question,
        };

        setMessages(prev => [...prev, userMessage]);
        setInputMessage('');
        setIsLoading(true);
        setStreamingMessage('');

        const metadata = await handleApiCall(userMessage, showToast);
        resolve(metadata);
      }, 100);
    });
  };

  const cancelRequest = () => {
    if (abortControllerRef.current) {
      console.log('User cancelled API request');
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
      setIsRequestActive(false);
      setIsLoading(false);
      setStreamingMessage('');
      setIsTryingAgain(false);
      setRetryingMessageIndex(null);
    }
  };

  return {
    messages,
    setMessages,
    inputMessage,
    setInputMessage,
    isLoading,
    setIsLoading,
    streamingMessage,
    setStreamingMessage,
    isTryingAgain,
    setIsTryingAgain,
    retryingMessageIndex,
    setRetryingMessageIndex,
    isRequestActive,
    setIsRequestActive,
    isChatDetailsLoading,
    dailyUsageCount,
    dailyUsageLimit,
    sendMessage,
    handleQuestionSelect,
    cancelRequest,
    handleApiCall,
    refetchUsage,
    refetchChatDetails,
    chatDetailsData,
  };
};
