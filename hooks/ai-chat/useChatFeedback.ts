import { ChatFeedback } from '@/types/api/ai-chat';
import { Message } from '@/utils/ai-chat/messageHelpers';
import { useTranslation } from 'react-i18next';

import { useState, useRef, useEffect } from 'react';

import { submitChatFeedback } from '@/hooks/useAIChat';

export const useChatFeedback = (
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  currentChatId: string | null,
  refetchChatDetails: () => void
) => {
  const { t } = useTranslation();

  // Debounce state for feedback operations
  const [pendingFeedback, setPendingFeedback] = useState<Set<string>>(new Set());
  const feedbackTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const handleFeedback = async (
    index: number,
    feedbackType: 'like' | 'dislike',
    showToast: (message: string) => void
  ) => {
    const message = messages[index];
    if (message.role !== 'assistant') return;

    const currentResponseIndex = message.currentResponseIndex || 0;
    const currentAiMessageId = message.aiMessageIds?.[currentResponseIndex] || message.aiMessageId;

    if (!currentAiMessageId) {
      console.warn('No AI message ID found for feedback');
      return;
    }

    // Create a unique key for this feedback operation
    const feedbackKey = `${currentAiMessageId}-${feedbackType}`;

    // Check if this feedback operation is already pending
    if (pendingFeedback.has(feedbackKey)) {
      console.log(`Feedback operation already pending for ${feedbackKey}`);
      return;
    }

    // Clear any existing timeout for this message and feedback type
    const existingTimeout = feedbackTimeoutRef.current.get(feedbackKey);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
      feedbackTimeoutRef.current.delete(feedbackKey);
    }

    // Get current feedback state
    const currentLiked = message.responseLiked?.[currentResponseIndex] || message.liked || false;
    const currentDisliked =
      message.responseDisliked?.[currentResponseIndex] || message.disliked || false;

    // Determine new states based on feedback type
    let newLikedState = currentLiked;
    let newDislikedState = currentDisliked;

    if (feedbackType === 'like') {
      newLikedState = !currentLiked;
      if (newLikedState) {
        newDislikedState = false; // Remove dislike when liked
      }
    } else {
      newDislikedState = !currentDisliked;
      if (newDislikedState) {
        newLikedState = false; // Remove like when disliked
      }
    }

    // Update UI state immediately for better UX
    setMessages(prev => {
      const newMessages = [...prev];
      if (newMessages[index].role === 'assistant') {
        const message = newMessages[index];
        const currentResponseIndex = message.currentResponseIndex || 0;

        // Initialize arrays if they don't exist
        if (!message.responseLiked) {
          message.responseLiked = new Array(message.responses?.length || 1).fill(false);
        }
        if (!message.responseDisliked) {
          message.responseDisliked = new Array(message.responses?.length || 1).fill(false);
        }

        // Update feedback for current response
        message.responseLiked[currentResponseIndex] = newLikedState;
        message.responseDisliked[currentResponseIndex] = newDislikedState;

        // Update main like/dislike to reflect current response
        message.liked = message.responseLiked[currentResponseIndex];
        message.disliked = message.responseDisliked[currentResponseIndex];
      }
      return newMessages;
    });

    // Mark this feedback operation as pending
    setPendingFeedback(prev => new Set([...prev, feedbackKey]));

    // Set up debounced API call
    const timeoutId = setTimeout(async () => {
      try {
        const shouldSubmitFeedback =
          (feedbackType === 'like' && newLikedState) ||
          (feedbackType === 'dislike' && newDislikedState);
        const shouldResetFeedback =
          (feedbackType === 'like' && !newLikedState && currentLiked) ||
          (feedbackType === 'dislike' && !newDislikedState && currentDisliked);

        if (shouldSubmitFeedback) {
          // User provided feedback
          await submitChatFeedback({
            messageId: currentAiMessageId,
            feedback: feedbackType === 'like' ? ChatFeedback.LIKE : ChatFeedback.DISLIKE,
          });

          if (currentChatId) {
            await refetchChatDetails();
          }
        } else if (shouldResetFeedback) {
          // User removed feedback - use RESET to clear both like and dislike
          await submitChatFeedback({
            messageId: currentAiMessageId,
            feedback: ChatFeedback.RESET,
          });
          if (currentChatId) {
            await refetchChatDetails();
          }
        } else {
          // No API call needed (no state change)
          console.log(`No feedback change for ${feedbackType}`);
        }
      } catch (error) {
        console.error(`Error submitting ${feedbackType} feedback:`, error);
        // Revert UI state on error
        setMessages(prev => {
          const newMessages = [...prev];
          if (newMessages[index].role === 'assistant') {
            const message = newMessages[index];
            const currentResponseIndex = message.currentResponseIndex || 0;

            if (feedbackType === 'like' && message.responseLiked) {
              message.responseLiked[currentResponseIndex] = !newLikedState;
              message.liked = message.responseLiked[currentResponseIndex];
            } else if (feedbackType === 'dislike' && message.responseDisliked) {
              message.responseDisliked[currentResponseIndex] = !newDislikedState;
              message.disliked = message.responseDisliked[currentResponseIndex];
            }
          }
          return newMessages;
        });
        showToast(t('aiChat.messages.feedbackError') || 'Failed to submit feedback');
      } finally {
        // Remove from pending operations and cleanup
        setPendingFeedback(prev => {
          const newSet = new Set(prev);
          newSet.delete(feedbackKey);
          return newSet;
        });
        feedbackTimeoutRef.current.delete(feedbackKey);
      }
    }, 500); // 500ms debounce delay

    // Store the timeout reference
    feedbackTimeoutRef.current.set(feedbackKey, timeoutId);
  };

  const handleLike = (index: number, showToast: (message: string) => void) =>
    handleFeedback(index, 'like', showToast);

  const handleDislike = (
    index: number,
    showToast: (message: string) => void,
    onShowFeedbackForm?: (messageIndex: number) => void
  ) => {
    const message = messages[index];
    if (message.role !== 'assistant') return;

    // Check if already disliked - if so, just toggle off
    const currentResponseIndex = message.currentResponseIndex || 0;
    const currentDisliked =
      message.responseDisliked?.[currentResponseIndex] || message.disliked || false;

    if (currentDisliked) {
      // Already disliked, just remove the dislike
      handleFeedback(index, 'dislike', showToast);
    } else {
      onShowFeedbackForm?.(index);
      handleFeedback(index, 'dislike', showToast);
    }
  };

  // Handle detailed feedback submission
  const handleDetailedFeedback = async (
    messageIndex: number,
    type: string,
    reasons: string[],
    comment: string,
    showToast: (message: string) => void
  ) => {
    const message = messages[messageIndex];
    if (message.role !== 'assistant') return;

    const currentResponseIndex = message.currentResponseIndex || 0;
    const currentAiMessageId = message.aiMessageIds?.[currentResponseIndex] || message.aiMessageId;

    if (!currentAiMessageId) {
      console.warn('No AI message ID found for detailed feedback');
      return;
    }

    // Update UI state to show dislike immediately
    setMessages(prev => {
      const newMessages = [...prev];
      if (newMessages[messageIndex].role === 'assistant') {
        const message = newMessages[messageIndex];
        const currentResponseIndex = message.currentResponseIndex || 0;

        // Initialize arrays if they don't exist
        if (!message.responseLiked) {
          message.responseLiked = new Array(message.responses?.length || 1).fill(false);
        }
        if (!message.responseDisliked) {
          message.responseDisliked = new Array(message.responses?.length || 1).fill(false);
        }

        // Set dislike state and remove like
        message.responseLiked[currentResponseIndex] = false;
        message.responseDisliked[currentResponseIndex] = true;
        message.liked = false;
        message.disliked = true;
      }
      return newMessages;
    });

    const feedbackKey = `${currentAiMessageId}-dislike`;
    setPendingFeedback(prev => new Set([...prev, feedbackKey]));

    try {
      // Submit detailed feedback to API
      // Note: The current API only supports basic feedback, but we could extend it
      // For now, we'll submit the basic dislike and log the detailed feedback
      await submitChatFeedback({
        messageId: currentAiMessageId,
        feedback: ChatFeedback.DISLIKE,
      });

      // Log detailed feedback for analytics (could be sent to a separate endpoint)
      console.log('Detailed feedback:', {
        type,
        messageId: currentAiMessageId,
        reasons,
        comment,
      });

      if (currentChatId) {
        await refetchChatDetails();
      }

      showToast(t('aiChat.messages.feedbackSubmitted') || 'Thank you for your feedback');
    } catch (error) {
      console.error('Error submitting detailed feedback:', error);

      // Revert UI state on error
      setMessages(prev => {
        const newMessages = [...prev];
        if (newMessages[messageIndex].role === 'assistant') {
          const message = newMessages[messageIndex];
          const currentResponseIndex = message.currentResponseIndex || 0;

          if (message.responseDisliked) {
            message.responseDisliked[currentResponseIndex] = false;
            message.disliked = false;
          }
        }
        return newMessages;
      });

      showToast(t('aiChat.messages.feedbackError') || 'Failed to submit feedback');
    } finally {
      setPendingFeedback(prev => {
        const newSet = new Set(prev);
        newSet.delete(feedbackKey);
        return newSet;
      });
    }
  };

  // Cleanup feedback timeouts on unmount
  useEffect(() => {
    return () => {
      // Clear all pending timeouts
      feedbackTimeoutRef.current.forEach(timeout => {
        clearTimeout(timeout);
      });
      feedbackTimeoutRef.current.clear();
    };
  }, []);

  return {
    pendingFeedback,
    handleLike,
    handleDislike,
    handleDetailedFeedback,
  };
};
