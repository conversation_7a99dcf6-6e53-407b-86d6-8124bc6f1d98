type TranslationFiles = {
  [key: string]: {
    [key: string]: any;
  };
};

const getTranslations = (lang: string) => {
  // Load auth translations separately to ensure they're not overwritten
  const authTranslations = (() => {
    switch (lang) {
      case 'en':
        return require('../languages/en/auth.json');
      case 'zh-TW':
        return require('../languages/zh-TW/auth.json');
      case 'ja':
        return require('../languages/ja/auth.json');
      case 'ko':
        return require('../languages/ko/auth.json');
      case 'th':
        return require('../languages/th/auth.json');
      case 'vi':
        return require('../languages/vi/auth.json');
      case 'ms':
        return require('../languages/ms/auth.json');
      case 'pt':
        return require('../languages/pt/auth.json');
      default:
        return require('../languages/en/auth.json');
    }
  })();

  // Load notification translations
  const notificationTranslations = (() => {
    try {
      switch (lang) {
        case 'en':
          return require('../languages/en/notifications.json');
        case 'zh-TW':
          return require('../languages/zh-TW/notifications.json');
        case 'ja':
          return require('../languages/ja/notifications.json');
        case 'ko':
          return require('../languages/ko/notifications.json');
        case 'th':
          return require('../languages/th/notifications.json');
        case 'vi':
          return require('../languages/vi/notifications.json');
        case 'ms':
          return require('../languages/ms/notifications.json');
        case 'pt':
          return require('../languages/pt/notifications.json');
        default:
          return require('../languages/en/notifications.json');
      }
    } catch (e) {
      // Fallback to English if language is not supported
      try {
        return require('../languages/en/notifications.json');
      } catch (e) {
        // Return empty object if notifications file doesn't exist
        return {};
      }
    }
  })();

  // Load tutorial translations
  const tutorialTranslations = (() => {
    try {
      switch (lang) {
        case 'en':
          return require('../languages/en/tutorial.json');
        case 'zh-TW':
          return require('../languages/zh-TW/tutorial.json');
        case 'ja':
          return require('../languages/ja/tutorial.json');
        case 'ko':
          return require('../languages/ko/tutorial.json');
        case 'th':
          return require('../languages/th/tutorial.json');
        case 'vi':
          return require('../languages/vi/tutorial.json');
        case 'ms':
          return require('../languages/ms/tutorial.json');
        case 'pt':
          return require('../languages/pt/tutorial.json');
        default:
          return require('../languages/en/tutorial.json');
      }
    } catch (e) {
      // Fallback to English if language is not supported
      try {
        return require('../languages/en/tutorial.json');
      } catch (e) {
        // Return empty object if tutorial file doesn't exist
        return {};
      }
    }
  })();

  // Load other translations
  const otherTranslations = (() => {
    switch (lang) {
      case 'en':
        return {
          ...require('../languages/en/common.json'),
          ...require('../languages/en/stock.json'),
          ...require('../languages/en/ai-analysis.json'),
          ...require('../languages/en/ai-chat.json'),
          ...require('../languages/en/setting.json'),
          ...require('../languages/en/report.json'),
          ...require('../languages/en/error.json'),
        };
      case 'zh-TW':
        return {
          ...require('../languages/zh-TW/common.json'),
          ...require('../languages/zh-TW/stock.json'),
          ...require('../languages/zh-TW/ai-analysis.json'),
          ...require('../languages/zh-TW/ai-chat.json'),
          ...require('../languages/zh-TW/setting.json'),
          ...require('../languages/zh-TW/report.json'),
          ...require('../languages/zh-TW/error.json'),
        };
      case 'ja':
        return {
          ...require('../languages/ja/common.json'),
          ...require('../languages/ja/stock.json'),
          ...require('../languages/ja/ai-analysis.json'),
          ...require('../languages/ja/ai-chat.json'),
          ...require('../languages/ja/setting.json'),
          ...require('../languages/ja/report.json'),
          ...require('../languages/ja/error.json'),
        };
      case 'ko':
        return {
          ...require('../languages/ko/common.json'),
          ...require('../languages/ko/stock.json'),
          ...require('../languages/ko/ai-analysis.json'),
          ...require('../languages/ko/ai-chat.json'),
          ...require('../languages/ko/setting.json'),
          ...require('../languages/ko/report.json'),
          ...require('../languages/ko/error.json'),
        };
      case 'th':
        return {
          ...require('../languages/th/common.json'),
          ...require('../languages/th/stock.json'),
          ...require('../languages/th/ai-analysis.json'),
          ...require('../languages/th/ai-chat.json'),
          ...require('../languages/th/setting.json'),
          ...require('../languages/th/report.json'),
          ...require('../languages/th/error.json'),
        };
      case 'vi':
        return {
          ...require('../languages/vi/common.json'),
          ...require('../languages/vi/stock.json'),
          ...require('../languages/vi/ai-analysis.json'),
          ...require('../languages/vi/ai-chat.json'),
          ...require('../languages/vi/setting.json'),
          ...require('../languages/vi/report.json'),
          ...require('../languages/vi/error.json'),
        };
      case 'ms':
        return {
          ...require('../languages/ms/common.json'),
          ...require('../languages/ms/stock.json'),
          ...require('../languages/ms/ai-analysis.json'),
          ...require('../languages/ms/ai-chat.json'),
          ...require('../languages/ms/setting.json'),
          ...require('../languages/ms/report.json'),
          ...require('../languages/ms/error.json'),
        };
      case 'pt':
        return {
          ...require('../languages/pt/common.json'),
          ...require('../languages/pt/stock.json'),
          ...require('../languages/pt/ai-analysis.json'),
          ...require('../languages/pt/ai-chat.json'),
          ...require('../languages/pt/setting.json'),
          ...require('../languages/pt/report.json'),
          ...require('../languages/pt/error.json'),
        };
      default:
        return {
          ...require('../languages/en/common.json'),
          ...require('../languages/en/stock.json'),
          ...require('../languages/en/ai-analysis.json'),
          ...require('../languages/en/ai-chat.json'),
          ...require('../languages/en/setting.json'),
          ...require('../languages/en/report.json'),
          ...require('../languages/en/error.json'),
        };
    }
  })();

  // Return merged translations with auth taking precedence
  return {
    ...otherTranslations,
    ...notificationTranslations,
    ...tutorialTranslations,
    ...authTranslations,
  };
};

const getUpdateLog = (lang: string) => {
  switch (lang) {
    case 'en':
      return require('../languages/en/update-log.json');
    case 'zh-TW':
      return require('../languages/zh-TW/update-log.json');
    case 'ja':
      return require('../languages/ja/update-log.json');
    case 'ko':
      return require('../languages/ko/update-log.json');
    case 'th':
      return require('../languages/th/update-log.json');
    case 'vi':
      return require('../languages/vi/update-log.json');
    case 'ms':
      return require('../languages/ms/update-log.json');
    case 'pt':
      return require('../languages/pt/update-log.json');
    default:
      return require('../languages/en/update-log.json');
  }
};

export const loadTranslations = () => {
  const languages: TranslationFiles = {};

  // Define supported languages
  const supportedLanguages = ['en', 'zh-TW', 'ja', 'ko', 'th', 'vi', 'ms', 'pt'];

  supportedLanguages.forEach(lang => {
    // Load translations
    const translations = getTranslations(lang);
    const updateLog = getUpdateLog(lang);

    // Create the language resource with proper namespacing
    languages[lang] = {
      translation: translations,
      'update-log': updateLog,
    };
  });

  return languages;
};
