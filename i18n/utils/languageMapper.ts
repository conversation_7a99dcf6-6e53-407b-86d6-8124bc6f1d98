import { LANGUAGES, Language } from '../index';

type LanguagePattern = {
  pattern: RegExp;
  language: Language;
};

/**
 * Language mapping patterns in order of precedence.
 * Examples of matched locales are provided in comments.
 */
const languagePatterns: LanguagePattern[] = [
  // Exact matches first (for any special cases that need exact matching)
  {
    pattern: /^zh-TW$/i,
    language: LANGUAGES['zh-TW'],
  },

  // Traditional Chinese regions
  // Matches: zh-TW (Taiwan), zh-HK (Hong Kong), zh-MO (Macau)
  {
    pattern: /^zh[-_](tw|hk|mo)$/i,
    language: LANGUAGES['zh-TW'],
  },

  // All other Chinese variants default to Traditional Chinese
  // Matches: zh (Chinese), zh-CN (Simplified), zh-SG (Singapore), zh-MY (Malaysia)
  {
    pattern: /^zh(-.*)?$/i,
    language: LANGUAGES['zh-TW'],
  },

  // Japanese
  // Matches: ja, ja-JP
  {
    pattern: /^ja(-.*)?$/i,
    language: LANGUAGES.ja,
  },

  // Korean
  // Matches: ko, ko-KR
  {
    pattern: /^ko(-.*)?$/i,
    language: LANGUAGES.ko,
  },

  // Thai
  // Matches: th, th-TH
  {
    pattern: /^th(-.*)?$/i,
    language: LANGUAGES.th,
  },

  // Vietnamese
  // Matches: vi, vi-VN
  {
    pattern: /^vi(-.*)?$/i,
    language: LANGUAGES.vi,
  },

  // Malaysian/Malay
  // Matches: ms, ms-MY, ms-SG, ms-BN
  {
    pattern: /^(ms|msa|may)(-.*)?$/i,
    language: LANGUAGES.ms,
  },

  // Portuguese
  // Matches: pt, pt-BR, pt-PT, pt-AO, pt-MZ, etc.
  {
    pattern: /^pt(-.*)?$/i,
    language: LANGUAGES.pt,
  },

  // All English variants
  // Examples: en-US, en-GB, en-AU, en-CA, en-NZ, en-IE, en-ZA, en-IN, en-PH, en-SG
  {
    pattern: /^en(-.*)?$/i,
    language: LANGUAGES.en,
  },
];

/**
 * Maps a device locale to one of our supported languages
 * @param deviceLocale The device's locale string (e.g., 'en-US', 'zh-CN')
 * @returns A supported language code, defaulting to 'en' if no mapping is found
 */
export function mapDeviceLocaleToSupportedLanguage(deviceLocale: string): Language {
  // Normalize the locale string
  const normalizedLocale = deviceLocale.trim().toLowerCase();

  // Find the first matching pattern
  const match = languagePatterns.find(({ pattern }) => pattern.test(normalizedLocale));

  // Return the matched language or default to English
  return match?.language || LANGUAGES.en;
}
