export const LANGUAGES = {
  en: 'en',
  'zh-TW': 'zh-TW',
  ja: 'ja',
  ko: 'ko',
  th: 'th',
  vi: 'vi',
  ms: 'ms',
  pt: 'pt',
} as const;

export const LANGUAGE_OPTIONS = [
  { label: 'English', value: LANGUAGES.en },
  { label: '繁體中文', value: LANGUAGES['zh-TW'] },
  { label: '日本語', value: LANGUAGES.ja },
  { label: '한국어', value: LANGUAGES.ko },
  { label: 'ไทย', value: LANGUAGES.th },
  { label: 'Tiếng Việt', value: LANGUAGES.vi },
  { label: 'Bahasa Melayu', value: LANGUAGES.ms },
  { label: 'Português', value: LANGUAGES.pt },
];

export const REPORT_LANGUAGE_OPTIONS = [
  { label: 'English', value: LANGUAGES.en },
  { label: '繁體中文', value: LANGUAGES['zh-TW'] },
  { label: '日本語', value: LANGUAGES.ja },
  { label: '한국어', value: LANGUAGES.ko },
  { label: 'ไทย', value: LANGUAGES.th },
  { label: 'Tiếng Việt', value: LANGUAGES.vi },
  { label: 'Bahasa Melayu', value: LANGUAGES.ms },
  { label: 'Português', value: LANGUAGES.pt },
];

export type Language = (typeof LANGUAGES)[keyof typeof LANGUAGES];
