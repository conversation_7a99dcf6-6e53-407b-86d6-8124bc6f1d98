{"aiAnalysis": {"common": {"featureDescription": "기능 설명"}, "finsAI": {"description": "FinSAI의 기업 주가에 대한 관점 분석.", "overview": {"label": "FinS AI", "title": "개요", "content": "개요", "volume": "거래량"}, "perspective": {"label": "FinS AI", "title": "관점", "keyPoints": "주요 포인트", "buySignal": "매수 신호", "sellSignal": "매도 신호"}}, "targetPrice": {"description": "다양한 평가 방법에 기반한 기업의 목표가 분석.", "overview": {"label": "목표가", "title": "개요", "summary": "요약", "opinion": "의견"}, "analysis": {"label": "목표가", "title": "분석", "point": "포인트 {{number}}"}, "prediction": {"label": "목표가", "title": "예측", "shortTerm": "단기", "longTerm": "장기", "summary": "요약"}}, "industry": {"description": "기업의 산업 성과와 트렌드 분석.", "overview": {"label": "주식", "title": "개요", "summary": "요약"}, "analysis": {"label": "주식", "title": "산업 분석", "performance": "성과", "difference": "차이", "score": "점수", "overview": "개요"}, "trend": {"label": "주식", "title": "기술적 분석", "rsi": "RSI", "williamsR": "윌리엄스 %R", "adx": "ADX", "standardDeviation": "표준편차"}, "industryOverview": {"label": "산업", "title": "개요", "forecast": "전망"}, "marketComparison": {"label": "산업", "title": "시장 비교", "performance": "성과", "difference": "차이", "score": "점수", "overview": "개요"}, "technicalAnalysis": {"label": "산업", "title": "기술적 분석", "rsi": "RSI", "williamsR": "윌리엄스 %R", "adx": "ADX", "standardDeviation": "표준편차"}}, "news": {"description": "주요 뉴스와 기업에 미치는 영향 분석.", "list": {"label": "뉴스", "title": "최신 업데이트", "noNews": "뉴스가 없습니다"}, "overview": {"label": "뉴스", "title": "개요", "summary": "요약", "opinion": "의견"}, "analysis": {"label": "뉴스", "title": "분석", "point": "포인트 {{number}}"}, "sentiment": {"label": "뉴스", "title": "센티먼트", "shortTerm": "단기", "longTerm": "장기", "summary": "요약"}}, "financials": {"description": "지난 10분기의 재무제표를 기반으로 기업의 단기 재무 상태와 실적, 그리고 트렌드 분석을 예측합니다.", "overview": {"label": "재무", "title": "개요", "summary": "요약", "opinion": "의견"}, "analysis": {"label": "재무", "title": "분석", "point": "포인트 {{number}}"}, "trend": {"label": "트렌드", "title": "분석", "shortTerm": "단기", "longTerm": "장기", "summary": "요약"}}}}