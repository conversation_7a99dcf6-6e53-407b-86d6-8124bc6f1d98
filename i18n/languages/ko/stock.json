{"stock": {"usage": {"title": "주식 사용 현황", "remaining": {"label": "남은 조회 횟수", "format": "{{remaining}}/{{total}}"}, "reset": {"label": "초기화까지", "format": "{{hours}}시간 {{minutes}}분"}, "viewedStocks": {"title": "오늘 본 주식", "empty": "오늘 본 주식이 없습니다"}, "survey": {"title": "추가 기능 잠금 해제", "promotion": "간단한 설문조사에 응답하시면 일일 사용 제한이 확장됩니다.", "button": "설문조사 참여"}}, "watchlist": {"search": {"placeholder": "검색 (예: AAPL)", "title": "주식 목록", "noResults": "관련 주식이 없습니다", "showMore": "모두 보기...", "showLess": "접기..."}, "recentSearches": {"title": "최근 검색"}, "popular": {"title": "인기 종목"}, "list": {"title": "관심 목록", "messages": {"added": "관심 목록에 추가됨: {{symbol}}", "removed": "관심 목록에서 제거됨: {{symbol}}"}}, "empty": {"title": "관심 목록이 비어 있습니다", "description": "관심 있는 주식을 관심 목록에 추가하여 추적을 시작하세요.", "searchButton": "주식 검색"}}, "common": {"tabBar": {"overview": "개요", "finsAI": "FinS AI", "targetPrice": "목표가", "industry": "업계", "news": "뉴스", "financials": "재무제표", "chart": "차트"}}, "overview": {"description": "기본 회사 정보.", "loading": {"analyzing": "FinS AI가 주식 데이터를 분석하고 있습니다..."}, "market": {"label": "시장", "title": "통계"}, "statistics": {"marketCap": "시가총액", "peRatio": "PER", "eps": "EPS", "beta": "베타", "yearHigh": "52주 최고", "yearLow": "52주 최저", "fiftyDayAvg": "50일 평균", "twoHundredDayAvg": "200일 평균", "volume": "거래량", "avgVolume": "평균 거래량", "sharesOut": "발행주식수", "lastDiv": "최근 배당금", "open": "시가", "prevClose": "전일 종가", "dayHigh": "고가", "dayLow": "저가", "change": "변동", "price": "주가"}, "priceChart": {"label": "주식", "title": "주가 차트", "noData": "데이터가 없습니다", "loading": "로딩 중...", "error": {"title": "차트 로딩 실패", "message": "네트워크 연결을 확인하고 다시 시도해주세요"}}, "companyProfile": {"label": "기업", "title": "프로필", "ceo": "CEO", "country": "국가", "industry": "산업", "sector": "섹터", "employees": "직원 수", "exchange": "거래소", "description": "설명"}}, "exceedLimit": {"title": "일일 한도에 도달했습니다!", "description": "간단한 설문조사에 응답하시면 일일 조회 한도가 10개 종목으로 확장됩니다.", "backToHome": "홈으로 돌아가기"}, "chart": {"description": "기술적 분석 도구가 포함된 고급 인터랙티브 차트.", "interactive": {"label": "차트", "title": "인터랙티브 거래 차트"}, "analysis": {"label": "분석", "title": "차트 분석", "priceRange": "가격 범위", "volatility": "변동성", "avgVolume": "평균 거래량", "dataPoints": "데이터 포인트"}}}}