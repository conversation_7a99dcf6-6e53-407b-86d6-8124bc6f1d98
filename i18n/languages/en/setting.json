{"setting": {"account": {"title": "Account", "fillSurvey": "Fill in Survey (To access more features)", "changePassword": "Change Password", "moreSettings": "More Settings", "deleteAccount": "Delete Account"}, "helpAndSupport": {"title": "Help & Support", "aboutUs": "About us", "pricing": "Pricing Plans", "faq": "Frequently Asked Question", "userAgreement": "User Agreement", "privacyPolicy": "Privacy Policy", "onboarding": "Onboarding", "updateLog": "Update Log", "disclaimer": "Disclaimer", "contactUs": "Contact Us", "aiChat": "<PERSON><PERSON><PERSON>", "tutorials": "App Tutorials"}, "preferences": {"title": "Preferences", "language": "Language", "logout": "Logout"}, "others": {"title": "Others"}, "guest": "Guest", "language": {"title": "Language", "description": "Choose your preferred language for the app interface", "note": "Changes will be applied immediately"}, "about": {"version": "Version {{version}}", "title": "Your Smart Stock Analysis Assistant", "description": "FinSMarket is an app that specializes in providing information analysis for the US stock market. It offers users comprehensive and timely insights into various aspects of the US stock market, including but not limited to stock price trends, company financial data, and industry developments.", "contact": {"title": "Contact Us"}, "copyright": "© 2024-2025 Zentanet Limited. All rights reserved."}, "updateLog": {"title": "Update Log", "subtitle": "Check out the latest features and improvements", "latest": "Latest", "newFeatures": "New Features", "improvements": "Improvements", "fixes": "Fixes"}, "disclaimer": {"title": "Disclaimer", "content": "AI analysis content in this app is for reference only and does not constitute any distribution, subscription offer, or investment advice. FinSMarket shall not be responsible for any losses suffered by any person relying on such information, and shall not assume any liability for any direct or indirect consequential losses arising from the use of this document or any comments, opinions, or estimates contained therein. The information provided by our company is subject to change at any time without prior notice. Past performance may not be indicative of future results, and investments involve risks. Please seek professional advice if needed."}, "onboarding": {"skip": "<PERSON><PERSON>", "back": "Back", "next": "Next", "getStarted": "Get Started", "slides": [{"title": "Welcome to FinSMarket!", "description": "FinSMarket: A premier platform for US stock predictions and analysis. We leverage AI and big data to help investors understand market trends and revolutionize stock market forecasting."}, {"title": "Reasons for Choosing FinSMarket", "description": "Powerful Data & AI: Gathers data from top providers, uses AI for in-depth analysis, offering insights for better investment choices."}, {"title": "Reasons for Choosing FinSMarket", "description": "Versatile Services: Real-time system and weekly reports. Expanding to more markets, supports multiple languages."}, {"title": "Join us now!", "description": "Unleash the power of AI, increase your investment returns, and make investing simpler."}]}, "tutorials": {"title": "Tutorials", "description": "Start the app tutorials to learn about key features. Select a tutorial below to begin.", "start": "Start", "restart": "<PERSON><PERSON>", "started": {"title": "Tutorial Ready", "description": "The tutorial will begin in a moment."}, "error": {"title": "Error", "description": "There was a problem starting the tutorial. Please try again.", "startingFailed": "Error starting tutorial {{tutorialId}}"}, "status": {"completed": "Completed", "notCompleted": "Not Completed"}, "reset": {"title": "Reset Tutorials", "description": "This will reset all tutorial guides in the app. You'll see tutorials again the next time you visit each feature.", "button": "Reset All Tutorials", "success": "Tutorials have been reset successfully", "action": "Reset", "singleTitle": "Reset Tutorial", "singleDescription": "This will reset the {{tutorial}}. You'll see this tutorial again the next time you visit this feature.", "successDescription": "The {{tutorial}} has been reset successfully", "error": "Error", "errorDescription": "An error occurred while resetting the tutorial. Please try again."}, "names": {"watchlist": "Home Page Tutorial", "stock": "Stock Details Page Tutorial", "aiChat": "AI Chat Tutorial"}, "descriptions": {"watchlist": "Learn how to search, track, and manage your favorite stocks.", "stock": "Discover the detailed stock information and analysis features.", "aiChat": "Learn how to interact with <PERSON><PERSON><PERSON>, your AI assistant for stock insights and market analysis."}}, "contact": {"title": "Contact Us", "help": "Need help? We're here to assist you.", "description": "Choose one of the options below and we'll respond to your message as soon as possible.", "options": {"problem": {"title": "Report a Problem", "description": "Experiencing technical issues or errors?"}, "suggestion": {"title": "Provide Suggestions", "description": "Have ideas to improve our app?"}, "question": {"title": "Ask a Question", "description": "Have any questions? We're happy to help"}}, "form": {"title": "Contact Form", "email": "Email", "emailPlaceholder": "Your email address", "topic": "Topic", "topicPlaceholder": "Briefly describe your issue", "description": "Detailed Description", "descriptionPlaceholder": "Please describe your issue or suggestion in detail", "attachments": "Attachments", "addFile": "Add File", "addImage": "Add Image", "submit": "Submit", "fileSizeError": {"title": "File Too Large", "message": "File size cannot exceed 5MB"}, "error": {"title": "Error", "pickingFile": "Error picking file", "pickingImage": "Error picking image", "submission": "Error submitting form, please try again later"}, "validation": {"title": "Validation Error", "emailRequired": "Please enter your email", "topicRequired": "Please enter a topic", "descriptionRequired": "Please enter a description", "topicTooShort": "Topic must be at least 2 characters", "topicTooLong": "Topic must be less than 40 characters", "descriptionTooShort": "Description must be at least 20 characters", "descriptionTooLong": "Description must be less than 400 characters"}, "success": {"title": "Submission Successful", "message": "Thank you for your feedback, we will get back to you soon.", "pendingMessage": "Please confirm if you've sent the email", "draftMessage": "<PERSON><PERSON> saved as draft"}, "type": "Type", "selectType": "Select Type"}}, "pricing": {"title": "Pricing Plans", "header": {"title": "Unlock Powerful Features", "subtitle": "Advanced features to help you make smart decisions"}, "popularPlan": "Popular Plan", "currentPlan": "Current Plan", "comingSoon": "Coming Soon", "completeSurvey": "Complete Survey", "free": "Free", "seeBenefits": "See benefits", "plans": {"basic": {"name": "Registered User", "price": "Free", "description": "Basic features for stock analysis", "features": {"fundamental": "View company fundamental information (real-time)", "aiAnalysis4": "FinS AI stock analysis function (up to 4 stocks/day)", "tuesdayReport": "Weekly potential stock selection report on Tuesday (4 per month)", "thursdayReport": "Weekly premium stock selection report on Thursday (4 per month)", "saturdayReport4": "Weekly stock analysis report on Saturday (4 per month, up to 4 stocks per week)", "aiChatLimit": "<PERSON><PERSON><PERSON> (AI Chat) usage up to 10 questions/day"}}, "advanced": {"name": "Registered User (Completed Survey)", "price": "Free", "description": "Advanced features with completed survey", "features": {"allBasic": "All basic features included", "aiAnalysis10": "FinS AI stock analysis function (up to 10 stocks/day)", "saturdayReport6": "Weekly stock analysis report on Saturday (4 per month, up to 6 stocks per week)", "manageReports": "Manage Saturday subscription reports (keep 3 months of records)", "aiChatLimit": "<PERSON><PERSON><PERSON> (AI Chat) usage up to 20 questions/day"}}, "premium": {"name": "Premium User", "price": "Coming Soon", "description": "Premium features for advanced users", "features": {"allAdvanced": "All advanced features included", "aiAnalysisUnlimited": "FinS AI stock analysis function (unlimited access)", "saturdayReport20": "Weekly stock analysis report on Saturday (up to 20 stocks per week)", "portfolioTool": "FinS AI investment portfolio tool", "targetPrice": "Stock target price notice", "priorityAccess": "Priority access to new features"}}}, "footer": {"note": "All features are subject to terms and conditions. Survey completion required for advanced features.", "disclaimer": "Pricing and features may change without notice."}}}}