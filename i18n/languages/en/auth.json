{"auth": {"shared": {"googleButton": "Sign in with Google", "appleButton": "Sign in with Apple", "orContinueWith": "or continue with email", "email": "Email", "password": "Password"}, "login": {"title": "Welcome Back", "forgotPassword": "Forgot Password?", "signIn": "Sign In", "noAccount": "Don't have an account? Sign Up", "agreement": {"checkbox": "I have read and agree to", "userAgreement": "User Agreement", "and": "and", "privacyPolicy": "Privacy Policy", "modal": {"title": "User Agreement & Privacy Policy", "content": "Read and agree to User Agreement and Privacy Policy", "disagree": "Disagree", "agreeAndContinue": "Agree & Continue"}}, "forgotPasswordModal": {"title": "Forgot Password", "description": "Please enter your email address. We will send you a password reset link.", "emailLabel": "Email Address", "sendButton": "Send", "cooldown": "Please wait {{seconds}} seconds before trying again", "successMessage": "If {{email}} matches the email address of your account, we'll send you an email with password reset instructions"}}, "signup": {"title": "Create Account", "firstName": "First Name", "lastName": "Last Name", "confirmPassword": "Confirm Password", "createAccount": "Create Account", "hasAccount": "Already have an account? Sign In", "passwordRequirements": "Password must contain: (1) A lowercase letter; (2) A capital letter; (3) A number; (4) Minimal 6 characters"}, "email": {"title": "Verify Your Email", "logout": "Logout", "greeting": "Hello {{name}} 👋", "description": "Please verify your email address to continue using our service.", "emailSentMessage": "Verification email has been sent to {{email}}. Please check your inbox and click the refresh button once you verify.", "sendButton": "Send Verification Email", "resendButton": "Resend Verification Email", "resendTimer": "Resend in {{seconds}}s"}, "changePassword": {"title": "Change Password", "description": "Please enter your current password and choose a new password.", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "button": "Change Password", "validation": {"minLength": "Password must be at least 6 characters long", "passwordMatch": "New passwords do not match", "differentPassword": "New password must be different from current password"}, "success": "Your password has been successfully changed", "error": {"default": "Failed to change password"}}}}