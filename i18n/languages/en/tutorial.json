{"tutorial": {"common": {"next": "Next", "previous": "Previous", "finish": "Got it!", "skip": "<PERSON><PERSON>"}, "watchlist": {"search": {"title": "Search Stocks", "description": "Tap here to search for stocks by symbol or company name"}, "searchInput": {"title": "Search Input", "description": "Type stock symbols or company names here to find stocks you want to follow."}, "searchResults": {"title": "Search Results", "description": "Browse through matching stocks and tap on any stock to view more details or follow it."}, "watchlist": {"title": "Your Watchlist", "description": "Stocks you follow will appear here. Monitor their price movements at a glance."}, "stockUsage": {"title": "FinSAI Button", "description": "Tap to chat with <PERSON><PERSON><PERSON> for stock insights, or long press to view your daily usage details. The progress ring shows your remaining usage."}}, "stock": {"stockInfo": {"title": "Stock Information", "description": "View the current stock price, daily change percentage, and price movement at a glance."}, "favorite": {"title": "Favorite <PERSON><PERSON>", "description": "Tap the heart icon to add this stock to your watchlist or remove it."}, "tabs": {"title": "Detail Tabs", "description": "Swipe or tap these tabs to explore different aspects of the stock including charts, news, and financial information."}, "content": {"title": "Stock Content", "description": "Each tab contains detailed information about the stock, including overview, financials, and more."}}, "aiChat": {"welcome": {"title": "<PERSON>", "description": "This is <PERSON><PERSON><PERSON>, your AI assistant for stock analysis and market insights. Click on any suggested question to start a conversation."}, "input": {"title": "Chat Input", "description": "Type your questions about stocks, markets, or investing here. <PERSON><PERSON><PERSON> can help with analysis, trends, and investment advice."}, "chatHistory": {"title": "Chat History", "description": "Access your previous conversations with <PERSON><PERSON><PERSON>. View past insights and continue where you left off."}, "chatHistorySection": {"title": "Chat Management", "description": "Start a new conversation with the 'New Chat' button at the top, or browse through your previous conversations below to resume any chat."}, "dailyUsage": {"title": "Daily Usage", "description": "Monitor your daily chat usage with <PERSON><PERSON><PERSON>. Keep track of how many conversations you have left today."}}}}