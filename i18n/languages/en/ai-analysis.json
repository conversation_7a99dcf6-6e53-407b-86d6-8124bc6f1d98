{"aiAnalysis": {"common": {"featureDescription": "Feature Description"}, "finsAI": {"description": "FinSAI's perspective analysis on the company's stock price.", "overview": {"label": "FinS AI", "title": "Overview", "content": "Overview", "volume": "Volume"}, "perspective": {"label": "FinS AI", "title": "Perspective", "keyPoints": "Key Points", "buySignal": "Buy Signal", "sellSignal": "<PERSON><PERSON>"}}, "targetPrice": {"description": "Analysis of the company's target price based on various valuation methods.", "overview": {"label": "Target Price", "title": "Overview", "summary": "Summary", "opinion": "Opinion"}, "analysis": {"label": "Target Price", "title": "Analysis", "point": "Point {{number}}"}, "prediction": {"label": "Target Price", "title": "Prediction", "shortTerm": "Short Term", "longTerm": "Long Term", "summary": "Summary"}}, "industry": {"description": "Analysis of the company's industry performance and trends.", "overview": {"label": "Stock", "title": "Overview", "summary": "Summary"}, "analysis": {"label": "Stock", "title": "Industry Analysis", "performance": "Performance", "difference": "Difference", "score": "Score", "overview": "Overview"}, "trend": {"label": "Stock", "title": "Technical Analysis", "rsi": "RSI", "williamsR": "Williams %R", "adx": "ADX", "standardDeviation": "Standard Deviation"}, "industryOverview": {"label": "Industry", "title": "Overview", "forecast": "Forecast"}, "marketComparison": {"label": "Industry", "title": "Market Comparison", "performance": "Performance", "difference": "Difference", "score": "Score", "overview": "Overview"}, "technicalAnalysis": {"label": "Industry", "title": "Technical Analysis", "rsi": "RSI", "williamsR": "Williams %R", "adx": "ADX", "standardDeviation": "Standard Deviation"}}, "news": {"description": "Analysis of key news and their impact on the company.", "list": {"label": "News", "title": "Latest Updates", "noNews": "No news available"}, "overview": {"label": "News", "title": "Overview", "summary": "Summary", "opinion": "Opinion"}, "analysis": {"label": "News", "title": "Analysis", "point": "Point {{number}}"}, "sentiment": {"label": "News", "title": "Sentiment", "shortTerm": "Short Term", "longTerm": "Long Term", "summary": "Summary"}}, "financials": {"description": "Based on the financial statements of the past 10 quarters, predict the company's short-term financial condition and performance, as well as trend analysis.", "overview": {"label": "Financial", "title": "Overview", "summary": "Summary", "opinion": "Opinion"}, "analysis": {"label": "Financial", "title": "Analysis", "point": "Point {{number}}"}, "trend": {"label": "Trend", "title": "Analysis", "shortTerm": "Short Term", "longTerm": "Long Term", "summary": "Summary"}}}}