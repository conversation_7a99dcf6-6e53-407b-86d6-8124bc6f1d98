{"auth": {"shared": {"googleButton": "Entrar com Google", "appleButton": "Entrar com Apple", "orContinueWith": "ou continue com e-mail", "email": "E-mail", "password": "<PERSON><PERSON>"}, "login": {"title": "Bem-vindo de volta", "forgotPassword": "Esque<PERSON>u a senha?", "signIn": "Entrar", "noAccount": "Não tem uma conta? Cadastre-se", "agreement": {"checkbox": "Li e concordo com os", "userAgreement": "Termos de Uso", "and": "e", "privacyPolicy": "Política de Privacidade", "modal": {"title": "Termos de Uso & Política de Privacidade", "content": "Leia e concorde com os Termos de Uso e Política de Privacidade", "disagree": "Discordo", "agreeAndContinue": "Concordo & Continuar"}}, "forgotPasswordModal": {"title": "Esqueceu a Senha", "description": "Por favor, digite seu e-mail. Enviaremos um link para redefinir sua senha.", "emailLabel": "Endereço de E-mail", "sendButton": "Enviar", "cooldown": "Por favor, aguarde {{seconds}} segundos antes de tentar novamente", "successMessage": "Se {{email}} corresponder ao e-mail da sua conta, enviaremos instruções para redefinir sua senha"}}, "signup": {"title": "<PERSON><PERSON><PERSON>", "firstName": "Nome", "lastName": "Sobrenome", "confirmPassword": "Confirmar <PERSON>", "createAccount": "<PERSON><PERSON><PERSON>", "hasAccount": "Já tem uma conta? Entrar", "passwordRequirements": "A senha deve conter: (1) Uma letra minúscula; (2) Uma letra maiúscula; (3) Um número; (4) Mínimo de 6 caracteres"}, "email": {"title": "Verificar Seu E-mail", "logout": "<PERSON><PERSON>", "greeting": "<PERSON><PERSON><PERSON> {{name}} 👋", "description": "Por favor, verifique seu endereço de e-mail para continuar usando nosso serviço.", "emailSentMessage": "E-mail de verificação enviado para {{email}}. Por favor, verifique sua caixa de entrada e clique no botão de atualizar após a verificação.", "sendButton": "Enviar E-mail de Verificação", "resendButton": "Reenviar E-mail de Verificação", "resendTimer": "Reenviar em {{seconds}}s"}, "changePassword": {"title": "<PERSON><PERSON><PERSON>", "description": "Por favor, digite sua senha atual e escolha uma nova senha.", "currentPassword": "<PERSON><PERSON>", "newPassword": "Nova Senha", "confirmNewPassword": "Confirmar <PERSON>", "button": "<PERSON><PERSON><PERSON>", "validation": {"minLength": "A senha deve ter pelo menos 6 caracteres", "passwordMatch": "As novas senhas não coincidem", "differentPassword": "A nova senha deve ser diferente da senha atual"}, "success": "Sua senha foi alterada com sucesso", "error": {"default": "Falha ao alterar a senha"}}}}