import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';
import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';

import { createContext, useContext, useState, useEffect } from 'react';

import { LANGUAGES, Language } from './index';
import { mapDeviceLocaleToSupportedLanguage } from './utils/languageMapper';
import { loadTranslations } from './utils/loadTranslations';

type I18nContextType = {
  setLocale: (locale: Language) => Promise<void>;
  locale: Language | undefined;
};

const I18nContext = createContext<I18nContextType>({} as I18nContextType);

const LOCALE_KEY = 'finsmarket_locale';

const resources = loadTranslations();

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocale] = useState<Language>();
  const [isI18nInitialized, setIsI18nInitialized] = useState(false);

  const updateLocale = async (newLocale: Language) => {
    await AsyncStorage.setItem(LOCALE_KEY, newLocale);
    await i18next.changeLanguage(newLocale);
    setLocale(newLocale);
  };

  useEffect(() => {
    const initializeI18n = async () => {
      // Try to get stored locale
      const storedLocale = await AsyncStorage.getItem(LOCALE_KEY);
      // Get device locale
      const deviceLocale = Localization.getLocales()?.[0].languageTag;

      // Map the device locale to a supported language
      const mappedLocale = deviceLocale
        ? mapDeviceLocaleToSupportedLanguage(deviceLocale)
        : LANGUAGES.en;

      // Determine which locale to use (stored locale takes precedence)
      const initialLocale = storedLocale || mappedLocale;

      // Initialize i18next
      await i18next.use(initReactI18next).init({
        resources,
        lng: initialLocale,
        fallbackLng: LANGUAGES.en,
        interpolation: {
          escapeValue: false,
        },
        defaultNS: 'translation',
      });

      setLocale(initialLocale as Language);
      setIsI18nInitialized(true);
    };

    initializeI18n();
  }, []);

  if (!isI18nInitialized) {
    return null;
  }

  return (
    <I18nContext.Provider
      value={{
        setLocale: updateLocale,
        locale,
      }}>
      {children}
    </I18nContext.Provider>
  );
}

// Custom hook to use the i18n context
export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}
