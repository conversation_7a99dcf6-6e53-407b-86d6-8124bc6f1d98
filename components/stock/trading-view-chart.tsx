import type { HistoricalPricesResponse } from '@/types/api/fmp';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFocusEffect } from '@react-navigation/native';
import { zhTW, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import { WebView } from 'react-native-webview';

import React, { useState, useRef, useCallback, useEffect } from 'react';

import { AppState } from 'react-native';
import {
  View,
  Dimensions,
  ActivityIndicator,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

interface TradingViewChartProps {
  data: HistoricalPricesResponse;
  height?: number;
  timeRange?: string;
}

const TradingViewChart: React.FC<TradingViewChartProps> = ({
  data,
  height = 500,
  timeRange = '3M',
}) => {
  const { t, i18n } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [webViewKey, setWebViewKey] = useState(0); // Key to force WebView reload
  const webViewRef = useRef<WebView>(null);
  const [isChartLoaded, setIsChartLoaded] = useState(false);
  const screenWidth = Dimensions.get('window').width;
  const chartWidth = screenWidth;

  // Handle app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active' && isChartLoaded) {
        // When app becomes active, check if chart is still working instead of auto-reloading
        checkChartHealth();
      }
    });

    return () => subscription?.remove();
  }, [isChartLoaded]);

  // Handle screen focus
  useFocusEffect(
    useCallback(() => {
      // When screen comes into focus, check health instead of auto-reloading
      if (isChartLoaded && !isLoading) {
        checkChartHealth();
      } else if (!isChartLoaded && !isLoading && !hasError) {
        // Only reload if chart was never loaded and not currently loading
        handleReload();
      }
    }, [isChartLoaded, isLoading, hasError])
  );

  // Function to check if the chart is still functional
  const checkChartHealth = useCallback(() => {
    if (!webViewRef.current) return;

    // Inject a simple test to see if the chart is still responsive
    webViewRef.current.injectJavaScript(`
      try {
        // Check if the chart object exists and is functional
        if (typeof chart !== 'undefined' && chart && typeof chart.timeScale === 'function') {
          // Chart is still functional
          window.ReactNativeWebView.postMessage('health:ok');
        } else {
          // Chart is broken or missing
          window.ReactNativeWebView.postMessage('health:broken');
        }
      } catch (error) {
        // Chart is broken
        window.ReactNativeWebView.postMessage('health:broken');
      }
      true;
    `);
  }, []);

  // Function to reload the WebView
  const handleReload = useCallback(() => {
    setIsLoading(true);
    setHasError(false);
    setIsChartLoaded(false);
    setWebViewKey(prev => prev + 1); // Force WebView to reload
  }, []);

  // Get current locale for date formatting
  const currentLocale = i18n.language === 'zh-TW' ? zhTW : enUS;

  // Process data format - use all available data
  const chartData =
    data.historical && data.historical.length > 0
      ? data.historical.map(item => ({
          time: new Date(item.date).getTime() / 1000, // Convert to Unix timestamp (seconds)
          open: parseFloat(item.open.toString()),
          high: parseFloat(item.high.toString()),
          low: parseFloat(item.low.toString()),
          close: parseFloat(item.close.toString()),
          volume: parseFloat(item.volume.toString()),
          originalDate: item.date, // Keep original date for reference
        }))
      : [];

  // Sort data by date (oldest to newest)
  chartData.sort((a, b) => a.time - b.time);

  // Create HTML content
  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
        <style>
          body {
            margin: 0;
            padding: 0;
            background-color: white;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
          }
          #tv_chart_container {
            width: 100%;
            height: ${height}px;
            position: absolute;
            top: 0;
            left: 0;
          }
          #debug {
            display: none;
            padding: 5px;
            font-size: 10px;
            color: #666;
            background-color: #f5f5f5;
            border-top: 1px solid #ddd;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 100px;
            overflow-y: auto;
          }
        </style>
        <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
      </head>
      <body>
        <div id="tv_chart_container"></div>
        <div id="debug"></div>
        <script>
          // Debug function
          function log(message) {
            console.log(message);
            var debugEl = document.getElementById('debug');
            debugEl.textContent += message + '\\n';
            window.ReactNativeWebView.postMessage('debug:' + message);
          }
          
          try {
            // Data
            var chartData = ${JSON.stringify(chartData)};
            var symbol = "${data.symbol || ''}";
            var currentLocale = "${i18n.language}";
            var initialTimeRange = "${timeRange}";
            
            log("Data loaded: " + chartData.length + " points");
            log("Initial time range: " + initialTimeRange);
            
            // Helper function to find the first day of each month in the data
            function findFirstDaysOfMonths(data) {
              const firstDays = {};
              
              data.forEach(item => {
                const date = new Date(item.time * 1000);
                const year = date.getFullYear();
                const month = date.getMonth();
                const day = date.getDate();
                const key = year + "-" + month;
                
                if (!firstDays[key] || day < firstDays[key].day) {
                  firstDays[key] = { time: item.time, day: day };
                }
              });
              
              return firstDays;
            }
            
            // Convert data format
            var candleData = chartData.map(function(item) {
              return {
                time: item.time,
                open: item.open,
                high: item.high,
                low: item.low,
                close: item.close
              };
            });
            
            // Find the first day of each month
            var firstDaysOfMonths = findFirstDaysOfMonths(candleData);
            log("First days of months: " + JSON.stringify(firstDaysOfMonths));
            
            // Format time based on locale
            function formatTickTime(time, tickType) {
              const date = new Date(time * 1000);
              const month = date.getMonth();
              const year = date.getFullYear();
              const day = date.getDate();
              const key = year + "-" + month;
              
              // Month names based on locale
              const monthNames = {
                'en': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                'zh-TW': ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
              };
              
              const locale = currentLocale.startsWith('zh') ? 'zh-TW' : 'en';
              
              // Check if this is the first day of a month in our data
              const isFirstDayOfMonth = firstDaysOfMonths[key] && Math.abs(firstDaysOfMonths[key].time - time) < 86400;
              
              // Format based on tick type
              if (tickType === 'year') {
                // For year ticks, show the year
                return String(year);
              } else if (tickType === 'month') {
                // For month ticks, show month name
                // Special case: for January, show the year instead
                if (month === 0) {
                  return String(year);
                } else {
                  return monthNames[locale][month];
                }
              } else if (tickType === 'day') {
                // For day ticks, check if it's the first day of the month in our data
                if (isFirstDayOfMonth) {
                  // For the first day of the month, show the month name
                  if (month === 0) {
                    return String(year);
                  } else {
                    return monthNames[locale][month];
                  }
                } else {
                  // For other days, just show the day number
                  return String(day);
                }
              } else {
                // For any other tick type, determine based on the date
                if (isFirstDayOfMonth) {
                  if (month === 0) {
                    return String(year);
                  } else {
                    return monthNames[locale][month];
                  }
                } else {
                  // Otherwise show the day
                  return String(day);
                }
              }
            }
            
            var volumeData = chartData.map(function(item) {
              return {
                time: item.time,
                value: item.volume,
                color: item.close >= item.open ? 'rgba(38, 166, 154, 0.5)' : 'rgba(239, 83, 80, 0.5)'
              };
            });
            
            log("Data processed successfully");
            
            // Create chart
            var chart = LightweightCharts.createChart(document.getElementById('tv_chart_container'), {
              width: window.innerWidth,
              height: ${height},
              layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
                fontSize: 12,
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
              },
              grid: {
                vertLines: {
                  color: 'rgba(197, 203, 206, 0.5)',
                },
                horzLines: {
                  color: 'rgba(197, 203, 206, 0.5)',
                },
              },
              crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
              },
              rightPriceScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
                scaleMargins: {
                  top: 0.1,
                  bottom: 0.2,
                },
              },
              timeScale: {
                borderColor: 'rgba(197, 203, 206, 0.8)',
                timeVisible: true,
                secondsVisible: false,
                tickMarkFormatter: (time, tickType) => {
                  return formatTickTime(time, tickType);
                },
                fixLeftEdge: true,
                fixRightEdge: true,
                lockVisibleTimeRangeOnResize: true,
                rightBarStaysOnScroll: true,
                borderVisible: true,
                visible: true,
                minBarSpacing: 4,
                barSpacing: 6
              }
            });
            
            log("Chart created");
            
            // Add candlestick series
            var candleSeries = chart.addCandlestickSeries({
              upColor: '#26a69a',
              downColor: '#ef5350',
              borderDownColor: '#ef5350',
              borderUpColor: '#26a69a',
              wickDownColor: '#ef5350',
              wickUpColor: '#26a69a',
            });
            
            log("Candle series added");
            
            // Set data
            candleSeries.setData(candleData);
            log("Candle data set: " + candleData.length + " points");
            
            // Add volume series
            var volumeSeries = chart.addHistogramSeries({
              color: '#26a69a',
              priceFormat: {
                type: 'volume',
              },
              priceScaleId: '',
              scaleMargins: {
                top: 0.8,
                bottom: 0,
              },
            });
            
            volumeSeries.setData(volumeData);
            log("Volume data set: " + volumeData.length + " points");
            
            // Set visible range based on initial timeRange prop
            if (candleData.length > 0) {
              const lastTime = candleData[candleData.length - 1].time;
              
              // Calculate time range based on initial prop
              const timeRangeMap = {
                '1M': 30,
                '3M': 90,
                '6M': 180,
                '1Y': 365,
              };
              const days = timeRangeMap[initialTimeRange] || 90;
              const startTimeCalculated = lastTime - (days * 24 * 60 * 60);
              
              // Find the closest data point to the calculated start time
              let startTime = candleData[0].time;
              for (let i = 0; i < candleData.length; i++) {
                if (candleData[i].time >= startTimeCalculated) {
                  startTime = candleData[i].time;
                  break;
                }
              }
              
              var visibleRange = {
                from: startTime,
                to: lastTime
              };
              chart.timeScale().setVisibleRange(visibleRange);
              log("Initial visible range set for " + initialTimeRange + ": " + new Date(startTime * 1000).toISOString().split('T')[0] + " to " + new Date(lastTime * 1000).toISOString().split('T')[0]);
            }
            
            // Adaptive sizing
            window.addEventListener('resize', function() {
              chart.applyOptions({
                width: window.innerWidth,
                height: ${height}
              });
            });
            
            // Notify React Native loading complete
            log("Chart rendering complete");
            window.ReactNativeWebView.postMessage('loaded');
          } catch (error) {
            log("Error: " + error.message);
            window.ReactNativeWebView.postMessage('error:' + error.message);
          }
        </script>
      </body>
    </html>
  `;

  // Handle WebView messages
  const handleMessage = (event: any) => {
    const { data } = event.nativeEvent;

    if (data === 'loaded') {
      setIsLoading(false);
      setIsChartLoaded(true); // Set chart loaded to true when WebView reports loaded
    } else if (data.startsWith('debug:')) {
      setDebugInfo(prev => prev + data.substring(6) + '\n');
    } else if (data.startsWith('error:')) {
      setHasError(true);
      setIsLoading(false);
      setDebugInfo(prev => prev + 'ERROR: ' + data.substring(6) + '\n');
    } else if (data.startsWith('health:')) {
      if (data === 'health:ok') {
        // Chart is healthy, do nothing
      } else if (data === 'health:broken') {
        // Chart is broken, force reload
        handleReload();
      }
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.chartContainer, { width: chartWidth, height }]}>
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF9339" />
            <Text style={styles.loadingText}>{t('stock.overview.priceChart.loading')}</Text>
          </View>
        )}

        {hasError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{t('stock.overview.priceChart.error.title')}</Text>
            <Text style={styles.errorSubText}>{t('stock.overview.priceChart.error.message')}</Text>
            {debugInfo.length > 0 && (
              <View style={styles.debugContainer}>
                <Text style={styles.debugText}>{debugInfo}</Text>
              </View>
            )}
          </View>
        )}

        <WebView
          key={webViewKey} // Add key to force reload
          ref={webViewRef}
          source={{ html: htmlContent }}
          style={[styles.webView, { opacity: isLoading ? 0 : 1 }]}
          scrollEnabled={false}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          originWhitelist={['*']}
          onMessage={handleMessage}
          nestedScrollEnabled={true}
          onError={error => {
            console.error('WebView error:', error);
            setHasError(true);
            setIsLoading(false);
            setDebugInfo(
              prev => prev + 'WebView error: ' + JSON.stringify(error.nativeEvent) + '\n'
            );
          }}
          onHttpError={error => {
            console.error('WebView HTTP error:', error);
            setHasError(true);
            setIsLoading(false);
            setDebugInfo(prev => prev + 'HTTP error: ' + JSON.stringify(error.nativeEvent) + '\n');
          }}
          scalesPageToFit={false}
          bounces={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    width: '100%',
  },
  header: {
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderWidth: 1,
    borderBottomWidth: 0,
    borderColor: '#e0e0e0',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  chartContainer: {
    overflow: 'hidden',
  },
  loadingContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    zIndex: 1,
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  errorContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  errorText: {
    color: '#ff0000',
  },
  errorSubText: {
    marginTop: 5,
    color: '#666',
    fontSize: 12,
  },
  webView: {
    width: '100%',
    height: '100%',
  },
  debugContainer: {
    marginTop: 10,
    padding: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    maxWidth: '90%',
    maxHeight: 150,
  },
  debugText: {
    fontSize: 10,
    color: '#333',
  },
});

export default TradingViewChart;
