import { FollowedStock } from '@/stores/stock';
import { useStockStore } from '@/stores/stock';
import { isMarketOpen } from '@/utils/stock-market';
import { Link } from 'expo-router';

import { Text, TouchableOpacity, View } from 'react-native';

import { useStockQuote } from '@/hooks/useFMP';

import Skeleton from '../common/Skeleteton';

type WatchListItemProps = {
  stock: FollowedStock;
  index: number;
};

type PriceChangeSkeletonProps = {
  className?: string;
};

const PriceChangeSkeleton = ({ className }: PriceChangeSkeletonProps) => {
  return (
    <View className={`mt-1 flex-row items-center gap-1.5 ${className}`}>
      <Skeleton className="h-3 w-14" />
      <Skeleton className="h-3 w-14" />
    </View>
  );
};

export default function WatchListItem({ stock, index }: WatchListItemProps) {
  const stockData = useStockStore(state => state.getStock(stock.symbol));
  const marketOpen = isMarketOpen();

  // Check if we should use store data
  const useStoreData = marketOpen && stockData && stockData.lastPrice > 0;

  // Fetch FMP quote when market is closed or store data is not available
  // Enhanced with better refresh strategies
  const {
    data: fmpQuote,
    isLoading: fmpLoading,
    isFetching: fmpFetching,
  } = useStockQuote(stock.symbol, {
    enabled: !useStoreData,
  });

  let currentPrice: number;
  let priceChange: number;
  let priceChangePercentage: number;
  let isLoading: boolean;
  let isFetching: boolean = false;

  if (useStoreData) {
    // Use store data when market is open
    currentPrice = stockData.lastPrice;
    priceChange = stockData.lastClosePrice ? stockData.lastPrice - stockData.lastClosePrice : 0;
    priceChangePercentage = stockData.lastClosePrice
      ? ((stockData.lastPrice - stockData.lastClosePrice) / stockData.lastClosePrice) * 100
      : 0;
    isLoading = false;
  } else {
    // Use FMP data when market is closed or store data is not available
    currentPrice = fmpQuote?.price || 0;
    priceChange = fmpQuote?.change || 0;
    priceChangePercentage = fmpQuote?.changesPercentage || 0;
    isLoading = fmpLoading || !fmpQuote;
    isFetching = fmpFetching;
  }

  return (
    <Link
      href={`/stock/${stock.symbol}/overview`}
      className={`border-b border-zinc-200 bg-white ${index === 0 ? 'border-t' : ''}`}
      asChild>
      <TouchableOpacity
        className="flex-row items-center justify-between gap-8 p-4 active:bg-zinc-50"
        activeOpacity={0.5}>
        <View className="flex-1">
          <Text className="text-lg font-bold text-zinc-900">{stock.symbol || 'NULL'}</Text>
          <Text numberOfLines={1} className="mt-0.5 text-sm text-zinc-600">
            {stock.name || 'NULL'}
          </Text>
        </View>
        {isLoading ? (
          <View className="items-end gap-2">
            <Skeleton className="h-5 w-16" />
            <PriceChangeSkeleton />
          </View>
        ) : (
          <View className="items-end">
            <View className="flex-row items-center">
              <Text className="font-bold text-zinc-900">{currentPrice.toFixed(2)}</Text>
              {/* Show a subtle indicator when data is being refreshed */}
              {isFetching && <View className="ml-2 h-2 w-2 rounded-full bg-theme/80" />}
            </View>
            <View className="mt-1 flex-row items-center gap-1.5">
              <Text
                className={`text-xs ${priceChangePercentage >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                {priceChangePercentage >= 0 ? '+' : '-'}${Math.abs(priceChange).toFixed(2)}
              </Text>
              <View
                className={`flex-row items-center rounded-sm bg-opacity-10 px-2 py-0.5 ${priceChangePercentage >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                <Text
                  className={`text-xs font-semibold ${priceChangePercentage >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                  {priceChangePercentage >= 0 ? '↑' : '↓'}{' '}
                  {Math.abs(priceChangePercentage).toFixed(2)}%
                </Text>
              </View>
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Link>
  );
}
