import LottieView from 'lottie-react-native';
import { useTranslation } from 'react-i18next';

import { View, Text, Dimensions } from 'react-native';

interface StockLoadingProps {
  /**
   * Custom message key for translation. Defaults to 'stock.overview.loading.analyzing'
   */
  messageKey?: string;
  /**
   * Custom message text (overrides messageKey)
   */
  customMessage?: string;
  /**
   * Size of the animation as percentage of screen width. Defaults to 0.4 (40%)
   */
  animationSize?: number;
  /**
   * Show in fullscreen center (true) or inline (false). Defaults to true
   */
  fullscreen?: boolean;
  /**
   * Additional styling classes
   */
  className?: string;
}

export default function StockLoading({
  messageKey = 'stock.overview.loading.analyzing',
  customMessage,
  animationSize = 0.4,
  fullscreen = true,
  className = '',
}: StockLoadingProps) {
  const { t } = useTranslation();
  const screenWidth = Dimensions.get('window').width;
  const size = screenWidth * animationSize;

  const message = customMessage || t(messageKey);

  const containerClasses = fullscreen
    ? `flex-1 items-center justify-center ${className}`
    : `items-center justify-center py-8 ${className}`;

  return (
    <View className={containerClasses}>
      <View>
        <LottieView
          source={require('../../assets/lottie/loading-stock.json')}
          style={{
            width: size,
            height: size,
          }}
          autoPlay
          loop
          speed={1}
        />
      </View>
      <Text className="text-lg font-bold text-gray-600">{message}</Text>
    </View>
  );
}
