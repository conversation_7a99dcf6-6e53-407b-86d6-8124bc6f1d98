import { Stock } from '@/types/stock';
import { FontAwesome } from '@expo/vector-icons';

import { Image, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';

interface SearchStockItemProps {
  stock: Stock;
  isFollowed: boolean;
  onPress: (stock: Stock) => void;
  onToggleFollow: (stock: Stock) => void;
  imageErrors: { [key: string]: boolean };
  onImageError: (symbol: string) => void;
}

export default function SearchStockItem({
  stock,
  isFollowed,
  onPress,
  onToggleFollow,
  imageErrors,
  onImageError,
}: SearchStockItemProps) {
  const renderStockImage = () => {
    return (
      <View className="h-10 w-10 items-center justify-center overflow-hidden rounded-lg bg-gray-100 p-1.5">
        {imageErrors[stock.symbol] ? (
          <Text className="text-xs">{stock.symbol}</Text>
        ) : (
          <Image
            className="h-full w-full"
            source={{
              uri: `https://financialmodelingprep.com/image-stock/${stock.symbol}.png`,
            }}
            onError={() => onImageError(stock.symbol)}
          />
        )}
      </View>
    );
  };

  return (
    <View className="border-b border-zinc-200 bg-white">
      <TouchableWithoutFeedback onPress={() => onPress(stock)}>
        <View className="flex-row items-center px-4 py-2 pr-10">
          {renderStockImage()}
          <View className="mb-1 ml-4 mr-4 flex-1">
            <Text className="text-lg font-bold">{stock.symbol}</Text>
            <Text className="text-gray-600">{stock.name}</Text>
          </View>
          <TouchableOpacity className="flex-row items-center" onPress={() => onToggleFollow(stock)}>
            {isFollowed ? (
              <FontAwesome name="heart" size={24} color="red" />
            ) : (
              <FontAwesome name="heart-o" size={24} color="red" />
            )}
          </TouchableOpacity>
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
}
