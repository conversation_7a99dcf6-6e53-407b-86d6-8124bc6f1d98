import type { HistoricalPricesResponse } from '@/types/api/fmp';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFocusEffect } from '@react-navigation/native';
import { zhTW, enUS } from 'date-fns/locale';
import * as Haptics from 'expo-haptics';
import { useTranslation } from 'react-i18next';
import { WebView } from 'react-native-webview';

import React, { useState, useRef, useCallback, useEffect } from 'react';

import { AppState } from 'react-native';
import {
  View,
  Dimensions,
  ActivityIndicator,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

interface StockLineChartProps {
  data: HistoricalPricesResponse;
  height?: number;
  onViewDetailed?: () => void;
}

const StockLineChart: React.FC<StockLineChartProps> = ({ data, height = 300, onViewDetailed }) => {
  const { t, i18n } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [selectedTimeRange, setSelectedTimeRange] = useState('1M');
  const [webViewKey, setWebViewKey] = useState(0); // Key to force WebView reload
  const screenWidth = Dimensions.get('window').width;
  // Use full width minus padding for consistency
  const chartWidth = screenWidth - 32;
  const webViewRef = useRef<WebView>(null);
  const [isChartLoaded, setIsChartLoaded] = useState(false);

  // Handle app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active' && isChartLoaded) {
        // When app becomes active, check if chart is still working instead of auto-reloading
        checkChartHealth();
      }
    });

    return () => subscription?.remove();
  }, [isChartLoaded]);

  // Handle screen focus
  useFocusEffect(
    useCallback(() => {
      // When screen comes into focus, check health instead of auto-reloading
      if (isChartLoaded && !isLoading) {
        checkChartHealth();
      } else if (!isChartLoaded && !isLoading && !hasError) {
        // Only reload if chart was never loaded and not currently loading
        handleReload();
      }
    }, [isChartLoaded, isLoading, hasError])
  );

  // Function to check if the chart is still functional
  const checkChartHealth = useCallback(() => {
    if (!webViewRef.current) return;

    // Inject a simple test to see if the chart is still responsive
    webViewRef.current.injectJavaScript(`
      try {
        // Check if the chart object exists and is functional
        if (typeof chart !== 'undefined' && chart && typeof chart.timeScale === 'function') {
          // Chart is still functional
          window.ReactNativeWebView.postMessage('health:ok');
        } else {
          // Chart is broken or missing
          window.ReactNativeWebView.postMessage('health:broken');
        }
      } catch (error) {
        // Chart is broken
        window.ReactNativeWebView.postMessage('health:broken');
      }
      true;
    `);
  }, []);

  // Function to reload the WebView
  const handleReload = useCallback(() => {
    setIsLoading(true);
    setHasError(false);
    setIsChartLoaded(false);
    setWebViewKey(prev => prev + 1); // Force WebView to reload
  }, []);

  // Get current locale for date formatting
  const currentLocale = i18n.language === 'zh-TW' ? zhTW : enUS;

  // Process data format - use closing prices for line chart
  const chartData =
    data.historical && data.historical.length > 0
      ? data.historical.map(item => ({
          time: new Date(item.date).getTime() / 1000,
          value: parseFloat(item.close.toString()),
          originalDate: item.date,
        }))
      : [];

  // Sort data by date (oldest to newest)
  chartData.sort((a, b) => a.time - b.time);

  const handleTimeRangeChange = (range: string) => {
    // Add haptic feedback for time range change
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setSelectedTimeRange(range);
    if (webViewRef.current) {
      const days = {
        '5D': 5,
        '1M': 30,
        '3M': 90,
        '6M': 180,
        '1Y': 365,
      }[range];

      webViewRef.current.injectJavaScript(`
        try {
          if (typeof chart !== 'undefined') {
            const lastTime = chartData[chartData.length - 1].time;
            const startTime = lastTime - (${days} * 24 * 60 * 60);
            
            // Filter data points within the selected time range
            const filteredData = chartData.filter(item => item.time >= startTime);
            
            // Update the chart with filtered data
            areaSeries.setData(filteredData);
            
            // Set the visible range to show all filtered data
            chart.timeScale().setVisibleRange({
              from: filteredData[0].time,
              to: lastTime
            });
            
            // Force chart to fit content
            chart.timeScale().fitContent();
            
            // Ensure settings are applied
            chart.applyOptions({
              handleScroll: false,
              handleScale: false,
              timeScale: {
                fixLeftEdge: true,
                fixRightEdge: true,
                lockVisibleTimeRangeOnResize: true,
                rightOffset: 5,
              }
            });
          }
        } catch (error) {
          console.error('Time range change error:', error);
          window.ReactNativeWebView.postMessage('debug:' + error.message);
        }
        true;
      `);
    }
  };

  const handleDetailPress = () => {
    // Add haptic feedback for detail button press
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onViewDetailed?.();
  };

  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
        <style>
          body {
            margin: 0;
            padding: 0;
            background-color: white;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
          }
          #chart_container {
            width: 100%;
            height: ${height}px;
          }
        </style>
        <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
      </head>
      <body>
        <div id="chart_container"></div>
        <script>
          try {
            // Data
            var chartData = ${JSON.stringify(chartData)};
            var currentLocale = "${i18n.language}";
            
            // Helper function to find the first day of each month in the data
            function findFirstDaysOfMonths(data) {
              const firstDays = {};
              
              data.forEach(item => {
                const date = new Date(item.time * 1000);
                const year = date.getFullYear();
                const month = date.getMonth();
                const day = date.getDate();
                const key = year + "-" + month;
                
                if (!firstDays[key] || day < firstDays[key].day) {
                  firstDays[key] = { time: item.time, day: day };
                }
              });
              
              return firstDays;
            }
            
            // Find the first day of each month
            var firstDaysOfMonths = findFirstDaysOfMonths(chartData);
            
            // Format time based on locale
            function formatTickTime(time, tickType) {
              const date = new Date(time * 1000);
              const month = date.getMonth();
              const year = date.getFullYear();
              const day = date.getDate();
              const key = year + "-" + month;
              
              // Month names based on locale
              const monthNames = {
                'en': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                'zh-TW': ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
              };
              
              const locale = currentLocale.startsWith('zh') ? 'zh-TW' : 'en';
              
              // Check if this is the first day of a month in our data
              const isFirstDayOfMonth = firstDaysOfMonths[key] && Math.abs(firstDaysOfMonths[key].time - time) < 86400;
              
              // Format based on tick type
              if (tickType === 'year') {
                // For year ticks, show the year
                return String(year);
              } else if (tickType === 'month') {
                // For month ticks, show month name
                // Special case: for January, show the year instead
                if (month === 0) {
                  return String(year);
                } else {
                  return monthNames[locale][month];
                }
              } else if (tickType === 'day') {
                // For day ticks, check if it's the first day of the month in our data
                if (isFirstDayOfMonth) {
                  // For the first day of the month, show the month name
                  if (month === 0) {
                    return String(year);
                  } else {
                    return monthNames[locale][month];
                  }
                } else {
                  // For other days, just show the day number
                  return String(day);
                }
              } else {
                // For any other tick type, determine based on the date
                if (isFirstDayOfMonth) {
                  // For the first day of the month, show the month name
                  if (month === 0) {
                    return String(year);
                  } else {
                    return monthNames[locale][month];
                  }
                } else {
                  // For other days, just show the day number
                  return String(day);
                }
              }
            }
            
            // Create chart with fixed width to prevent shifting
            var chart = LightweightCharts.createChart(document.getElementById('chart_container'), {
              width: ${chartWidth},
              height: ${height},
              layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
              },
              grid: {
                vertLines: {
                  color: 'rgba(197, 203, 206, 0.2)',
                },
                horzLines: {
                  color: 'rgba(197, 203, 206, 0.2)',
                },
              },
              timeScale: {
                timeVisible: true,
                secondsVisible: false,
                borderColor: '#e0e0e0',
                tickMarkFormatter: formatTickTime,
              },
              rightPriceScale: {
                borderColor: '#e0e0e0',
              },
              // Add touch handling for haptic feedback
              handleScroll: false,
              handleScale: false,
            });
            
            // Create area series
            var areaSeries = chart.addAreaSeries({
              topColor: 'rgba(255, 107, 0, 0.56)',
              bottomColor: 'rgba(255, 107, 0, 0.04)',
              lineColor: 'rgba(255, 107, 0, 1)',
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 2,
                minMove: 0.01,
              },
            });
            
            // Add touch interaction handlers for haptic feedback
            var chartContainer = document.getElementById('chart_container');
            var isInteracting = false;
            var lastDataPoint = null;
            
            // Subscribe to crosshair move events to trigger haptic feedback only when data point changes
            chart.subscribeCrosshairMove(function(param) {
              if (param.time !== undefined && param.time !== null) {
                // User is hovering over a data point
                var currentDataPoint = param.time;
                
                // Trigger haptic feedback if this is a different data point than before
                // OR if this is the first data point selection (lastDataPoint is null)
                if (lastDataPoint === null || lastDataPoint !== currentDataPoint) {
                  lastDataPoint = currentDataPoint;
                  // Trigger haptic feedback when user selects a new data point (including first selection)
                  window.ReactNativeWebView.postMessage('haptic:selection');
                }
              } else {
                // User moved away from data points
                lastDataPoint = null;
              }
            });
            
            // Remove the aggressive touch event handlers that were causing issues
            // No longer need touchstart, touchend, or touchmove handlers for haptic feedback
            
            // Set chart data
            if (chartData.length > 0) {
              areaSeries.setData(chartData);
              
              // Set visible range to show last 30 days by default
              const lastTime = chartData[chartData.length - 1].time;
              const oneMonthAgo = lastTime - (30 * 24 * 60 * 60);
              
              const initialData = chartData.filter(item => item.time >= oneMonthAgo);
              areaSeries.setData(initialData);
              
              chart.timeScale().setVisibleRange({
                from: initialData[0].time,
                to: lastTime
              });
              
              // Force chart to fit content
              chart.timeScale().fitContent();
            }
            
            // Remove the resize handler that was causing issues
            // Don't resize the chart as it causes shifting
            
            // Notify React Native loading complete
            window.ReactNativeWebView.postMessage('loaded');
          } catch (error) {
            window.ReactNativeWebView.postMessage('error:' + error.message);
          }
        </script>
      </body>
    </html>
  `;

  // Handle WebView messages
  const handleMessage = (event: any) => {
    const { data } = event.nativeEvent;

    if (data === 'loaded') {
      setIsLoading(false);
      setIsChartLoaded(true);
    } else if (data === 'haptic:selection') {
      // Trigger selection haptic feedback for data point selection
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else if (data.startsWith('debug:')) {
      setDebugInfo(prev => prev + data.substring(6) + '\n');
    } else if (data.startsWith('error:')) {
      setHasError(true);
      setIsLoading(false);
      setDebugInfo(prev => prev + 'ERROR: ' + data.substring(6) + '\n');
    } else if (data.startsWith('health:')) {
      if (data === 'health:ok') {
        // Chart is healthy, do nothing
      } else if (data === 'health:broken') {
        // Chart is broken, force reload
        handleReload();
      }
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.chartContainer, { width: chartWidth, height: height + 40 }]}>
        <View style={styles.controlsContainer}>
          <View style={styles.timeRangeContainer}>
            {['5D', '1M', '3M', '6M', '1Y'].map(range => (
              <TouchableOpacity
                key={range}
                style={[
                  styles.timeRangeButton,
                  selectedTimeRange === range && styles.timeRangeButtonActive,
                ]}
                onPress={() => handleTimeRangeChange(range)}>
                <Text
                  style={[
                    styles.timeRangeButtonText,
                    selectedTimeRange === range && styles.timeRangeButtonTextActive,
                  ]}>
                  {range}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <TouchableOpacity style={styles.detailButton} onPress={handleDetailPress}>
            <FontAwesome name="bar-chart" size={16} color="#FF6B00" />
          </TouchableOpacity>
        </View>

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#FF6B00" />
            <Text style={styles.loadingText}>{t('stock.overview.priceChart.loading')}</Text>
          </View>
        )}

        {hasError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{t('stock.overview.priceChart.error.title')}</Text>
            <Text style={styles.errorSubText}>{t('stock.overview.priceChart.error.message')}</Text>
          </View>
        )}

        <WebView
          key={webViewKey} // Add key to force reload
          ref={webViewRef}
          source={{ html: htmlContent }}
          style={[styles.webView, { opacity: isLoading ? 0 : 1 }]}
          scrollEnabled={false}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          originWhitelist={['*']}
          onMessage={handleMessage}
          onError={() => {
            setHasError(true);
            setIsLoading(false);
          }}
          scalesPageToFit={false}
          bounces={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // Remove alignItems: 'center' to prevent shifting
    width: '100%',
  },
  chartContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#DDD',
    // Ensure the chart takes full width without centering
    alignSelf: 'stretch',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#DDD',
    height: 40,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  timeRangeButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: '#F3F4F6',
  },
  timeRangeButtonActive: {
    backgroundColor: '#FF6B00',
  },
  timeRangeButtonText: {
    fontSize: 12,
    color: '#6B7280',
  },
  timeRangeButtonTextActive: {
    color: 'white',
    fontWeight: '500',
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: '#FFF3E0',
  },
  detailButtonText: {
    fontSize: 12,
    color: '#FF6B00',
    fontWeight: '500',
  },
  loadingContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    zIndex: 1,
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  errorContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  errorText: {
    color: '#ff0000',
  },
  errorSubText: {
    marginTop: 5,
    color: '#666',
    fontSize: 12,
  },
  webView: {
    width: '100%',
    height: '100%',
  },
});

export default StockLineChart;
