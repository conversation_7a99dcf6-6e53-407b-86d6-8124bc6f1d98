import { useI18n } from '@/i18n/provider';
import notificationService from '@/lib/notification/notification.service';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTranslation } from 'react-i18next';

import React, { useEffect } from 'react';

import { useAuth } from '@/hooks/useAuth';
import { useReport } from '@/hooks/useReport';

const SHOW_LOGS = false;

export const ReportNotificationInitializer = () => {
  const { t } = useTranslation();
  const { locale } = useI18n();
  const { profile, isAuthenticated } = useAuth();
  const { reportData } = useReport();

  useEffect(() => {
    const setupNotifications = async () => {
      try {
        if (SHOW_LOGS) {
          console.log('[ReportNotifications] Initializing notifications...');
        }

        if (!isAuthenticated) {
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] User not authenticated, skipping initialization');
          }
          return;
        }

        if (!reportData) {
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] No report data available, skipping initialization');
          }
          return;
        }

        if (!locale) {
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] No locale available, skipping initialization');
          }
          return;
        }

        const { data } = reportData;
        const hasActiveSubscription = !!data?.user; // Check if user data exists to determine if subscription is active
        const userEmail = profile?.data?.email;

        if (!userEmail) {
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] No user email available, skipping initialization');
          }
          return;
        }

        if (SHOW_LOGS) {
          console.log('[ReportNotifications] Checking subscription status for user:', userEmail);
          console.log('[ReportNotifications] Current app language:', locale);
        }

        const notificationsSetupKey = `report_notifications_setup_${userEmail}`;

        if (hasActiveSubscription) {
          if (SHOW_LOGS) {
            console.log(
              '[ReportNotifications] User has active subscription, checking notification status'
            );
          }

          // Always check language first, regardless of setup status
          const languageCheck = await notificationService.checkNotificationLanguage(locale);
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] Language check result:', languageCheck);
          }

          const alreadySetup = await AsyncStorage.getItem(notificationsSetupKey);
          const notificationStatus = await notificationService.areAllNotificationsScheduled();

          // Determine if we need to schedule/reschedule notifications
          const needsSetup = !alreadySetup;
          const needsLanguageUpdate = languageCheck.needsUpdate;
          const needsReschedule = !notificationStatus.allScheduled;

          if (SHOW_LOGS) {
            console.log('[ReportNotifications] Setup status:', {
              needsSetup,
              needsLanguageUpdate,
              needsReschedule,
              alreadySetup: !!alreadySetup,
              allScheduled: notificationStatus.allScheduled,
            });
          }

          if (needsSetup || needsLanguageUpdate || needsReschedule) {
            // We need to set up or update notifications
            let reason = '';
            if (needsSetup) reason += 'First time setup. ';
            if (needsLanguageUpdate)
              reason += `Language change (${languageCheck.storedLanguage} → ${locale}). `;
            if (needsReschedule) reason += 'Missing notifications. ';

            if (SHOW_LOGS) {
              console.log(`[ReportNotifications] Scheduling notifications. Reason: ${reason}`);
            }

            // Check if we already have permissions
            const hasPermissions = await notificationService.areNotificationsEnabled();
            if (!hasPermissions) {
              const permissionsGranted = await notificationService.requestPermissions();
              if (!permissionsGranted) {
                if (SHOW_LOGS) {
                  console.log(
                    '[ReportNotifications] Notification permissions denied, skipping setup'
                  );
                }
                return;
              }
            }

            // Cancel all existing notifications first to ensure clean state
            if (SHOW_LOGS) {
              console.log('[ReportNotifications] Canceling any existing notifications...');
            }
            await notificationService.cancelAllReportNotifications();

            // Small delay to ensure cancellation operations are fully completed
            await new Promise(resolve => setTimeout(resolve, 500));

            // Schedule all notifications with current language
            if (SHOW_LOGS) {
              console.log(
                '[ReportNotifications] Scheduling notifications with current language...'
              );
            }
            const scheduleSuccess = await notificationService.scheduleAllReportNotifications(
              t('notifications.report.title'),
              t('notifications.report.body'),
              t('notifications.potential.title'),
              t('notifications.potential.body'),
              t('notifications.preferred.title'),
              t('notifications.preferred.body'),
              locale
            );

            if (scheduleSuccess) {
              await AsyncStorage.setItem(notificationsSetupKey, 'true');
              if (SHOW_LOGS) {
                console.log(
                  '[ReportNotifications] Successfully scheduled all report notifications'
                );
              }

              // Debug: List all scheduled notifications to verify setup
              if (SHOW_LOGS) {
                console.log('[ReportNotifications] Debug: Listing all scheduled notifications...');
              }
              await notificationService.listAllScheduledNotifications();
            } else {
              if (SHOW_LOGS) {
                console.log('[ReportNotifications] Failed to schedule notifications');
              }
              // Don't set the setup flag if scheduling failed
            }
          } else {
            if (SHOW_LOGS) {
              console.log(
                '[ReportNotifications] All notifications are properly set up and scheduled'
              );
            }
          }
        } else {
          // Cancel all notifications if user is not subscribed
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] No active subscription, cancelling notifications');
          }
          await notificationService.cancelAllReportNotifications();
          await AsyncStorage.removeItem(notificationsSetupKey);
          if (SHOW_LOGS) {
            console.log('[ReportNotifications] Successfully cancelled all report notifications');
          }
        }
      } catch (err) {
        if (SHOW_LOGS) {
          console.error('[ReportNotifications] Error setting up notifications:', err);
        }
      }
    };

    setupNotifications();
  }, [isAuthenticated, profile?.data?.email, reportData, t, locale]);

  return null;
};
