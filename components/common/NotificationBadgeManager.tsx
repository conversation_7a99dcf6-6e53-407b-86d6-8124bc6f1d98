import notificationService from '@/lib/notification/notification.service';

import { useEffect, useRef } from 'react';

import { AppState, AppStateStatus } from 'react-native';

const SHOW_LOGS = false;

// Safely import notifications module
let Notifications: any = null;
try {
  Notifications = require('expo-notifications');
} catch (error) {
  console.log('expo-notifications module not available:', error);
}

export const NotificationBadgeManager = () => {
  const appState = useRef(AppState.currentState);

  useEffect(() => {
    if (!Notifications) return;

    // Listen for received notifications (when app is in foreground or background)
    const notificationReceivedSubscription = Notifications.addNotificationReceivedListener(
      async (notification: any) => {
        if (SHOW_LOGS) {
          console.log(
            '[Badge] Notification received:',
            notification.request.identifier,
            'App state:',
            AppState.currentState
          );
        }

        // Extract badge count from notification data
        const badgeCount = notification.request.content.data?.badgeCount;

        if (badgeCount && typeof badgeCount === 'number') {
          // Update badge count to match what was set in the notification
          await Notifications.setBadgeCountAsync(badgeCount);
          if (SHOW_LOGS) {
            console.log(`[Badge] Updated badge count to ${badgeCount} from notification data`);
          }
        } else {
          // Fallback: increment badge count if no specific count provided
          await notificationService.incrementBadgeCount();
        }
      }
    );

    // Listen for notification responses (when user taps notification)
    const notificationResponseSubscription = Notifications.addNotificationResponseReceivedListener(
      async (response: any) => {
        if (SHOW_LOGS) {
          console.log('[Badge] Notification tapped:', response.notification.request.identifier);
        }

        // Mark notification as read when user taps it
        await notificationService.markNotificationAsRead(response.notification.request.identifier);
      }
    );

    // Listen for app state changes
    const appStateSubscription = AppState.addEventListener(
      'change',
      async (nextAppState: AppStateStatus) => {
        if (SHOW_LOGS) {
          console.log('[Badge] App state changed:', appState.current, '→', nextAppState);
        }

        if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
          // App has come to the foreground
          if (SHOW_LOGS) {
            console.log('[Badge] App came to foreground, syncing badge count');
          }
          await syncBadgeCount();
        }

        appState.current = nextAppState;
      }
    );

    // Initial badge sync when component mounts
    syncBadgeCount();

    // Cleanup subscriptions
    return () => {
      notificationReceivedSubscription.remove();
      notificationResponseSubscription.remove();
      appStateSubscription.remove();
    };
  }, []);

  // Sync badge count with actual unread notifications
  const syncBadgeCount = async () => {
    try {
      if (!Notifications) return;

      if (SHOW_LOGS) {
        console.log('[Badge] Syncing badge count...');
      }

      // Get delivered notifications (notifications that were actually shown to user)
      const deliveredNotifications = await Notifications.getPresentedNotificationsAsync();
      if (SHOW_LOGS) {
        console.log(`[Badge] Found ${deliveredNotifications.length} delivered notifications`);
      }

      // Filter for our notification types only
      const ourNotificationTypes = ['weekly_report', 'potential_stocks', 'preferred_stocks'];
      const ourDeliveredNotifications = deliveredNotifications.filter((notification: any) => {
        const notificationType = notification.request.content?.data?.type;
        return ourNotificationTypes.includes(notificationType);
      });

      if (SHOW_LOGS) {
        console.log(
          `[Badge] Found ${ourDeliveredNotifications.length} of our delivered notifications`
        );
      }

      // Check which ones are unread
      let unreadCount = 0;
      for (const notification of ourDeliveredNotifications) {
        const isRead = await notificationService.isNotificationRead(
          notification.request.identifier
        );
        if (!isRead) {
          unreadCount++;
        }
      }

      if (SHOW_LOGS) {
        console.log(`[Badge] Calculated unread count: ${unreadCount}`);
      }

      // Update badge count to match actual unread count
      await Notifications.setBadgeCountAsync(unreadCount);

      // Store the count in AsyncStorage for persistence
      const currentStoredCount = await notificationService.getBadgeCount();
      if (currentStoredCount !== unreadCount) {
        await notificationService.clearBadgeCount();
        for (let i = 0; i < unreadCount; i++) {
          await notificationService.incrementBadgeCount();
        }
        if (SHOW_LOGS) {
          console.log(
            `[Badge] Updated stored badge count from ${currentStoredCount} to ${unreadCount}`
          );
        }
      }
    } catch (error) {
      if (SHOW_LOGS) {
        console.error('[Badge] Error syncing badge count:', error);
      }
    }
  };

  return null; // This component doesn't render anything
};
