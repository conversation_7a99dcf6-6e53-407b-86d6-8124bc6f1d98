import React from 'react';

import { View, Text } from 'react-native';

import { SettingMenuItem, SettingMenuItemProps } from './SettingMenuItem';

export type SettingsSectionProps = {
  section: SettingsSections;
};

export type SettingsSections = {
  title?: string;
  items: SettingMenuItemProps[];
};

export function SettingsSection({ section }: SettingsSectionProps) {
  return (
    <View className="mb-6">
      {section.title && (
        <Text className="mb-2 ml-4 text-sm font-medium text-zinc-500">{section.title}</Text>
      )}
      {section.items.map((item, index) => (
        <SettingMenuItem key={`${section.title}-${index}`} {...item} />
      ))}
    </View>
  );
}
