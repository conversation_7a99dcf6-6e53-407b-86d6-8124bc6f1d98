import FontAwesome from '@expo/vector-icons/FontAwesome';

import { View, Text } from 'react-native';

interface SectionHeaderProps {
  label: string;
  title: string;
  icon: keyof typeof FontAwesome.glyphMap;
  className?: string;
  classNames?: {
    container?: string;
    title?: string;
    label?: string;
  };
}

export function SectionHeader({
  label,
  title,
  icon,
  className = '',
  classNames = {},
}: SectionHeaderProps) {
  return (
    <View className={`border-b border-gray-100 px-5 py-4 ${className} ${classNames.container}`}>
      <View className="flex-row items-center">
        <View className="h-10 w-10 items-center justify-center rounded-xl bg-orange-100/70">
          <FontAwesome name={icon} size={20} color="#FF6B00" />
        </View>
        <View className="ml-3">
          <Text className={`text-sm font-medium text-orange-500 ${classNames.label}`}>{label}</Text>
          <Text className={`text-xl font-bold text-gray-800 ${classNames.title}`}>{title}</Text>
        </View>
      </View>
    </View>
  );
}
