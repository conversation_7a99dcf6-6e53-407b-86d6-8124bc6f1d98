import { FontAwesome } from '@expo/vector-icons';
import { Link } from 'expo-router';
import { Href } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';

import React from 'react';

import { View, Text, TouchableOpacity } from 'react-native';

export type FontAwesomeIconName = keyof typeof FontAwesome.glyphMap;

export type SettingMenuItemProps = {
  href?: Href;
  browserUrl?: string;
  icon: FontAwesomeIconName;
  label: string;
  onPress?: () => void;
  variant?: 'danger';
  show?: boolean;
  highlight?: boolean;
};

export function SettingMenuItem({
  href,
  browserUrl,
  icon,
  label,
  onPress,
  variant,
  show = true,
  highlight = false,
}: SettingMenuItemProps) {
  if (!show) return null;

  const baseStyles = 'mb-2 rounded-xl bg-zinc-50 p-4';

  const handleBrowserPress = async () => {
    if (browserUrl) {
      await WebBrowser.openBrowserAsync(browserUrl);
    }
  };

  const content = (
    <View className="flex-row items-center">
      <View className="min-w-[90%] flex-row items-center">
        <View className="ml-1 min-w-6">
          <FontAwesome name={icon} size={20} color={variant === 'danger' ? '#ef4444' : '#F97316'} />
        </View>
        <Text
          className={`ml-6 text-base font-medium ${
            variant === 'danger' ? 'text-red-500' : 'text-zinc-900'
          }`}>
          {label}
        </Text>
        {highlight && (
          <View className="ml-3 rounded-full bg-theme px-2.5 py-0.5">
            <Text className="text-xs font-bold text-white">New</Text>
          </View>
        )}
      </View>
      {(href || browserUrl) && (
        <View>
          <FontAwesome
            name={browserUrl ? 'external-link' : 'chevron-right'}
            size={browserUrl ? 16 : 14}
            color={variant === 'danger' ? '#ef4444' : '#F97316'}
          />
        </View>
      )}
    </View>
  );

  if (browserUrl) {
    return (
      <TouchableOpacity className={baseStyles} activeOpacity={0.7} onPress={handleBrowserPress}>
        {content}
      </TouchableOpacity>
    );
  }

  if (href) {
    return (
      <Link href={href as any} className={baseStyles} asChild>
        <TouchableOpacity activeOpacity={0.7}>{content}</TouchableOpacity>
      </Link>
    );
  }

  return (
    <TouchableOpacity
      className={baseStyles}
      activeOpacity={0.7}
      onPress={onPress}
      disabled={!onPress}>
      {content}
    </TouchableOpacity>
  );
}
