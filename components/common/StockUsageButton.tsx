import { bottomSheetRegistry } from '@/lib/bottomSheetRegistry';
import { FontAwesome5 } from '@expo/vector-icons';
import { BottomSheetModal, BottomSheetBackdrop, BottomSheetView } from '@gorhom/bottom-sheet';
import { differenceInSeconds } from 'date-fns';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Svg, Circle } from 'react-native-svg';

import * as React from 'react';
import { useState, useEffect, useRef, useCallback, useMemo, forwardRef } from 'react';

import { View, Text, Image, TouchableOpacity } from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useViewStockUsage } from '@/hooks/useStock';

import Button from '@/components/shared/Button';

const StockUsageButton = forwardRef<View, {}>((props, ref) => {
  const { t } = useTranslation();
  const { data } = useViewStockUsage();
  const { hasPermissionTo } = useAuth();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['60%'], []);
  const router = useRouter();
  const params = useLocalSearchParams<{ symbol?: string }>();

  // Register bottom sheet with global registry
  useEffect(() => {
    bottomSheetRegistry.register(bottomSheetModalRef);
    return () => {
      bottomSheetRegistry.unregister(bottomSheetModalRef);
    };
  }, []);

  // Calculate remaining usage ratio
  const remainingRatio = data ? (data.data.limit - data.data.usage) / data.data.limit : 1;
  const circumference = 2 * Math.PI * 45; // 2πr where r=45
  const strokeDashoffset = circumference * (1 - remainingRatio);

  const [timeLeft, setTimeLeft] = useState('');

  // Calculate countdown timer
  useEffect(() => {
    if (!data) return;

    const timer = setInterval(() => {
      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setHours(24, 0, 0, 0);
      const seconds = differenceInSeconds(tomorrow, now);
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      setTimeLeft(t('stock.usage.reset.format', { hours, minutes }));
    }, 1000);

    return () => clearInterval(timer);
  }, [data, t]);

  const handlePresentModalPress = useCallback(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  const handlePress = useCallback(() => {
    // If we have a symbol from the route parameters, we're on a stock page
    if (params.symbol) {
      // Pass the symbol to AI chat for contextual questions
      router.push(`/setting/ai-chat?symbol=${params.symbol}`);
    } else {
      router.push('/setting/ai-chat');
    }
  }, [router, params.symbol]);

  const handleLongPress = useCallback(() => {
    handlePresentModalPress();
  }, [handlePresentModalPress]);

  const handleSheetChanges = useCallback((index: number) => {
    /* console.log('handleSheetChanges', index); */
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} />,
    []
  );

  const handleStockPress = useCallback(
    (symbol: string) => {
      bottomSheetModalRef.current?.dismiss();
      router.push(`../stock/${symbol}`);
    },
    [router]
  );

  return (
    <>
      <TouchableOpacity
        ref={ref}
        onPress={handlePress}
        onLongPress={handleLongPress}
        delayLongPress={300}
        className="absolute bottom-28 right-6 h-16 w-16 items-center justify-center">
        <View className="relative h-full w-full">
          {/* Progress Ring */}
          <View className="absolute -left-[10%] -top-[10%] h-[120%] w-[120%]">
            <Svg height="100%" width="100%" viewBox="0 0 100 100">
              {/* Background Circle */}
              <Circle cx="50" cy="50" r="45" stroke="#E5E7EB" strokeWidth="3" fill="transparent" />
              {/* Progress Circle */}
              <Circle
                cx="50"
                cy="50"
                r="45"
                stroke="#FFB339"
                strokeWidth="4"
                fill="transparent"
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                transform="rotate(-90 50 50)"
              />
            </Svg>
          </View>

          {/* Content */}
          <View className="absolute h-full w-full items-center justify-center overflow-hidden rounded-full bg-white shadow-lg">
            <Image
              source={require('../../assets/illustration/floating-button.png')}
              className="h-full w-full scale-110"
              resizeMode="cover"
            />
          </View>
        </View>
      </TouchableOpacity>

      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={0}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}>
        <BottomSheetView className="flex-1 p-4">
          <Text className="mb-4 text-xl font-bold">{t('stock.usage.title')}</Text>

          {/* Survey Promotion - Only show if user doesn't have free_report_2024 permission */}
          {!hasPermissionTo('free_report_2024') && (
            <View className="mb-4 overflow-hidden rounded-xl bg-gray-50 shadow shadow-gray-100">
              <View className="p-4">
                <View className="mb-3 flex-row items-center">
                  <View className="mr-2 rounded-full bg-orange-100 p-2">
                    <FontAwesome5 name="gift" size={16} color="#FF9339" />
                  </View>
                  <Text className="text-lg font-semibold text-gray-800">
                    {t('stock.usage.survey.title', 'Unlock More Features')}
                  </Text>
                </View>
                <Text className="mb-3 text-base text-gray-600">
                  {t(
                    'stock.usage.survey.promotion',
                    'Share your feedback through our quick survey and get extended daily usage limits.'
                  )}
                </Text>
                <Button
                  variant="primary"
                  onPress={() => {
                    router.push('/(tabs)/setting');
                    bottomSheetModalRef.current?.dismiss();
                  }}
                  className="mt-1">
                  {t('stock.usage.survey.button', 'Take Quick Survey')}
                </Button>
              </View>
              <View className="absolute -right-6 -top-6 rotate-12 opacity-10">
                <FontAwesome5 name="chart-line" size={96} color="#FFB339" />
              </View>
            </View>
          )}

          {/* Usage info */}
          <View className="mb-4 flex-row items-center justify-between rounded-lg bg-gray-50 p-4 shadow-sm shadow-gray-100">
            <View>
              <Text className="text-gray-600">{t('stock.usage.remaining.label')}</Text>
              <Text className="text-2xl font-bold text-theme">
                {t('stock.usage.remaining.format', {
                  remaining: data ? data.data.limit - data.data.usage : '-',
                  total: data?.data.limit,
                })}
              </Text>
            </View>
            <View>
              <Text className="text-gray-600">{t('stock.usage.reset.label')}</Text>
              <Text className="text-lg font-medium">{timeLeft}</Text>
            </View>
          </View>

          {/* Viewed stocks */}
          <Text className="mb-2 font-medium">{t('stock.usage.viewedStocks.title')}</Text>
          <View className="rounded-lg bg-gray-50 p-4">
            {data?.data.stocks.length ? (
              <View className="flex-row flex-wrap gap-4">
                {data.data.stocks.map((stock, index) => (
                  <TouchableOpacity
                    key={stock}
                    onPress={() => handleStockPress(stock)}
                    className="relative w-[47%] flex-row items-center rounded-md bg-white p-3 shadow-[0_1px_2px_rgba(0,0,0,0.05)] active:opacity-50">
                    <View className="flex-row items-center gap-2 pr-4">
                      <Text className="h-5 w-5 rounded-full bg-gray-100 text-center text-xs leading-5 text-gray-600">
                        {index + 1}
                      </Text>
                      <Text numberOfLines={1} className="flex-1 text-sm font-medium text-gray-800">
                        {stock}
                      </Text>
                    </View>
                    <FontAwesome5
                      name="chevron-right"
                      size={12}
                      color="#9CA3AF"
                      solid
                      className="absolute right-3"
                    />
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <Text className="text-center text-gray-400">
                {t('stock.usage.viewedStocks.empty')}
              </Text>
            )}
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    </>
  );
});

StockUsageButton.displayName = 'StockUsageButton';

export default StockUsageButton;
