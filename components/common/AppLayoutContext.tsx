import React, { createContext, useContext, ReactNode } from 'react';

interface AppLayoutContextType {
  stockUsageButtonRef: React.RefObject<any> | null;
}

const AppLayoutContext = createContext<AppLayoutContextType>({
  stockUsageButtonRef: null,
});

interface AppLayoutProviderProps {
  children: ReactNode;
  stockUsageButtonRef: React.RefObject<any>;
}

export const AppLayoutProvider: React.FC<AppLayoutProviderProps> = ({
  children,
  stockUsageButtonRef,
}) => {
  return (
    <AppLayoutContext.Provider
      value={{
        stockUsageButtonRef,
      }}>
      {children}
    </AppLayoutContext.Provider>
  );
};

export const useAppLayout = () => {
  const context = useContext(AppLayoutContext);
  if (!context) {
    throw new Error('useAppLayout must be used within an AppLayoutProvider');
  }
  return context;
};
