import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { View, Text, TouchableOpacity } from 'react-native';

import ExceedLimitSvg from '@/components/illustrations/exceed-limit';

export default function ExceedLimitView() {
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <View className="flex-1 items-center justify-center bg-white px-6">
      <View className="h-64 w-full">
        <ExceedLimitSvg height={'100%'} width={'100%'} />
      </View>

      <Text className="mb-2 mt-8 text-center text-2xl font-bold text-gray-900">
        {t('stock.exceedLimit.title')}
      </Text>

      <Text className="mb-8 text-center text-base text-gray-600">
        {t('stock.exceedLimit.description')}
      </Text>

      <View className="w-full space-y-4">
        <TouchableOpacity
          onPress={() => router.push('/home')}
          className="flex-row items-center justify-center rounded-lg bg-theme px-6 py-3">
          <FontAwesome name="home" size={16} color="white" className="mr-2" />
          <Text className="font-medium text-white">{t('stock.exceedLimit.backToHome')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
