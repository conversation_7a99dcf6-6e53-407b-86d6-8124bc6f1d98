import { useTranslation } from 'react-i18next';

import { View, Text } from 'react-native';

type InfoCardProps = {
  title?: string;
  content?: string | null;
  className?: string;
  classNames?: {
    container?: string;
    title?: string;
    content?: string;
  };
};

export function InfoCard({
  title,
  content = 'common.analyzing',
  className = '',
  classNames = {},
}: InfoCardProps) {
  const { t } = useTranslation();

  // Enhanced empty check function
  const isEmpty = (value: string | null | undefined): boolean => {
    return value === null || value === undefined || value.trim() === '';
  };

  // Handle content display with translation
  const getDisplayContent = (value: string | null | undefined): string => {
    if (isEmpty(value)) {
      return t('common.analyzing');
    }
    return value?.startsWith('common.') ? t(value) : (value as string);
  };

  const displayContent = getDisplayContent(content);

  return (
    <View className={`rounded-xl bg-orange-50/70 p-4 ${className} ${classNames.container}`}>
      {title && (
        <Text className={`mb-2 text-sm font-semibold text-orange-600 ${classNames.title}`}>
          {title}
        </Text>
      )}
      <Text className={`leading-6 text-gray-700 ${classNames.content}`}>{displayContent}</Text>
    </View>
  );
}
