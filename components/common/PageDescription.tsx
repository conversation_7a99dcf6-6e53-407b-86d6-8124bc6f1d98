import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

import { View, Text } from 'react-native';

interface PageDescriptionProps {
  label?: string;
  description: string;
}

export function PageDescription({ label, description }: PageDescriptionProps) {
  const { t } = useTranslation();
  const displayLabel = label ? label : t(`aiAnalysis.common.featureDescription`);

  return (
    <View className="mb-2 flex-row items-start gap-2 rounded-xl bg-gray-50">
      {<Ionicons name="information-circle" size={17} color="#9ca3af" />}
      <Text className="flex-1 text-sm leading-5 text-gray-500">{`${displayLabel}: ${description}`}</Text>
    </View>
  );
}
