import { REPORT_LANGUAGE_OPTIONS, LANGUAGES } from '@/i18n';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { Dropdown } from 'react-native-element-dropdown';

import { useState } from 'react';

import { StyleSheet, View } from 'react-native';

type LanguageOption = {
  label: string;
  value: string;
};

type ReportLanguagePickerProps = {
  onLanguageChange?: (selected: LanguageOption) => void;
  value?: string;
};

export default function ReportLanguagePicker({
  onLanguageChange,
  value,
}: ReportLanguagePickerProps) {
  const { t } = useTranslation();
  const [isFocus, setIsFocus] = useState(false);

  const handleChange = (item: LanguageOption) => {
    onLanguageChange?.(item);
  };

  return (
    <View style={styles.container}>
      <Dropdown
        style={[styles.dropdown, isFocus && styles.dropdownFocused]}
        containerStyle={styles.dropdownContainer}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        selectedTextProps={{
          numberOfLines: 1,
          ellipsizeMode: 'tail',
        }}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        data={REPORT_LANGUAGE_OPTIONS}
        maxHeight={300}
        labelField="label"
        valueField="value"
        placeholder={t('common.selectLanguage')}
        searchPlaceholder={t('common.search')}
        value={value}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={handleChange}
        activeColor="rgb(249, 249, 249)"
        renderLeftIcon={() => (
          <MaterialIcons
            style={styles.icon}
            color={isFocus ? '#FF9339' : '#4b5563'}
            name="language"
            size={20}
          />
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignSelf: 'stretch',
  },
  dropdown: {
    paddingHorizontal: 8,
    paddingVertical: 9,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#cfcfcf',
  },
  dropdownContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#cfcfcf',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    overflow: 'hidden',
  },
  dropdownFocused: {
    borderWidth: 1,
    borderColor: '#FF9339',
  },
  icon: {
    marginRight: 8,
  },
  placeholderStyle: {
    fontSize: 14,
    color: '#4b5563',
    flex: 1,
  },
  selectedTextStyle: {
    fontSize: 14,
    color: '#111827',
    marginRight: 8,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 14,
    borderRadius: 6,
  },
});
