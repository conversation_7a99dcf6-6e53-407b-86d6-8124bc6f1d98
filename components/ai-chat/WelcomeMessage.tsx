import { useTranslation } from 'react-i18next';

import React, { useState } from 'react';

import { Text, TouchableOpacity, View } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

interface WelcomeMessageProps {
  onQuestionSelect: (question: string) => void;
  symbol?: string;
  dailyUsageCount: number;
  dailyUsageLimit: number;
}

const WelcomeMessage = ({
  onQuestionSelect,
  symbol,
  dailyUsageCount,
  dailyUsageLimit,
}: WelcomeMessageProps) => {
  const { t } = useTranslation();

  const getQuestionBank = () => {
    const questionBank = t('aiChat.welcome.questionBank', { returnObjects: true }) as any;
    return {
      general: questionBank.general || [],
      symbolSpecific: questionBank.symbolSpecific || [],
    };
  };

  const getRandomSubtitle = () => {
    const subtitles = t('aiChat.welcome.subtitles', { returnObjects: true }) as string[];
    if (Array.isArray(subtitles) && subtitles.length > 0) {
      return subtitles[Math.floor(Math.random() * subtitles.length)];
    }
    // Fallback to single subtitle if subtitles array is not available
    return (
      t('aiChat.welcome.subtitle') ||
      'Your intelligent companion for stock analysis and market insights.'
    );
  };

  const getRandomQuestions = () => {
    const questionBank = getQuestionBank();
    let selectedQuestions: string[] = [];

    if (symbol && typeof symbol === 'string') {
      // If we have a symbol, include 2 symbol-specific questions and 1 general question
      const symbolQuestions = questionBank.symbolSpecific
        .map((q: string) => q.replace(/\{\{symbol\}\}/g, symbol))
        .sort(() => 0.5 - Math.random())
        .slice(0, 2);

      const generalQuestions = questionBank.general.sort(() => 0.5 - Math.random()).slice(0, 1);

      selectedQuestions = [...symbolQuestions, ...generalQuestions];
    } else {
      // If no symbol, select 3 random general questions
      selectedQuestions = questionBank.general.sort(() => 0.5 - Math.random()).slice(0, 3);
    }

    return selectedQuestions;
  };

  // Generate questions and subtitle once when component mounts
  const [questions] = useState(() => getRandomQuestions());
  const [subtitle] = useState(() => getRandomSubtitle());

  // Check if user has remaining usage
  const hasRemainingUsage = dailyUsageCount < dailyUsageLimit;

  const showHints = () => {
    console.log('showHints');
  };

  return (
    <View className="p-2 pb-4">
      <View className="flex-row items-center">
        <Text className="mb-2 text-3xl font-bold" style={{ color: '#FF9339' }}>
          {t('aiChat.welcome.title') || 'Hi! I am Tabbi'}
        </Text>
        <TouchableOpacity onPress={() => showHints()} activeOpacity={0.7}>
          <FontAwesome className="ml-3 pb-2" name="question-circle" size={28} color="#FF9339" />
        </TouchableOpacity>
      </View>
      <Text className="mb-2 text-base leading-6 text-gray-700">{subtitle}</Text>

      {/* Show usage limit message if no remaining usage */}
      {!hasRemainingUsage && (
        <View className="mb-4 rounded-lg bg-red-50 p-3">
          <Text className="text-sm text-red-600">
            {t('aiChat.messages.limitReachedShort', {
              current: dailyUsageCount,
              limit: dailyUsageLimit,
            })}
          </Text>
        </View>
      )}

      {/* Random Questions - only show if user has remaining usage */}
      {hasRemainingUsage && (
        <View className="">
          {questions.map((question: string, index: number) => (
            <TouchableOpacity
              key={index}
              className={`rounded-lg border border-gray-200 bg-white p-3 ${index > 0 ? 'mt-2' : ''}`}
              onPress={() => onQuestionSelect(question)}
              activeOpacity={0.7}>
              <Text className="text-sm leading-5 text-gray-700">💬 {question}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

export default WelcomeMessage;
