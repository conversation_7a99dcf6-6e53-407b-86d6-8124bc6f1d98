import { useTranslation } from 'react-i18next';

import React, { useState } from 'react';

import { Text, TouchableOpacity, View } from 'react-native';

interface FollowUpQuestionsProps {
  onQuestionSelect: (question: string) => void;
  symbol?: string;
  dailyUsageCount: number;
  dailyUsageLimit: number;
}

const FollowUpQuestions = ({
  onQuestionSelect,
  symbol,
  dailyUsageCount,
  dailyUsageLimit,
}: FollowUpQuestionsProps) => {
  const { t } = useTranslation();

  const getQuestionBank = () => {
    const questionBank = t('aiChat.welcome.questionBank', { returnObjects: true }) as any;
    return {
      general: questionBank.general || [],
      symbolSpecific: questionBank.symbolSpecific || [],
    };
  };

  const getRandomQuestions = () => {
    const questionBank = getQuestionBank();
    let selectedQuestions: string[] = [];

    if (symbol && typeof symbol === 'string') {
      // If we have a symbol, include 2 symbol-specific questions and 1 general question
      const symbolQuestions = questionBank.symbolSpecific
        .map((q: string) => q.replace(/\{\{symbol\}\}/g, symbol))
        .sort(() => 0.5 - Math.random())
        .slice(0, 2);

      const generalQuestions = questionBank.general.sort(() => 0.5 - Math.random()).slice(0, 1);

      selectedQuestions = [...symbolQuestions, ...generalQuestions];
    } else {
      // If no symbol, select 3 random general questions
      selectedQuestions = questionBank.general.sort(() => 0.5 - Math.random()).slice(0, 3);
    }

    return selectedQuestions;
  };

  // Generate questions once when component mounts
  const [questions] = useState(() => getRandomQuestions());

  // Check if user has remaining usage
  const hasRemainingUsage = dailyUsageCount < dailyUsageLimit;

  // Don't render anything if user has no remaining usage
  if (!hasRemainingUsage) {
    return null;
  }

  return (
    <View className="mt-3">
      <Text className="mb-2 text-xs font-medium uppercase tracking-wide text-gray-500">
        {t('aiChat.followUp.title')}
      </Text>
      <View className="space-y-2">
        {questions.map((question: string, index: number) => (
          <TouchableOpacity
            key={index}
            className={`rounded-lg border border-gray-200 bg-white p-3 ${index > 0 ? 'mt-2' : ''}`}
            onPress={() => onQuestionSelect(question)}
            activeOpacity={0.7}>
            <Text className="text-sm leading-5 text-gray-700">💬 {question}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default FollowUpQuestions;
