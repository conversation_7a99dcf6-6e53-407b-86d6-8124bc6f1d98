import Markdown from 'react-native-markdown-display';

import React from 'react';

import { Platform, Text } from 'react-native';

interface MessageContentProps {
  content: string;
  isUser: boolean;
}

// Markdown styles
const markdownStyles = {
  body: {
    color: '#000000', // Black text for light background
    lineHeight: 20,
  },
  paragraph: {
    marginVertical: 8,
    lineHeight: 20,
    includeFontPadding: false,
  },
  text: {
    lineHeight: 20,
    includeFontPadding: false,
    selectable: true,
  },
  strong: {
    fontWeight: '700' as const,
    lineHeight: 20,
  },
  em: {
    fontStyle: 'italic' as const,
    lineHeight: 20,
  },
  link: {
    color: '#0066cc', // Blue links for light background
    lineHeight: 20,
  },
  list: {
    marginVertical: 4,
  },
  listItem: {
    marginVertical: 2,
    lineHeight: 20,
  },
  listItemText: {
    lineHeight: 20,
    includeFontPadding: false,
  },
  code_inline: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)', // Light gray for code
    padding: 4,
    borderRadius: 4,
    fontFamily: Platform.select({
      ios: 'Menlo',
      android: 'monospace',
    }),
    lineHeight: 20,
  },
  code_block: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)', // Light gray for code
    padding: 8,
    borderRadius: 4,
    fontFamily: Platform.select({
      ios: 'Menlo',
      android: 'monospace',
    }),
    lineHeight: 20,
  },
};

const MessageContent = ({ content, isUser }: MessageContentProps) => {
  // Process content to handle newlines and ensure consistent spacing
  const processContent = (text: string) => {
    return text
      .replace(/\\n/g, '\n')
      .replace(/\n\n+/g, '\n\n')
      .split('\n')
      .map(line => line.trim())
      .join('\n')
      .trim();
  };

  // Check if the content contains any markdown-like syntax
  const hasMarkdown = /[*#`\[\]\(\)\n]/.test(content);

  if (hasMarkdown) {
    return (
      <Markdown style={isUser ? { ...markdownStyles, body: { color: '#FFFFFF' } } : markdownStyles}>
        {processContent(content)}
      </Markdown>
    );
  }

  return (
    <Text
      selectable={true}
      style={{
        lineHeight: 20,
        includeFontPadding: false,
        color: isUser ? '#FFFFFF' : '#000000',
      }}>
      {processContent(content)
        .split('\n')
        .map((line, i, arr) => (
          <React.Fragment key={i}>
            {line}
            {i < arr.length - 1 && '\n'}
          </React.Fragment>
        ))}
    </Text>
  );
};

export default MessageContent;
