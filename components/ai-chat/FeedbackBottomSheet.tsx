import { Ionicons } from '@expo/vector-icons';
import { 
  BottomSheetModal, 
  BottomSheetView, 
  BottomSheetBackdrop, 
  BottomSheetTextInput 
} from '@gorhom/bottom-sheet';
import { useTranslation } from 'react-i18next';

import React, { useCallback, useMemo, useState } from 'react';

import { View, Text, TouchableOpacity, ScrollView } from 'react-native';

interface FeedbackReason {
  key: string;
  label: string;
}

interface FeedbackBottomSheetProps {
  onClose: () => void;
  onSubmit: (reasons: string[], comment: string) => void;
}

const FeedbackBottomSheet = React.forwardRef<BottomSheetModal, FeedbackBottomSheetProps>(
  ({ onClose, onSubmit }, ref) => {
    const { t } = useTranslation();
    const [selectedReasons, setSelectedReasons] = useState<string[]>([]);
    const [comment, setComment] = useState('');

    // Snap points for the bottom sheet
    const snapPoints = useMemo(() => ['75%'], []);

    // Feedback reasons based on the provided image
    const feedbackReasons: FeedbackReason[] = useMemo(
      () => [
        {
          key: 'irrelevant',
          label: t('aiChat.feedback.reasons.irrelevant', 'Response irrelevant to the question'),
        },
        {
          key: 'unrealistic',
          label: t('aiChat.feedback.reasons.unrealistic', 'Unrealistic prediction'),
        },
        {
          key: 'unclear',
          label: t('aiChat.feedback.reasons.unclear', 'Unclear conclusion'),
        },
        {
          key: 'inaccurate',
          label: t('aiChat.feedback.reasons.inaccurate', 'Inaccurate information'),
        },
        {
          key: 'insufficient',
          label: t('aiChat.feedback.reasons.insufficient', 'Insufficient depth in answer'),
        },
        {
          key: 'excessive',
          label: t('aiChat.feedback.reasons.excessive', 'Excessive in length and detail'),
        },
      ],
      [t]
    );

    // Handle reason selection
    const toggleReason = useCallback((reasonKey: string) => {
      setSelectedReasons(prev => {
        if (prev.includes(reasonKey)) {
          return prev.filter(key => key !== reasonKey);
        } else {
          return [...prev, reasonKey];
        }
      });
    }, []);

    // Handle submit
    const handleSubmit = useCallback(() => {
      onSubmit(selectedReasons, comment);
      // Reset form
      setSelectedReasons([]);
      setComment('');
    }, [selectedReasons, comment, onSubmit]);

    // Handle close
    const handleClose = useCallback(() => {
      // Reset form
      setSelectedReasons([]);
      setComment('');
      onClose();
    }, [onClose]);

    // Handle sheet changes (needed for proper functionality)
    const handleSheetChanges = useCallback(
      (index: number) => {
        if (index === -1) {
          handleClose();
        }
      },
      [handleClose]
    );

    // Render backdrop
    const renderBackdrop = useCallback(
      (props: any) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={0.7}
          onPress={handleClose}
        />
      ),
      [handleClose]
    );

    return (
      <BottomSheetModal
        ref={ref}
        index={0}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={true}
        enableDynamicSizing={false}
        keyboardBehavior="fillParent"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        onDismiss={handleClose}>
        <BottomSheetView className="flex-1 px-4 pt-2">
          {/* Header */}
          <View className="mb-6 flex-row items-center justify-center">
            <Text className="text-xl font-semibold text-gray-800">
              {t('aiChat.feedback.title', 'Tell us more')}
            </Text>
            <TouchableOpacity onPress={handleClose} className="absolute right-0 rounded-full p-2">
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView 
            className="flex-1" 
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{ flexGrow: 1 }}>
            {/* Feedback reasons */}
            <View className="mb-6">
              {feedbackReasons.map(reason => (
                <TouchableOpacity
                  key={reason.key}
                  onPress={() => toggleReason(reason.key)}
                  className="mb-5 flex-row items-center"
                  activeOpacity={0.7}>
                  <Text className="flex-1 text-base text-gray-700">{reason.label}</Text>
                  <View
                    className={`mr-3 h-5 w-5 rounded border-2 ${
                      selectedReasons.includes(reason.key)
                        ? 'border-[#F7AC13] bg-[#F7AC13]'
                        : 'border-gray-300 bg-transparent'
                    }`}>
                    {selectedReasons.includes(reason.key) && (
                      <Ionicons
                        name="checkmark"
                        size={12}
                        color="white"
                        style={{ alignSelf: 'center', marginTop: 1 }}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Comment section */}
            <View className="-mt-5">
              <BottomSheetTextInput
                value={comment}
                onChangeText={setComment}
                placeholder={t(
                  'aiChat.feedback.commentPlaceholder',
                  'Please share your thoughts...'
                )}
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={4}
                maxLength={500}
                className="rounded-lg border border-gray-200 bg-gray-50 p-3 text-base text-gray-800"
                style={{
                  textAlignVertical: 'top',
                  minHeight: 100,
                  maxHeight: 120,
                }}
              />
              <Text className="mt-1 text-right text-xs text-gray-400">{comment.length}/500</Text>
            </View>
          </ScrollView>

          {/* Submit button */}
          <View className="border-t border-gray-100 pb-10 pt-4">
            <TouchableOpacity
              onPress={handleSubmit}
              disabled={selectedReasons.length === 0 && comment.trim() === ''}
              className={`rounded-full py-3 ${
                selectedReasons.length > 0 || comment.trim() !== '' ? 'bg-[#F7AC13]' : 'bg-gray-300'
              }`}>
              <Text className="text-center text-base font-semibold text-white">
                {t('aiChat.feedback.submit', 'Confirm')}
              </Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheetModal>
    );
  }
);

FeedbackBottomSheet.displayName = 'FeedbackBottomSheet';

export default FeedbackBottomSheet;
