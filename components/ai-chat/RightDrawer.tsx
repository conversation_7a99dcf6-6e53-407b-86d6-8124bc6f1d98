import { Chat } from '@/types/api/ai-chat';
import { Ionicons } from '@expo/vector-icons';
import { Portal } from '@gorhom/portal';
import { differenceInSeconds } from 'date-fns';
import { useTranslation } from 'react-i18next';

import React, { useEffect, useRef, useState } from 'react';

import {
  ActivityIndicator,
  Animated,
  FlatList,
  Platform,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface RightDrawerProps {
  visible: boolean;
  onClose: () => void;
  onNewChat: () => void;
  chatHistory: Chat[];
  historyPagination?: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  currentChatId: string | null;
  onLoadChat: (chatId: string) => void;
  onDeleteChat: (chatId: string) => void;
  dailyUsageCount: number;
  dailyUsageLimit: number;
  isUsageLoading: boolean;
  isHistoryLoading: boolean;
  isRequestActive?: boolean;
  // Tutorial refs
  chatHistorySectionRef?: React.RefObject<any>;
  dailyUsageSectionRef?: React.RefObject<any>;
}

const RightDrawer = ({
  visible,
  onClose,
  onNewChat,
  chatHistory,
  historyPagination,
  currentChatId,
  onLoadChat,
  onDeleteChat,
  dailyUsageCount,
  dailyUsageLimit,
  isUsageLoading,
  isHistoryLoading,
  isRequestActive,
  chatHistorySectionRef,
  dailyUsageSectionRef,
}: RightDrawerProps) => {
  const slideAnim = useRef(new Animated.Value(300)).current; // Start off-screen to the right
  const backdropAnim = useRef(new Animated.Value(0)).current; // For backdrop fade
  const [modalVisible, setModalVisible] = useState(false);
  const [timeLeft, setTimeLeft] = useState('');
  const { t } = useTranslation();

  // Calculate countdown timer for usage reset
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setHours(24, 0, 0, 0);
      const seconds = differenceInSeconds(tomorrow, now);
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      setTimeLeft(`${hours}h ${minutes}m`);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (visible) {
      // Show modal first, then animate in
      setModalVisible(true);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animate out first, then hide modal
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(backdropAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Hide modal only after animation completes
        setModalVisible(false);
      });
    }
  }, [visible, slideAnim, backdropAnim]);

  const handleNewChatPress = () => {
    onNewChat();
    onClose(); // Close drawer after starting new chat
  };

  const handleChatSelect = (chatId: string) => {
    onLoadChat(chatId);
    onClose(); // Close drawer after selecting chat
  };

  const formatDate = (timestamp: string) => {
    // Convert API timestamp (ISO string) to Date and format
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return t('aiChat.drawer.dateLabels.today');
    } else if (diffInDays === 1) {
      return t('aiChat.drawer.dateLabels.yesterday');
    } else if (diffInDays < 7) {
      return t('aiChat.drawer.dateLabels.daysAgo', { days: diffInDays });
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <>
      {/* Render drawer only when visible or animating */}
      {modalVisible && (
        <Portal>
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}>
            {/* Animated backdrop */}
            <Animated.View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                opacity: backdropAnim,
              }}>
              <TouchableOpacity style={{ flex: 1 }} activeOpacity={1} onPress={onClose} />
            </Animated.View>

            {/* Animated drawer */}
            <Animated.View
              style={{
                position: 'absolute',
                right: 0,
                top: 0,
                bottom: 0,
                width: '80%',
                backgroundColor: 'white',
                transform: [{ translateX: slideAnim }],
                shadowColor: '#000',
                shadowOffset: { width: -2, height: 0 },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
                elevation: 5,
              }}>
              {/* Safe area for status bar */}
              <SafeAreaView
                style={{
                  flex: 1,
                  paddingTop: Platform.OS === 'ios' ? 0 : 40,
                  paddingBottom: Platform.OS === 'ios' ? 0 : 30,
                }}>
                {/* Drawer Header */}
                <View className="flex-1" ref={chatHistorySectionRef}>
                  <View className="flex-row items-center justify-between border-b border-gray-200 px-4 pb-2">
                    <Text className="text-xl font-semibold text-gray-800">
                      {t('aiChat.drawer.history')}
                    </Text>
                    <TouchableOpacity
                      onPress={onClose}
                      className="rounded-full bg-gray-100 p-2"
                      activeOpacity={0.7}>
                      <Ionicons name="close" size={20} color="#666" />
                    </TouchableOpacity>
                  </View>

                  {/* Drawer Content */}
                  <View className="flex-1">
                    {/* New Chat Button */}
                    <View className="p-4 pb-0">
                      <TouchableOpacity
                        onPress={handleNewChatPress}
                        disabled={isRequestActive}
                        className="flex-row items-center justify-center rounded-lg py-3"
                        activeOpacity={0.8}
                        style={{
                          backgroundColor: isRequestActive ? '#ccc' : '#FF9339',
                          shadowColor: '#FF9339',
                          shadowOffset: { width: 0, height: 2 },
                          shadowOpacity: isRequestActive ? 0 : 0.2,
                          shadowRadius: 4,
                          elevation: isRequestActive ? 0 : 3,
                          opacity: isRequestActive ? 0.5 : 1,
                        }}>
                        <Ionicons
                          name="add-circle-outline"
                          size={20}
                          color={isRequestActive ? '#666' : 'white'}
                        />
                        <Text
                          className="ml-2 text-base font-semibold"
                          style={{ color: isRequestActive ? '#666' : 'white' }}>
                          {t('aiChat.drawer.newChat')}
                        </Text>
                      </TouchableOpacity>
                    </View>

                    {/* History Section */}
                    <View className="flex-1 pt-4">
                      {isHistoryLoading ? (
                        <View className="flex-row items-center justify-center py-8">
                          <ActivityIndicator size="small" color="#FF9339" />
                          <Text className="ml-2 text-sm text-gray-500">
                            {t('aiChat.drawer.loadingHistory')}
                          </Text>
                        </View>
                      ) : chatHistory.length === 0 ? (
                        <View className="rounded-lg bg-gray-50 p-4">
                          <Text className="text-center text-sm text-gray-500">
                            {t('aiChat.drawer.noHistory')}
                          </Text>
                        </View>
                      ) : (
                        <FlatList
                          data={chatHistory}
                          keyExtractor={item => item.id}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={{ paddingBottom: 16 }}
                          style={{ flex: 1 }}
                          renderItem={({ item: chat, index }) => (
                            <TouchableOpacity
                              onPress={() => handleChatSelect(chat.id)}
                              disabled={isRequestActive}
                              className={`rounded-lg px-4 py-1 ${
                                currentChatId === chat.id ? 'bg-orange-50' : 'bg-white'
                              } ${index === 0 ? '' : 'mt-2'}`}
                              activeOpacity={0.7}
                              style={{
                                opacity: isRequestActive ? 0.5 : 1,
                              }}>
                              <View className="flex-row items-start justify-between">
                                <View className="mr-2 flex-1">
                                  <Text
                                    className={`text-sm font-medium ${
                                      currentChatId === chat.id
                                        ? 'text-orange-700'
                                        : 'text-gray-800'
                                    }`}
                                    numberOfLines={1}
                                    style={{
                                      color: isRequestActive
                                        ? '#999'
                                        : currentChatId === chat.id
                                          ? '#c2410c'
                                          : '#1f2937',
                                    }}>
                                    {chat.title}
                                  </Text>
                                  <Text
                                    className="mt-1 text-xs"
                                    style={{
                                      color: isRequestActive ? '#ccc' : '#6b7280',
                                    }}>
                                    {formatDate(chat.updatedAt)}
                                  </Text>
                                  {chat.symbol && (
                                    <Text
                                      className="mt-1 text-xs"
                                      style={{
                                        color: isRequestActive ? '#ccc' : '#2563eb',
                                      }}>
                                      📊 {chat.symbol}
                                    </Text>
                                  )}
                                </View>
                              </View>
                            </TouchableOpacity>
                          )}
                        />
                      )}
                    </View>
                  </View>
                </View>

                {/* Usage Section - Fixed at bottom */}
                <View
                  className="border-t border-gray-200 px-4 pb-0 pt-3"
                  ref={dailyUsageSectionRef}>
                  <Text className="mb-3 text-lg font-semibold text-gray-800">
                    {t('aiChat.drawer.dailyUsage')}
                  </Text>
                  <View className="rounded-lg bg-gray-50 p-4">
                    {isUsageLoading ? (
                      <View className="flex-row items-center justify-center py-4">
                        <ActivityIndicator size="small" color="#FF9339" />
                        <Text className="ml-2 text-sm text-gray-500">
                          {t('aiChat.drawer.loadingUsage')}
                        </Text>
                      </View>
                    ) : (
                      <>
                        <View className="mb-2 flex-row items-center justify-between">
                          <Text className="text-sm font-medium text-gray-700">
                            {t('aiChat.drawer.usageCount', {
                              current: dailyUsageCount,
                              limit: dailyUsageLimit,
                            })}
                          </Text>
                          <Text className="text-sm text-gray-500">
                            {t('aiChat.drawer.usageRemaining', {
                              remaining: dailyUsageLimit - dailyUsageCount,
                            })}
                          </Text>
                        </View>

                        {/* Progress Bar */}
                        <View className="h-2 w-full rounded-full bg-gray-200">
                          <View
                            className="h-full rounded-full"
                            style={{
                              width: `${Math.min((dailyUsageCount / dailyUsageLimit) * 100, 100)}%`,
                              backgroundColor:
                                dailyUsageCount >= dailyUsageLimit
                                  ? '#EF4444'
                                  : dailyUsageCount >= dailyUsageLimit * 0.8
                                    ? '#F59E0B'
                                    : '#10B981',
                            }}
                          />
                        </View>

                        {dailyUsageCount >= dailyUsageLimit && (
                          <Text className="mt-2 text-xs text-red-600">
                            {t('aiChat.drawer.limitReached', { time: timeLeft })}
                          </Text>
                        )}
                      </>
                    )}
                  </View>
                </View>
              </SafeAreaView>
            </Animated.View>
          </View>
        </Portal>
      )}
    </>
  );
};

export default RightDrawer;
