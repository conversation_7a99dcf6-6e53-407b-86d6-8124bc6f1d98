import { Ionicons } from '@expo/vector-icons';

import React from 'react';

import { TouchableOpacity, View } from 'react-native';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  liked?: boolean;
  disliked?: boolean;
  responses?: string[];
  currentResponseIndex?: number;
  aiMessageId?: string;
  aiMessageIds?: string[];
}

interface MessageActionsProps {
  message: Message;
  index: number;
  onLike: (index: number) => void;
  onDislike: (index: number) => void;
  onCopy: (content: string) => void;
  onShare: (content: string) => void;
  onTryAgain: (index: number) => void;
  isLatestAssistant: boolean;
  pendingFeedback?: Set<string>;
}

const MessageActions = ({
  message,
  index,
  onLike,
  onDislike,
  onCopy,
  onShare,
  onTryAgain,
  isLatestAssistant,
  pendingFeedback,
}: MessageActionsProps) => {
  const currentResponseIndex = message.currentResponseIndex || 0;
  const currentAiMessageId = message.aiMessageIds?.[currentResponseIndex] || message.aiMessageId;
  const isLikePending = pendingFeedback?.has(`${currentAiMessageId}-like`) || false;
  const isDislikePending = pendingFeedback?.has(`${currentAiMessageId}-dislike`) || false;

  return (
    <View className="mt-2 flex-row justify-start" style={{ gap: 10 }}>
      <TouchableOpacity
        onPress={() => onLike(index)}
        className={`rounded-full p-1 ${message.liked ? 'bg-blue-100' : 'bg-gray-100'}`}
        disabled={isLikePending || isDislikePending}
        style={{ opacity: isLikePending ? 0.5 : 1 }}>
        <Ionicons
          name={message.liked ? 'thumbs-up' : 'thumbs-up-outline'}
          size={16}
          color={message.liked ? '#4285F4' : '#666'}
        />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => onDislike(index)}
        className={`rounded-full p-1 ${message.disliked ? 'bg-red-100' : 'bg-gray-100'}`}
        disabled={isDislikePending || isLikePending}
        style={{ opacity: isDislikePending ? 0.5 : 1 }}>
        <Ionicons
          name={message.disliked ? 'thumbs-down' : 'thumbs-down-outline'}
          size={16}
          color={message.disliked ? '#EA4335' : '#666'}
        />
      </TouchableOpacity>

      {/* Only show retry button for the latest assistant message */}
      {isLatestAssistant && (
        <TouchableOpacity
          onPress={() => onTryAgain(index)}
          className="rounded-full bg-gray-100 p-1">
          <Ionicons name="refresh-outline" size={16} color="#666" />
        </TouchableOpacity>
      )}

      <TouchableOpacity
        onPress={() => onCopy(message.content)}
        className="rounded-full bg-gray-100 p-1">
        <Ionicons name="copy-outline" size={16} color="#666" />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => onShare(message.content)}
        className="rounded-full bg-gray-100 p-1">
        <Ionicons name="share-outline" size={16} color="#666" />
      </TouchableOpacity>
    </View>
  );
};

export default MessageActions;
