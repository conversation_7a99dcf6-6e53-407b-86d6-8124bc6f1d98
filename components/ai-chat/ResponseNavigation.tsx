import { Ionicons } from '@expo/vector-icons';

import React from 'react';

import { Text, TouchableOpacity, View } from 'react-native';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  responses?: string[];
  currentResponseIndex?: number;
}

interface ResponseNavigationProps {
  message: Message;
  index: number;
  onNavigate: (index: number, direction: 'prev' | 'next') => void;
}

const ResponseNavigation = ({ message, index, onNavigate }: ResponseNavigationProps) => {
  if (!message.responses || message.responses.length <= 1) {
    return null;
  }

  const currentIndex = message.currentResponseIndex || 0;
  const totalResponses = message.responses.length;

  return (
    <View className="mb-1 mt-1 flex-row items-center justify-start" style={{ gap: 8 }}>
      <TouchableOpacity
        onPress={() => onNavigate(index, 'prev')}
        className="p-1"
        disabled={currentIndex === 0}>
        <Ionicons name="chevron-back" size={16} color={currentIndex === 0 ? '#ccc' : '#666'} />
      </TouchableOpacity>

      <Text className="min-w-[30px] text-center text-xs text-gray-600">
        {currentIndex + 1}/{totalResponses}
      </Text>

      <TouchableOpacity
        onPress={() => onNavigate(index, 'next')}
        className="p-1"
        disabled={currentIndex === totalResponses - 1}>
        <Ionicons
          name="chevron-forward"
          size={16}
          color={currentIndex === totalResponses - 1 ? '#ccc' : '#666'}
        />
      </TouchableOpacity>
    </View>
  );
};

export default ResponseNavigation;
