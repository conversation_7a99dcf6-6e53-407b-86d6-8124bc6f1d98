import { useTranslation } from 'react-i18next';

import React, { useEffect, useRef } from 'react';

import { Animated, Text, View } from 'react-native';

interface TypingIndicatorProps {
  inline?: boolean;
}

const TypingIndicator = ({ inline = false }: TypingIndicatorProps) => {
  const { t } = useTranslation();
  const dot1Anim = useRef(new Animated.Value(0.3)).current;
  const dot2Anim = useRef(new Animated.Value(0.3)).current;
  const dot3Anim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    // Animated dots
    const animateDots = () => {
      const duration = 600;
      const sequence = Animated.sequence([
        Animated.timing(dot1Anim, { toValue: 1, duration: duration / 3, useNativeDriver: true }),
        Animated.timing(dot2Anim, { toValue: 1, duration: duration / 3, useNativeDriver: true }),
        Animated.timing(dot3Anim, { toValue: 1, duration: duration / 3, useNativeDriver: true }),
        Animated.timing(dot1Anim, { toValue: 0.3, duration: duration / 3, useNativeDriver: true }),
        Animated.timing(dot2Anim, { toValue: 0.3, duration: duration / 3, useNativeDriver: true }),
        Animated.timing(dot3Anim, { toValue: 0.3, duration: duration / 3, useNativeDriver: true }),
      ]);

      Animated.loop(sequence).start();
    };

    animateDots();

    return () => {
      dot1Anim.stopAnimation();
      dot2Anim.stopAnimation();
      dot3Anim.stopAnimation();
    };
  }, [dot1Anim, dot2Anim, dot3Anim]);

  const content = (
    <View className="flex-row items-center">
      <View className="mr-3 flex-row items-center space-x-1">
        <Animated.View
          className="h-2 w-2 rounded-full bg-orange-400"
          style={{ opacity: dot1Anim }}
        />
        <Animated.View
          className="h-2 w-2 rounded-full bg-orange-400"
          style={{ opacity: dot2Anim }}
        />
        <Animated.View
          className="h-2 w-2 rounded-full bg-orange-400"
          style={{ opacity: dot3Anim }}
        />
      </View>
      <Text className="text-sm text-gray-600">{t('aiChat.typing.message')}</Text>
    </View>
  );

  // Return inline content without container
  if (inline) {
    return content;
  }

  // Return with full container styling
  return (
    <View
      className="mb-4 flex-row"
      style={{
        justifyContent: 'flex-start',
      }}>
      <View
        style={{
          maxWidth: '90%',
          backgroundColor: '#F5F5F5',
          borderRadius: 12,
          padding: 12,
        }}>
        {content}
      </View>
    </View>
  );
};

export default TypingIndicator;
