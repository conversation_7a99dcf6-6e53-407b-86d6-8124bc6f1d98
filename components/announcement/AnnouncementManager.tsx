import announcementService, { AnnouncementData } from '@/lib/announcement/announcement.service';
import { bottomSheetRegistry } from '@/lib/bottomSheetRegistry';
import { useTranslation } from 'react-i18next';

import React, { useEffect, useState, useRef } from 'react';

import { InteractionManager } from 'react-native';

import { useAuth } from '@/hooks/useAuth';

import { useTutorial } from '@/components/tutorial/TutorialContext';

import AnnouncementModal from './AnnouncementModal';

const SHOW_LOGS = false;

// Tutorial ID from home page
const WATCHLIST_TUTORIAL_ID = 'watchlist_tutorial';

interface AnnouncementManagerProps {
  // Optional: Only show announcements for authenticated users
  requireAuthentication?: boolean;
  // Optional: Delay before checking for announcements (in ms)
  delay?: number;
}

const AnnouncementManager: React.FC<AnnouncementManagerProps> = ({
  requireAuthentication = false,
  delay = 2000, // 2 seconds delay to let app initialize
}) => {
  const { t } = useTranslation('update-log');
  const { isAuthenticated } = useAuth();
  const { isTutorialCompleted, activeTutorial } = useTutorial();
  const [showModal, setShowModal] = useState(false);
  const [announcement, setAnnouncement] = useState<AnnouncementData | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isCheckingRef = useRef(false);

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    const checkForAnnouncements = async () => {
      // Prevent multiple concurrent checks
      if (isCheckingRef.current) {
        if (SHOW_LOGS) {
          console.log('[Announcement] Already checking, skipping duplicate check');
        }
        return;
      }

      // Wait for all interactions to complete before checking
      InteractionManager.runAfterInteractions(async () => {
        try {
          isCheckingRef.current = true;

          // Check if we should require authentication
          if (requireAuthentication && !isAuthenticated) {
            if (SHOW_LOGS) {
              console.log('[Announcement] Not authenticated, skipping announcement');
            }
            return;
          }

          // Don't show announcements if any tutorial is currently active
          if (activeTutorial) {
            if (SHOW_LOGS) {
              console.log('[Announcement] Tutorial is active, skipping announcement');
            }
            return;
          }

          // Only show announcements if the watchlist tutorial is completed
          if (!isTutorialCompleted(WATCHLIST_TUTORIAL_ID)) {
            if (SHOW_LOGS) {
              console.log('[Announcement] Watchlist tutorial not completed, skipping announcement');
            }
            return;
          }

          // Get announcement data from i18n
          const logs = (t('logs', { returnObjects: true }) || []) as any[];
          const announcements: AnnouncementData[] = logs.map(log => ({
            version: log.version,
            date: log.date,
            title: log.title || `Version ${log.version}`,
            features: log.features || [],
            improvements: log.improvements || [],
            fixes: log.fixes || [],
          }));

          // Check if we should show an announcement
          // Simple logic: Show latest announcement if app version changed
          const { shouldShow, announcement: announcementToShow } =
            await announcementService.shouldShowAnnouncement(announcements);

          if (shouldShow && announcementToShow) {
            if (SHOW_LOGS) {
              console.log(
                '[Announcement] Showing announcement for version:',
                announcementToShow.version
              );
            }

            // Close all bottom sheet modals before showing announcement modal
            // This prevents rendering conflicts on Android devices
            try {
              if (SHOW_LOGS) {
                console.log(
                  '[Announcement] Dismissing any open bottom sheets before showing announcement'
                );
              }
              await bottomSheetRegistry.dismissAll();
            } catch (error) {
              console.warn('[Announcement] Error dismissing bottom sheets:', error);
              // Continue showing announcement even if dismissal fails
            }

            setAnnouncement(announcementToShow);
            setShowModal(true);
          } else {
            if (SHOW_LOGS) {
              console.log('[Announcement] No announcement to show');
            }
          }
        } catch (error) {
          console.error('[Announcement] Error checking for announcements:', error);
        } finally {
          isCheckingRef.current = false;
        }
      });
    };

    // Calculate delay based on context
    let actualDelay = delay;

    // If tutorial just completed (no active tutorial but tutorial is completed),
    // add extra delay to allow cleanup
    if (!activeTutorial && isTutorialCompleted(WATCHLIST_TUTORIAL_ID)) {
      actualDelay = Math.max(delay, 3000); // At least 3 seconds after tutorial completion
      if (SHOW_LOGS) {
        console.log('[Announcement] Tutorial completed, using extended delay:', actualDelay);
      }
    }

    // Add delay to let app initialize properly and avoid race conditions
    timeoutRef.current = setTimeout(checkForAnnouncements, actualDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      isCheckingRef.current = false;
    };
  }, [isAuthenticated, requireAuthentication, delay, t, activeTutorial, isTutorialCompleted]);

  const handleCloseModal = async () => {
    if (announcement) {
      // Mark the current app version as shown (not the announcement version)
      const currentVersion = announcementService.getCurrentAppVersion();
      await announcementService.markAnnouncementAsShown(currentVersion);
    }
    setShowModal(false);
    setAnnouncement(null);
  };

  return (
    <AnnouncementModal visible={showModal} announcement={announcement} onClose={handleCloseModal} />
  );
};

export default AnnouncementManager;
