import { AnnouncementData } from '@/lib/announcement/announcement.service';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import Modal from 'react-native-modal';

import React, { useEffect, useRef } from 'react';

import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ScrollView,
  Animated,
  Dimensions,
} from 'react-native';

interface AnnouncementModalProps {
  visible: boolean;
  announcement: AnnouncementData | null;
  onClose: () => void;
  onViewChangelog?: () => void;
}

const AnnouncementModal: React.FC<AnnouncementModalProps> = ({
  visible,
  announcement,
  onClose,
  onViewChangelog,
}) => {
  const { t } = useTranslation('update-log');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const { width: screenWidth } = Dimensions.get('window');

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleClose = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClose();
  };

  const handleViewChangelog = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClose();
    if (onViewChangelog) {
      onViewChangelog();
    } else {
      router.push('/setting/update-log');
    }
  };

  if (!announcement) return null;

  const renderFeatureItem = (feature: string, index: number) => {
    return (
      <Animated.View
        key={index}
        style={{
          opacity: fadeAnim,
          transform: [
            {
              translateY: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            },
          ],
        }}
        className="mb-4 flex-row items-start">
        <View className="mr-3 mt-1.5 h-2 w-2 rounded-full bg-theme" />
        <Text className="flex-1 text-base text-gray-700">{feature}</Text>
      </Animated.View>
    );
  };

  return (
    <Modal
      isVisible={visible}
      backdropTransitionOutTiming={0}
      hideModalContentWhileAnimating={true}
      useNativeDriverForBackdrop={true}
      animationIn="fadeIn"
      animationOut="fadeOut"
      onBackdropPress={handleClose}
      style={{ margin: 0, justifyContent: 'center', alignItems: 'center' }}>
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        }}
        className="mx-6 max-h-[80%] w-[90%] max-w-md overflow-hidden rounded-3xl bg-white shadow-2xl">
        {/* Header Section */}
        <View className="relative items-center bg-gradient-to-b from-theme/5 to-transparent px-6 pb-4 pt-8">
          {/* Close Button */}
          <TouchableOpacity
            onPress={handleClose}
            className="absolute right-4 top-4 h-8 w-8 items-center justify-center rounded-full bg-black/5"
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
            <Ionicons name="close" size={18} color="#666" />
          </TouchableOpacity>

          {/* App Logo */}
          <View className="mb-4 h-16 w-16 items-center justify-center rounded-2xl bg-white shadow-lg shadow-gray-200">
            <Image
              source={require('@/assets/logo/icon.png')}
              className="h-12 w-12 rounded-lg"
              resizeMode="contain"
            />
          </View>

          {/* Title */}
          <Text className="mb-2 text-center text-2xl font-bold text-gray-800">
            {announcement.title}
          </Text>

          {/* Version Badge */}
          <View className="rounded-full bg-theme/10 px-4 py-1.5">
            <Text className="text-sm font-medium text-theme">
              {t('announcement.version', { version: announcement.version })}
            </Text>
          </View>
        </View>

        {/* Content Section */}
        <ScrollView className="max-h-80 px-6" showsVerticalScrollIndicator={false}>
          {/* What's New Section */}
          <View className="mb-2">
            <Text className="mb-4 text-lg font-semibold text-gray-800">
              {t('announcement.whatsNew')}
            </Text>

            {announcement.features.map((feature, index) => renderFeatureItem(feature, index))}
          </View>

          {/* Improvements Section */}
          {announcement.improvements && announcement.improvements.length > 0 && (
            <View className="mb-2">
              <Text className="mb-4 text-lg font-semibold text-gray-800">
                {t('announcement.improvements')}
              </Text>
              {announcement.improvements.map((improvement, index) => (
                <View key={index} className="mb-3 flex-row items-start">
                  <View className="mr-3 mt-1.5 h-2 w-2 rounded-full bg-theme" />
                  <Text className="flex-1 text-base text-gray-600">{improvement}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Fixes Section */}
          {announcement.fixes && announcement.fixes.length > 0 && (
            <View className="mb-4">
              <Text className="mb-4 text-lg font-semibold text-gray-800">
                {t('announcement.bugFixes')}
              </Text>
              {announcement.fixes.map((fix, index) => (
                <View key={index} className="mb-3 flex-row items-start">
                  <View className="mr-3 mt-1.5 h-2 w-2 rounded-full bg-theme" />
                  <Text className="flex-1 text-base text-gray-600">{fix}</Text>
                </View>
              ))}
            </View>
          )}
        </ScrollView>

        {/* Action Buttons */}
        <View className="mb-2 border-t border-gray-100 px-6 py-4">
          <TouchableOpacity
            onPress={handleViewChangelog}
            className="w-full items-center justify-center rounded-2xl bg-theme py-4">
            <Text className="text-base font-semibold text-white">
              {t('announcement.viewFullChangelog')}
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </Modal>
  );
};

export default AnnouncementModal;
