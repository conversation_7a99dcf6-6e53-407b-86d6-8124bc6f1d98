import { FontAwesome5 } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { router, useFocusEffect } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'react-native-gesture-handler';
import { Searchbar, Snackbar, List } from 'react-native-paper';
import { TabScreen, Tabs, TabsProvider } from 'react-native-paper-tabs';
import { z } from 'zod';

import { useState, useEffect, useCallback } from 'react';

import {
  View,
  Text,
  ScrollView,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  Image,
  TouchableOpacity,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useReportManage, useReportSubscribe, REPORT_KEYS } from '@/hooks/useReport';
import { useCompanySearch } from '@/hooks/useStock';

import { COLORS } from '@/constants/colors';

import ReportLanguagePicker from '@/components/common/ReportLanguagePicker';

import Button from '../shared/Button';

const MAX_INPUT_LENGTH = 50;

const RequiredMark = () => <Text className="ml-0.5 text-red-500">*</Text>;

const UpdateFormSchema = z.object({
  email: z
    .string()
    .email({ message: 'report.common.validation.invalidEmail' })
    .max(MAX_INPUT_LENGTH, { message: 'report.common.validation.tooLong' }),
  language: z.string().min(1, { message: 'report.common.validation.optionRequired' }),
  stocks: z.array(z.string()).min(1, { message: 'report.common.validation.stockRequired' }),
});

type FormData = z.infer<typeof UpdateFormSchema>;

// Popular stocks list
const popularStocks = [
  { symbol: 'META', name: 'Meta Platforms Inc.' },
  { symbol: 'MSFT', name: 'Microsoft Corporation' },
  { symbol: 'AMZN', name: 'Amazon.com Inc.' },
  { symbol: 'AAPL', name: 'Apple Inc.' },
  { symbol: 'GOOGL', name: 'Alphabet Inc.' },
  { symbol: 'NVDA', name: 'NVIDIA Corporation' },
  { symbol: 'TSLA', name: 'Tesla Inc.' },
];

type UpdateFormProps = {
  email: string;
};

export default function UpdateForm({ email }: UpdateFormProps) {
  const { t } = useTranslation();
  const { hasPermissionTo } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [selectedStocks, setSelectedStocks] = useState<string[]>([]);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<{
    type: 'success' | 'warning';
    message: string;
  } | null>(null);
  const { data: searchResults, isLoading } = useCompanySearch(searchQuery);
  const {
    data: reportData,
    isLoading: isReportLoading,
    isFetching: isReportFetching,
  } = useReportManage(email);
  const { mutate: subscribeReport, isPending: isSubmitting } = useReportSubscribe();
  const queryClient = useQueryClient();

  const maxStocks = hasPermissionTo('free_report_2024') ? 6 : 4;

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(UpdateFormSchema),
    defaultValues: async () => {
      // Wait for the initial data to be loaded
      if (reportData?.data.user) {
        const { language_code, currentSymbolList } = reportData.data.user;
        return {
          email,
          language: language_code,
          stocks: currentSymbolList,
        };
      }
      return {
        email,
        language: '',
        stocks: [],
      };
    },
  });

  // Reset form when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (reportData?.data.user) {
        const { language_code, currentSymbolList } = reportData.data.user;
        reset({
          email,
          stocks: currentSymbolList,
          language: language_code,
        });
        setSelectedStocks(currentSymbolList);
      }
    }, [reportData, reset])
  );

  // Fill form with user data when report data is loaded
  useEffect(() => {
    if (reportData?.data.user) {
      const { currentSymbolList } = reportData.data.user;
      // Only keep the UI state sync
      setSelectedStocks(currentSymbolList);
    }
  }, [reportData]);

  const handleStockToggle = (stock: { symbol: string }) => {
    let newSelectedStocks;
    if (selectedStocks.includes(stock.symbol)) {
      newSelectedStocks = selectedStocks.filter(s => s !== stock.symbol);
    } else {
      if (selectedStocks.length >= maxStocks) {
        setSnackbarMessage({
          type: 'warning',
          message: t('report.common.stockSelection.maxStocksWarning', { maxStocks }),
        });
        setSnackbarVisible(true);
        return;
      }
      newSelectedStocks = [...selectedStocks, stock.symbol];
    }
    setSelectedStocks(newSelectedStocks);
    setValue('stocks', newSelectedStocks, {
      shouldDirty: true,
    });
  };

  const handleOutsidePress = () => {
    if (isSearchFocused) {
      setIsSearchFocused(false);
      Keyboard.dismiss();
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!reportData?.data.user) return;

    const { first_name, last_name } = reportData.data.user;

    try {
      await subscribeReport(
        {
          firstName: first_name,
          lastName: last_name,
          email: data.email,
          language: data.language,
          stocks: data.stocks,
        },
        {
          onSuccess: () => {
            setSnackbarMessage({
              type: 'success',
              message: t('report.updateForm.messages.success'),
            });
            setSnackbarVisible(true);
            queryClient.refetchQueries({
              queryKey: REPORT_KEYS.reportManage(email),
            });
          },
          onError: error => {
            console.error('Update Error:', error);
            setSnackbarMessage({
              type: 'warning',
              message: t('report.updateForm.messages.error'),
            });
            setSnackbarVisible(true);
          },
        }
      );
    } catch (error) {
      console.error('Submit Error:', error);
    }
  };

  const handleFormSubmit = () => {
    handleSubmit(onSubmit)();
  };

  const openReport = async (url: string) => {
    await WebBrowser.openBrowserAsync(url);
  };

  if (isReportLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  const ReportsTab = () => (
    <ScrollView className="flex-1 bg-white">
      <View className="p-4">
        {reportData?.data.reportList && reportData.data.reportList.length > 0 && (
          <View>
            <List.Section>
              {reportData.data.reportList.map(report => (
                <List.Accordion
                  key={report.date}
                  title={t('report.updateForm.reports.weeklyReport', { date: report.date })}
                  left={props => (
                    <List.Icon {...props} icon="file-document-outline" color="#FF9339" />
                  )}
                  theme={{
                    colors: {
                      primary: '#FF9339',
                    },
                  }}
                  style={{
                    backgroundColor: '#FFF5EB',
                  }}
                  titleStyle={{
                    color: '#000000',
                  }}>
                  {report.stockList.map(stock => (
                    <View
                      key={stock.symbol}
                      className="mx-2 mb-2 flex-row items-center justify-between rounded-lg bg-gray-100 p-4">
                      <View className="flex-1 flex-row items-center">
                        <Image source={{ uri: stock.logo }} className="h-12 w-12 rounded-full" />
                        <View className="ml-3 flex-1">
                          <Text className="font-medium text-gray-900" numberOfLines={1}>
                            {stock.companyName}
                          </Text>
                          <Text className="text-sm text-gray-600">
                            {t('report.updateForm.stockInfo.exchange', {
                              symbol: stock.symbol,
                              exchangeShortName: stock.exchangeShortName,
                            })}
                          </Text>
                        </View>
                      </View>
                      <TouchableOpacity
                        onPress={() => openReport(stock.reportUrl)}
                        className="ml-2 rounded-lg bg-theme px-4 py-2">
                        <Text className="text-white">{t('report.updateForm.button.view')}</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </List.Accordion>
              ))}
            </List.Section>
          </View>
        )}
      </View>
    </ScrollView>
  );

  return (
    <View className="flex-1 bg-white">
      <TabsProvider defaultIndex={0}>
        <Tabs
          uppercase={false}
          style={{ backgroundColor: '#FFFFFF' }}
          theme={{
            colors: {
              primary: '#FF9339',
            },
          }}
          mode="fixed">
          <TabScreen label={t('report.updateForm.tabs.reports')} icon="file-document-outline">
            <ReportsTab />
          </TabScreen>
          <TabScreen label={t('report.updateForm.tabs.update')} icon="pen">
            <View className="flex-1">
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                className="flex-1 bg-white"
                keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
                <ScrollView
                  keyboardShouldPersistTaps="always"
                  contentContainerStyle={{ flexGrow: 1 }}
                  onScrollBeginDrag={() => {
                    Keyboard.dismiss();
                    setIsSearchFocused(false);
                  }}>
                  <TouchableWithoutFeedback onPress={() => handleOutsidePress()}>
                    <View className="flex-1 p-4">
                      {/* Survey Promotion - Only show if user doesn't have free_report_2024 permission */}
                      {!hasPermissionTo('free_report_2024') && (
                        <View className="mb-4 overflow-hidden rounded-xl bg-zinc-50 shadow shadow-gray-100">
                          <View className="p-4">
                            <View className="mb-3 flex-row items-center">
                              <View className="mr-2 rounded-full bg-orange-100 p-2">
                                <FontAwesome5 name="gift" size={16} color="#FF9339" />
                              </View>
                              <Text className="text-lg font-semibold text-gray-800">
                                {t('stock.usage.survey.title', 'Unlock More Features')}
                              </Text>
                            </View>
                            <Text className="mb-3 text-base text-gray-600">
                              {t(
                                'stock.usage.survey.promotion',
                                'Share your feedback through our quick survey and get extended daily usage limits.'
                              )}
                            </Text>
                            <Button
                              variant="primary"
                              onPress={() => {
                                router.push('/(tabs)/setting');
                              }}
                              className="mt-1">
                              {t('stock.usage.survey.button', 'Take Quick Survey')}
                            </Button>
                          </View>
                          <View className="absolute -right-6 -top-6 rotate-12 opacity-10">
                            <FontAwesome5 name="chart-line" size={96} color="#FFB339" />
                          </View>
                        </View>
                      )}

                      {/* Stocks Selection */}
                      <View className="mb-4">
                        <View className="mb-2 flex-row items-center">
                          <Text className="text-gray-700">
                            {t('report.common.stockSelection.label')}
                          </Text>
                          <RequiredMark />
                          <Text className="ml-2 text-gray-500">{`(${selectedStocks.length}/${maxStocks})`}</Text>
                        </View>
                        <View>
                          <Searchbar
                            placeholder={t('report.common.stockSelection.searchPlaceholder')}
                            onChangeText={setSearchQuery}
                            value={searchQuery}
                            className="mb-2"
                            style={{
                              backgroundColor: 'white',
                              borderColor: '#d1d5db',
                              borderWidth: 1,
                              borderRadius: 10,
                              height: 40,
                              minHeight: 40,
                            }}
                            inputStyle={{
                              fontSize: 14,
                              minHeight: 38,
                              paddingVertical: 0,
                            }}
                            iconColor="gray"
                            onFocus={() => setIsSearchFocused(true)}
                          />

                          {/* Selected stocks list */}
                          {selectedStocks.length > 0 && (
                            <View className="mt-2 flex-row flex-wrap">
                              {selectedStocks.map(symbol => (
                                <TouchableOpacity
                                  key={symbol}
                                  onPress={() => handleStockToggle({ symbol })}>
                                  <View className="m-1 flex-row items-center rounded-full bg-theme-light px-3 py-1">
                                    <Text className="mr-2">{symbol}</Text>
                                    <FontAwesome name="times-circle" size={16} color="#4B5563" />
                                  </View>
                                </TouchableOpacity>
                              ))}
                            </View>
                          )}

                          {/* Stocks list */}
                          {(isSearchFocused || searchQuery) && (
                            <View className="max-h-80 rounded-lg border border-gray-300">
                              {searchQuery ? (
                                // Search results list
                                isLoading ? (
                                  <ActivityIndicator className="py-4" />
                                ) : searchResults && searchResults.length > 0 ? (
                                  <ScrollView keyboardShouldPersistTaps="always">
                                    {searchResults.map(stock => (
                                      <TouchableOpacity
                                        key={stock.symbol}
                                        onPress={() => handleStockToggle(stock)}>
                                        <View className="flex-row items-center justify-between border-b border-gray-200 p-3">
                                          <View>
                                            <Text className="font-bold">{stock.symbol}</Text>
                                            <Text className="text-gray-600">{stock.name}</Text>
                                          </View>
                                          {selectedStocks.includes(stock.symbol) && (
                                            <FontAwesome name="check" size={16} color="green" />
                                          )}
                                        </View>
                                      </TouchableOpacity>
                                    ))}
                                  </ScrollView>
                                ) : (
                                  <Text className="p-3 text-gray-500">
                                    {t('report.common.stockSelection.noResults')}
                                  </Text>
                                )
                              ) : (
                                // Default popular stocks list
                                <ScrollView keyboardShouldPersistTaps="always">
                                  {popularStocks.map(stock => (
                                    <TouchableOpacity
                                      key={stock.symbol}
                                      onPress={() => handleStockToggle(stock)}>
                                      <View className="flex-row items-center justify-between border-b border-gray-200 p-3">
                                        <View>
                                          <Text className="font-bold">{stock.symbol}</Text>
                                          <Text className="text-gray-600">{stock.name}</Text>
                                        </View>
                                        {selectedStocks.includes(stock.symbol) && (
                                          <FontAwesome name="check" size={16} color="green" />
                                        )}
                                      </View>
                                    </TouchableOpacity>
                                  ))}
                                </ScrollView>
                              )}
                            </View>
                          )}
                        </View>

                        {errors.stocks && (
                          <Text className="mt-1 text-red-500">
                            {errors.stocks.message ? t(errors.stocks.message as string) : ''}
                          </Text>
                        )}
                      </View>

                      {/* Language */}
                      <View className="mb-4">
                        <View className="mb-2 flex-row items-center">
                          <Text className="text-gray-700">{t('report.common.language.label')}</Text>
                          <RequiredMark />
                        </View>
                        <Controller
                          control={control}
                          name="language"
                          render={({ field: { onChange, value } }) => (
                            <ReportLanguagePicker
                              onLanguageChange={selected => {
                                onChange(selected.value);
                              }}
                              value={value}
                            />
                          )}
                        />
                        {errors.language && (
                          <Text className="mt-1 text-red-500">
                            {errors.language.message ? t(errors.language.message as string) : ''}
                          </Text>
                        )}
                      </View>

                      {/* Email */}
                      <View className="mb-4">
                        <View className="mb-2 flex-row items-center">
                          <Text className="text-gray-700">
                            {t('report.updateForm.personalInfo.email.label')}
                          </Text>
                          <RequiredMark />
                        </View>
                        <Controller
                          control={control}
                          name="email"
                          render={({ field: { value } }) => (
                            <TextInput
                              className="rounded-lg border border-gray-300 bg-gray-100 p-3"
                              value={value}
                              editable={false}
                              keyboardType="email-address"
                              autoCapitalize="none"
                              inputAccessoryViewID="email"
                            />
                          )}
                        />
                        {errors.email && (
                          <Text className="mt-1 text-red-500">
                            {errors.email.message ? t(errors.email.message as string) : ''}
                          </Text>
                        )}
                      </View>

                      <View className="mt-6">
                        {isSubmitting ? (
                          <View className="rounded-lg bg-theme py-3 opacity-70">
                            <ActivityIndicator color="white" />
                          </View>
                        ) : (
                          <Button
                            onPress={handleFormSubmit}
                            disabled={!isDirty}
                            loading={isSubmitting || isReportFetching}>
                            {!isDirty
                              ? t('report.updateForm.button.makeChangesToUpdate')
                              : t('report.updateForm.button.update')}
                          </Button>
                        )}
                      </View>
                    </View>
                  </TouchableWithoutFeedback>
                </ScrollView>
              </KeyboardAvoidingView>
            </View>
          </TabScreen>
        </Tabs>
      </TabsProvider>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => {
          setSnackbarVisible(false);
          setTimeout(() => {
            setSnackbarMessage(null);
          }, 200);
        }}
        duration={2000}
        style={{
          backgroundColor: snackbarMessage?.type === 'success' ? COLORS.theme : '#ef4444',
          borderRadius: 8,
        }}>
        <View className="flex-row items-center">
          <FontAwesome
            name={snackbarMessage?.type === 'success' ? 'check-circle' : 'exclamation-circle'}
            size={16}
            color="#FFFFFF"
            style={{ marginRight: 8 }}
          />
          <Text className="text-white">{snackbarMessage?.message || ''}</Text>
        </View>
      </Snackbar>
    </View>
  );
}
