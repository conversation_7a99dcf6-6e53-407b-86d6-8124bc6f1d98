import FontAwesome from '@expo/vector-icons/FontAwesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'react-native-gesture-handler';
import { Searchbar, Snackbar } from 'react-native-paper';
import { z } from 'zod';

import { useState } from 'react';

import {
  View,
  Text,
  ScrollView,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useReportSubscribe, REPORT_KEYS } from '@/hooks/useReport';
import { useCompanySearch } from '@/hooks/useStock';

import { COLORS } from '@/constants/colors';

import ReportLanguagePicker from '@/components/common/ReportLanguagePicker';
import Button from '@/components/shared/Button';

const MAX_INPUT_LENGTH = 50;

const RequiredMark = () => <Text className="ml-0.5 text-red-500">*</Text>;

const SubscribeFormSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: 'report.common.validation.required' })
    .max(MAX_INPUT_LENGTH, { message: 'report.common.validation.tooLong' }),
  lastName: z
    .string()
    .min(1, { message: 'report.common.validation.required' })
    .max(MAX_INPUT_LENGTH, { message: 'report.common.validation.tooLong' }),
  email: z.string().email({ message: 'report.common.validation.invalidEmail' }),
  language: z.string().min(1, { message: 'report.common.validation.optionRequired' }),
  stocks: z.array(z.string()).min(1, { message: 'report.common.validation.stockRequired' }),
});

type FormData = z.infer<typeof SubscribeFormSchema>;

// Popular stocks list
const popularStocks = [
  { symbol: 'META', name: 'Meta Platforms Inc.' },
  { symbol: 'MSFT', name: 'Microsoft Corporation' },
  { symbol: 'AMZN', name: 'Amazon.com Inc.' },
  { symbol: 'AAPL', name: 'Apple Inc.' },
  { symbol: 'GOOGL', name: 'Alphabet Inc.' },
  { symbol: 'NVDA', name: 'NVIDIA Corporation' },
  { symbol: 'TSLA', name: 'Tesla Inc.' },
];

type SubscribeFormProps = {
  defaultEmail: string;
  defaultFirstName: string;
  defaultLastName: string;
};

export default function SubscribeForm({
  defaultEmail,
  defaultFirstName,
  defaultLastName,
}: SubscribeFormProps) {
  const { t } = useTranslation();
  const { hasPermissionTo } = useAuth();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [selectedStocks, setSelectedStocks] = useState<string[]>([]);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<{
    type: 'success' | 'warning';
    message: string;
  } | null>(null);
  const { data: searchResults, isLoading } = useCompanySearch(searchQuery);
  const { mutate: subscribeReport, isPending: isSubmitting } = useReportSubscribe();

  const maxStocks = hasPermissionTo('free_report_2024') ? 6 : 4;

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<FormData>({
    resolver: zodResolver(SubscribeFormSchema),
    defaultValues: {
      firstName: defaultFirstName,
      lastName: defaultLastName,
      email: defaultEmail,
      language: '',
      stocks: [],
    },
  });

  const handleStockToggle = (stock: { symbol: string; name: string }) => {
    let newSelectedStocks;
    if (selectedStocks.includes(stock.symbol)) {
      newSelectedStocks = selectedStocks.filter(s => s !== stock.symbol);
    } else {
      if (selectedStocks.length >= maxStocks) {
        setSnackbarMessage({
          type: 'warning',
          message: t('report.common.stockSelection.maxStocksWarning', { maxStocks }),
        });
        setSnackbarVisible(true);
        return;
      }
      newSelectedStocks = [...selectedStocks, stock.symbol];
    }
    setSelectedStocks(newSelectedStocks);
    setValue('stocks', newSelectedStocks, {
      shouldDirty: true,
    });
  };

  const handleOutsidePress = () => {
    if (isSearchFocused) {
      Keyboard.dismiss();
      setIsSearchFocused(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      await subscribeReport(
        {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          language: data.language,
          stocks: data.stocks,
        },
        {
          onSuccess: () => {
            setSnackbarMessage({
              type: 'success',
              message: t('report.subscribeForm.messages.success'),
            });
            setSnackbarVisible(true);
            queryClient.refetchQueries({
              queryKey: REPORT_KEYS.reportManage(data.email),
            });
          },
          onError: error => {
            console.error('Subscribe Error:', error);
            setSnackbarMessage({
              type: 'warning',
              message: t('report.subscribeForm.messages.error'),
            });
            setSnackbarVisible(true);
          },
        }
      );
    } catch (error) {
      console.error('Submit Error:', error);
    }
  };

  const handleFormSubmit = () => {
    handleSubmit(onSubmit)();
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white">
      <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <View className="flex-1">
          <ScrollView keyboardShouldPersistTaps="handled">
            <View className="p-4">
              <Text className="mb-6 text-2xl font-bold">{t('report.subscribeForm.title')}</Text>

              {/* Stocks Selection */}
              <View className="mb-4">
                <View className="mb-2 flex-row items-center">
                  <Text className="text-gray-700">{t('report.common.stockSelection.label')}</Text>
                  <RequiredMark />
                  <Text className="ml-2 text-gray-500">{`(${selectedStocks.length}/${maxStocks})`}</Text>
                </View>
                <Searchbar
                  placeholder={t('report.common.stockSelection.searchPlaceholder')}
                  onChangeText={setSearchQuery}
                  value={searchQuery}
                  className="mb-2"
                  style={{
                    backgroundColor: 'white',
                    borderColor: '#d1d5db',
                    borderWidth: 1,
                    borderRadius: 10,
                    height: 40,
                    minHeight: 40,
                  }}
                  inputStyle={{
                    fontSize: 14,
                    minHeight: 38,
                    paddingVertical: 0,
                  }}
                  iconColor="gray"
                  onFocus={() => setIsSearchFocused(true)}
                />

                {/* Selected stocks list */}
                {selectedStocks.length > 0 && (
                  <View className="mt-2 flex-row flex-wrap">
                    {selectedStocks.map(stock => (
                      <TouchableWithoutFeedback
                        key={stock}
                        onPress={() => handleStockToggle({ symbol: stock, name: '' })}>
                        <View className="m-1 flex-row items-center rounded-full bg-theme-light px-3 py-1">
                          <Text className="mr-2">{stock}</Text>
                          <FontAwesome name="times-circle" size={16} color="#4B5563" />
                        </View>
                      </TouchableWithoutFeedback>
                    ))}
                  </View>
                )}

                {/* Stocks list */}
                {(isSearchFocused || searchQuery) && (
                  <TouchableWithoutFeedback>
                    <View className="max-h-48 rounded-lg border border-gray-300">
                      {searchQuery ? (
                        // Search results list
                        isLoading ? (
                          <ActivityIndicator className="py-4" />
                        ) : searchResults && searchResults.length > 0 ? (
                          <ScrollView>
                            {searchResults.map(stock => (
                              <TouchableWithoutFeedback
                                key={stock.symbol}
                                onPress={() => handleStockToggle(stock)}>
                                <View className="flex-row items-center justify-between border-b border-gray-200 p-3">
                                  <View>
                                    <Text className="font-bold">{stock.symbol}</Text>
                                    <Text className="text-gray-600">{stock.name}</Text>
                                  </View>
                                  {selectedStocks.includes(stock.symbol) && (
                                    <FontAwesome name="check" size={16} color="green" />
                                  )}
                                </View>
                              </TouchableWithoutFeedback>
                            ))}
                          </ScrollView>
                        ) : (
                          <Text className="p-3 text-gray-500">
                            {t('report.common.stockSelection.noResults')}
                          </Text>
                        )
                      ) : (
                        // Default popular stocks list
                        <ScrollView>
                          {popularStocks.map(stock => (
                            <TouchableWithoutFeedback
                              key={stock.symbol}
                              onPress={() => handleStockToggle(stock)}>
                              <View className="flex-row items-center justify-between border-b border-gray-200 p-3">
                                <View>
                                  <Text className="font-bold">{stock.symbol}</Text>
                                  <Text className="text-gray-600">{stock.name}</Text>
                                </View>
                                {selectedStocks.includes(stock.symbol) && (
                                  <FontAwesome name="check" size={16} color="green" />
                                )}
                              </View>
                            </TouchableWithoutFeedback>
                          ))}
                        </ScrollView>
                      )}
                    </View>
                  </TouchableWithoutFeedback>
                )}

                {errors.stocks && (
                  <Text className="mt-1 text-red-500">
                    {errors.stocks.message ? t(errors.stocks.message as string) : ''}
                  </Text>
                )}
              </View>

              {/* Language */}
              <View className="mb-4">
                <View className="mb-2 flex-row items-center">
                  <Text className="text-gray-700">{t('report.common.language.label')}</Text>
                  <RequiredMark />
                </View>
                <Controller
                  control={control}
                  name="language"
                  render={({ field: { onChange, value } }) => (
                    <ReportLanguagePicker
                      onLanguageChange={selected => {
                        onChange(selected.value);
                      }}
                      value={value}
                    />
                  )}
                />
                {errors.language && (
                  <Text className="mt-1 text-red-500">
                    {errors.language.message ? t(errors.language.message as string) : ''}
                  </Text>
                )}
              </View>

              {/* First Name */}
              <View className="mb-4">
                <View className="mb-2 flex-row items-center">
                  <Text className="text-gray-700">
                    {t('report.subscribeForm.personalInfo.firstName.label')}
                  </Text>
                  <RequiredMark />
                </View>
                <Controller
                  control={control}
                  name="firstName"
                  render={({ field: { value } }) => (
                    <TextInput
                      className="rounded-lg border border-gray-300 bg-gray-100 p-3"
                      value={value}
                      editable={false}
                      inputAccessoryViewID="firstName"
                    />
                  )}
                />
                {errors.firstName && (
                  <Text className="mt-1 text-red-500">
                    {errors.firstName.message
                      ? t(errors.firstName.message as string, {
                          field: t('report.subscribeForm.personalInfo.firstName.label'),
                        })
                      : ''}
                  </Text>
                )}
              </View>

              {/* Last Name */}
              <View className="mb-4">
                <View className="mb-2 flex-row items-center">
                  <Text className="text-gray-700">
                    {t('report.subscribeForm.personalInfo.lastName.label')}
                  </Text>
                  <RequiredMark />
                </View>
                <Controller
                  control={control}
                  name="lastName"
                  render={({ field: { value } }) => (
                    <TextInput
                      className="rounded-lg border border-gray-300 bg-gray-100 p-3"
                      value={value}
                      editable={false}
                      inputAccessoryViewID="lastName"
                    />
                  )}
                />
                {errors.lastName && (
                  <Text className="mt-1 text-red-500">
                    {errors.lastName.message
                      ? t(errors.lastName.message as string, {
                          field: t('report.subscribeForm.personalInfo.lastName.label'),
                        })
                      : ''}
                  </Text>
                )}
              </View>

              {/* Email */}
              <View className="mb-4">
                <View className="mb-2 flex-row items-center">
                  <Text className="text-gray-700">
                    {t('report.subscribeForm.personalInfo.email.label')}
                  </Text>
                  <RequiredMark />
                </View>
                <Controller
                  control={control}
                  name="email"
                  render={({ field: { value } }) => (
                    <TextInput
                      className="rounded-lg border border-gray-300 bg-gray-100 p-3"
                      value={value}
                      editable={false}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      inputAccessoryViewID="email"
                    />
                  )}
                />
                {errors.email && (
                  <Text className="mt-1 text-red-500">
                    {errors.email.message ? t(errors.email.message as string) : ''}
                  </Text>
                )}
              </View>

              <View className="mt-6">
                {isSubmitting ? (
                  <View className="rounded-lg bg-theme py-3 opacity-70">
                    <ActivityIndicator color="white" />
                  </View>
                ) : (
                  <View className="flex-row gap-2">
                    <Button
                      variant="primary"
                      style={{ flex: 1 }}
                      onPress={handleFormSubmit}
                      loading={isSubmitting}>
                      {t('report.subscribeForm.button.subscribe')}
                    </Button>
                    <Button
                      variant="primary"
                      onPress={() => {
                        queryClient.refetchQueries({
                          queryKey: REPORT_KEYS.reportManage(defaultEmail),
                        });
                      }}>
                      <FontAwesome name="refresh" size={16} color="white" />
                    </Button>
                  </View>
                )}
              </View>
            </View>
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => {
          setSnackbarVisible(false);
          // Wait for the animation to complete before clearing the message
          setTimeout(() => {
            setSnackbarMessage(null);
          }, 200);
        }}
        duration={2000}
        style={{
          backgroundColor: snackbarMessage?.type === 'success' ? COLORS.theme : '#ef4444',
          borderRadius: 8,
        }}>
        <View className="flex-row items-center">
          <FontAwesome
            name={snackbarMessage?.type === 'success' ? 'check-circle' : 'exclamation-circle'}
            size={16}
            color="#FFFFFF"
            style={{ marginRight: 8 }}
          />
          <Text className="text-white">{snackbarMessage?.message || ''}</Text>
        </View>
      </Snackbar>
    </KeyboardAvoidingView>
  );
}
