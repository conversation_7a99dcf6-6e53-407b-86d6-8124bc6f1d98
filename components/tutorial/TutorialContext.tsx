import AsyncStorage from '@react-native-async-storage/async-storage';

import React, { createContext, useState, useContext, useEffect, ReactNode, useRef } from 'react';

import { InteractionManager, View, Text } from 'react-native';

import TutorialLayer from './TutorialLayer';

export const TUTORIAL_COMPLETED_KEY = 'tutorial_completed';

interface TutorialContextType {
  // Tutorial state
  activeTutorial: string | null;
  currentStep: number;
  completedTutorials: string[];
  isPreparingTutorial: boolean;

  // Tutorial methods
  startTutorial: (tutorialId: string, steps: TutorialStep[]) => void;
  setPreparingTutorial: (preparing: boolean) => void;
  nextStep: () => void;
  previousStep?: () => void;
  closeTutorial: () => void;
  completeTutorial: () => void;
  resetTutorial: (tutorialId: string) => Promise<void>;
  resetAllTutorials: () => Promise<void>;
  isTutorialCompleted: (tutorialId: string) => boolean;
}

export interface TutorialStep {
  targetRef: React.RefObject<any>;
  title: string;
  description: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  padding?: number;
  prepare?: () => Promise<void>;
  isHeaderComponent?: boolean;
  isBottomComponent?: boolean;
}

export const TutorialContext = createContext<TutorialContextType | undefined>(undefined);

interface TutorialProviderProps {
  children: ReactNode;
}

export const TutorialProvider: React.FC<TutorialProviderProps> = ({ children }) => {
  const [activeTutorial, setActiveTutorial] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [steps, setSteps] = useState<TutorialStep[]>([]);
  const [completedTutorials, setCompletedTutorials] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isPreparingTutorial, setIsPreparingTutorial] = useState(false);

  // Use a ref to store the total steps to avoid issues with stale state
  const totalStepsRef = useRef(0);

  // Debug flag
  const DEBUG = __DEV__ && false; // Set to true to enable debug logs

  // Initialize completed tutorials from AsyncStorage
  useEffect(() => {
    const initTutorials = async () => {
      try {
        const completedTutorialsJson = await AsyncStorage.getItem(TUTORIAL_COMPLETED_KEY);
        const loadedCompletedTutorials = completedTutorialsJson
          ? JSON.parse(completedTutorialsJson)
          : [];
        setCompletedTutorials(loadedCompletedTutorials);
        setIsInitialized(true);
      } catch (error) {
        console.error('Error loading completed tutorials:', error);
        setIsInitialized(true);
      }
    };

    initTutorials();
  }, []);

  const startTutorial = (tutorialId: string, tutorialSteps: TutorialStep[]) => {
    // Only start if not already completed
    if (!completedTutorials.includes(tutorialId)) {
      if (DEBUG) console.log(`Starting tutorial: ${tutorialId} with ${tutorialSteps.length} steps`);
      setActiveTutorial(tutorialId);
      setCurrentStep(0);
      setSteps(tutorialSteps);
      totalStepsRef.current = tutorialSteps.length;
    }
  };

  const setPreparingTutorial = (preparing: boolean) => {
    if (DEBUG || __DEV__) console.log(`Setting tutorial preparing: ${preparing}`);
    setIsPreparingTutorial(preparing);
  };

  const nextStep = () => {
    const totalSteps = totalStepsRef.current;
    if (DEBUG) console.log(`Next step: ${currentStep + 1}/${totalSteps}`);

    if (currentStep < totalSteps - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      // If we're at the last step, complete the tutorial
      if (DEBUG) console.log(`Completing tutorial at step: ${currentStep + 1}/${totalSteps}`);
      completeTutorial();
    }
  };

  const previousStep = () => {
    if (DEBUG) console.log(`Previous step: ${currentStep - 1}/${totalStepsRef.current}`);
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const closeTutorial = () => {
    if (DEBUG) console.log(`Closing tutorial: ${activeTutorial}`);
    setActiveTutorial(null);
    setSteps([]);
    setCurrentStep(0);
    totalStepsRef.current = 0;
    setIsPreparingTutorial(false);
  };

  const completeTutorial = async () => {
    if (activeTutorial) {
      if (DEBUG) console.log(`Completing tutorial: ${activeTutorial}`);
      try {
        const updatedCompletedTutorials = [...completedTutorials];
        if (!updatedCompletedTutorials.includes(activeTutorial)) {
          updatedCompletedTutorials.push(activeTutorial);
        }

        await AsyncStorage.setItem(
          TUTORIAL_COMPLETED_KEY,
          JSON.stringify(updatedCompletedTutorials)
        );

        // Use InteractionManager to ensure state updates happen after interactions complete
        InteractionManager.runAfterInteractions(() => {
          setCompletedTutorials(updatedCompletedTutorials);
          setActiveTutorial(null);
          setSteps([]);
          setCurrentStep(0);
          totalStepsRef.current = 0;
          setIsPreparingTutorial(false);
        });
      } catch (error) {
        console.error('Error completing tutorial:', error);
      }
    }
  };

  const resetTutorial = async (tutorialId: string) => {
    try {
      if (DEBUG) console.log(`Resetting tutorial: ${tutorialId}`);
      const updatedCompletedTutorials = completedTutorials.filter(id => id !== tutorialId);

      await AsyncStorage.setItem(TUTORIAL_COMPLETED_KEY, JSON.stringify(updatedCompletedTutorials));

      setCompletedTutorials(updatedCompletedTutorials);
    } catch (error) {
      console.error('Error resetting tutorial:', error);
      throw error;
    }
  };

  const resetAllTutorials = async () => {
    try {
      if (DEBUG) console.log('Resetting all tutorials');
      await AsyncStorage.removeItem(TUTORIAL_COMPLETED_KEY);
      setCompletedTutorials([]);
      setIsPreparingTutorial(false);
    } catch (error) {
      console.error('Error resetting all tutorials:', error);
      throw error;
    }
  };

  const isTutorialCompleted = (tutorialId: string) => {
    return completedTutorials.includes(tutorialId);
  };

  const value = {
    activeTutorial,
    currentStep,
    completedTutorials,
    isPreparingTutorial,
    startTutorial,
    setPreparingTutorial,
    nextStep,
    previousStep,
    closeTutorial,
    completeTutorial,
    resetTutorial,
    resetAllTutorials,
    isTutorialCompleted,
  };

  return (
    <TutorialContext.Provider value={value}>
      {children}

      {/* Global blocking overlay during tutorial preparation */}
      {isPreparingTutorial && (
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999999,
            backgroundColor: 'transparent',
          }}
          pointerEvents="box-only"
        />
      )}

      {activeTutorial && steps.length > 0 && (
        <TutorialLayer
          steps={steps}
          visible={!!activeTutorial}
          currentStep={currentStep}
          onClose={closeTutorial}
          onNext={nextStep}
          onFinish={completeTutorial}
        />
      )}
    </TutorialContext.Provider>
  );
};

export const useTutorial = () => {
  const context = useContext(TutorialContext);
  if (context === undefined) {
    throw new Error('useTutorial must be used within a TutorialProvider');
  }
  return context;
};
