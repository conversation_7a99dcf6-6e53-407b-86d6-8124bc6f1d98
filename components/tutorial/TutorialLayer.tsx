import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import React, { useRef, useState, useEffect } from 'react';

import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  ViewStyle,
  TextStyle,
  InteractionManager,
} from 'react-native';

import { COLORS } from '@/constants/colors';

export interface TutorialStep {
  targetRef: React.RefObject<any>;
  title: string;
  description: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  padding?: number;
  prepare?: () => Promise<void>;
  isHeaderComponent?: boolean;
  isBottomComponent?: boolean;
}

interface TutorialLayerProps {
  steps: TutorialStep[];
  visible: boolean;
  currentStep: number;
  onClose: () => void;
  onNext: () => void;
  onPrevious?: () => void;
  onFinish: () => void;
  overlayColor?: string;
  highlightBorderColor?: string;
  textColor?: string;
  titleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
  allowTapToDismiss?: boolean;
}

const TutorialLayer: React.FC<TutorialLayerProps> = ({
  steps,
  visible,
  currentStep,
  onClose,
  onNext,
  onPrevious,
  onFinish,
  overlayColor = 'rgba(0, 0, 0, 0.7)',
  highlightBorderColor = COLORS.theme,
  textColor = '#FFFFFF',
  titleStyle,
  descriptionStyle,
  allowTapToDismiss = false,
}) => {
  const [targetMeasurements, setTargetMeasurements] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [previousPosition, setPreviousPosition] = useState<ViewStyle | null>(null);
  const [screenDimensions, setScreenDimensions] = useState({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  });

  // Get translations
  const { t } = useTranslation();

  // Debug flag - temporarily enabled for troubleshooting
  const DEBUG = __DEV__ && false; // Set to true to enable debug logs

  // Store the last step in a ref to avoid stale closures
  const lastStepRef = useRef(currentStep);

  const insets = useSafeAreaInsets();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const tooltipFadeAnim = useRef(new Animated.Value(0)).current;
  const tooltipPositionX = useRef(new Animated.Value(0)).current;
  const tooltipPositionY = useRef(new Animated.Value(0)).current;

  // Validate current step to prevent out-of-bounds errors
  const safeCurrentStep = Math.min(Math.max(0, currentStep), steps.length - 1);

  // Update lastStepRef when currentStep changes
  useEffect(() => {
    lastStepRef.current = safeCurrentStep;
  }, [safeCurrentStep]);

  // Update screen dimensions when layout changes
  useEffect(() => {
    const updateDimensions = () => {
      setScreenDimensions({
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
      });
    };

    const dimensionsListener = Dimensions.addEventListener('change', updateDimensions);

    return () => {
      dimensionsListener.remove();
    };
  }, []);

  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      if (DEBUG)
        console.log(`Tutorial visible, showing step ${safeCurrentStep + 1}/${steps.length}`);
      // Delay measuring until after the component has rendered
      InteractionManager.runAfterInteractions(() => {
        setTimeout(() => {
          measureTarget();
          setIsReady(true);

          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
          ]).start(() => {
            // Only show tooltip after overlay is fully visible
            setTooltipVisible(true);
            Animated.timing(tooltipFadeAnim, {
              toValue: 1,
              duration: 200,
              useNativeDriver: true,
            }).start();
          });
        }, 100);
      });
    } else {
      setIsReady(false);
      setTooltipVisible(false);
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Ensure measurements are updated when currentStep changes directly
  useEffect(() => {
    if (visible && isReady && !isTransitioning) {
      // We need to update measurements when the step changes directly
      // This is a fallback for cases where the transition effect doesn't catch it
      if (DEBUG) console.log(`Current step changed directly to ${safeCurrentStep + 1}`);
      measureTarget();
    }
  }, [safeCurrentStep, visible, isReady]);

  // Handle step changes with smooth transitions
  useEffect(() => {
    // Only handle transitions if the step actually changed
    if (visible && isReady && steps[safeCurrentStep] && lastStepRef.current !== safeCurrentStep) {
      if (DEBUG) console.log(`Transitioning to step ${safeCurrentStep + 1}/${steps.length}`);
      setIsTransitioning(true);

      // First, hide the tooltip completely
      setTooltipVisible(false);
      Animated.timing(tooltipFadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => {
        // After tooltip is hidden, measure the new target
        setTimeout(() => {
          // Reset target measurements to force a clean measurement
          setTargetMeasurements(null);
          measureTarget();

          // After measurement is complete, wait a bit to ensure measurements are applied
          setTimeout(() => {
            // Now show the tooltip at the new position
            setTooltipVisible(true);
            Animated.timing(tooltipFadeAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }).start(() => {
              setIsTransitioning(false);
              // Update lastStepRef after transition is complete
              lastStepRef.current = safeCurrentStep;
            });
          }, 300);
        }, 50);
      });
    }
  }, [safeCurrentStep, steps, visible, isReady]);

  const measureTarget = () => {
    const currentStep = steps[safeCurrentStep];
    const maxRetries = 30; // Increased max retries
    const retryInterval = 100; // Shorter intervals for more responsive polling

    // Helper function to perform the actual measurement with retries
    const performMeasurement = (retryCount = 0) => {
      if (retryCount >= maxRetries) {
        console.warn(
          `Failed to measure target for step ${safeCurrentStep + 1} after ${maxRetries} retries`
        );
        return;
      }

      // Check if target exists
      const target = currentStep?.targetRef?.current;
      if (DEBUG) console.log(`Attempt ${retryCount + 1}: Target exists: ${!!target}`);

      if (!target || !target.measure) {
        // Target not available yet, retry after delay
        setTimeout(() => performMeasurement(retryCount + 1), retryInterval);
        return;
      }

      try {
        target.measure(
          (x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
            // Validate measurements
            if (width <= 0 || height <= 0 || pageX < 0) {
              // Invalid measurements, retry
              if (DEBUG)
                console.log(
                  `Invalid measurements for step ${safeCurrentStep + 1}: x=${pageX}, y=${pageY}, w=${width}, h=${height}, retrying...`
                );
              setTimeout(() => performMeasurement(retryCount + 1), retryInterval);
              return;
            }

            // Handle negative y values for header components
            let adjustedY = pageY;
            if (pageY < 0 || currentStep.isHeaderComponent) {
              if (DEBUG)
                console.log(
                  `Header component detected for step ${safeCurrentStep + 1}, adjusting y position`
                );
              // For header components, we set a small positive value to ensure visibility
              adjustedY = currentStep.isHeaderComponent ? insets.top : 0;
            }

            if (DEBUG)
              console.log(
                `Measured target for step ${safeCurrentStep + 1}: x=${pageX}, y=${adjustedY} (original y=${pageY}), w=${width}, h=${height}`
              );
            setTargetMeasurements({
              x: pageX,
              y: adjustedY,
              width,
              height,
            });
          }
        );
      } catch (error) {
        console.error('Error measuring target:', error);
        // Try again after a delay
        setTimeout(() => performMeasurement(retryCount + 1), retryInterval);
      }
    };

    // If the step has a prepare function, run it before attempting measurement
    if (currentStep?.prepare) {
      setIsTransitioning(true);
      if (DEBUG) console.log(`Running prepare function for step ${safeCurrentStep + 1}`);

      // Execute the prepare function and wait for it to complete
      currentStep
        .prepare()
        .then(() => {
          // Start measurement immediately after preparation
          performMeasurement();
          setIsTransitioning(false);
        })
        .catch(error => {
          console.error('Error in prepare function:', error);
          setIsTransitioning(false);
          performMeasurement();
        });
    } else {
      // No prepare function, measure directly
      performMeasurement();
    }
  };

  const getTooltipPosition = (): ViewStyle => {
    if (!targetMeasurements) return {};

    const { width: screenWidth, height: screenHeight } = screenDimensions;
    const { x, y, width, height } = targetMeasurements;
    const position = steps[safeCurrentStep].position || 'bottom';
    const padding = steps[safeCurrentStep].padding || 10;

    // Account for safe area insets
    const safeTop = insets.top;
    const safeBottom = insets.bottom;
    const safeLeft = insets.left;
    const safeRight = insets.right;

    // Default tooltip width
    const tooltipWidth = 300;

    let tooltipPos: ViewStyle = {};

    switch (position) {
      case 'top':
        // Special handling for bottom components
        if (steps[safeCurrentStep].isBottomComponent) {
          // For bottom components, position tooltip in the upper portion of screen
          tooltipPos = {
            position: 'absolute',
            left: Math.max(
              safeLeft + 10,
              Math.min(
                screenWidth - tooltipWidth - safeRight - 10,
                x + width / 2 - tooltipWidth / 2
              )
            ),
            top: Math.max(safeTop + 20, screenHeight * 0.6),
            width: tooltipWidth,
          };
        } else {
          // Normal positioning for regular components
          tooltipPos = {
            position: 'absolute',
            left: Math.max(
              safeLeft + 10,
              Math.min(
                screenWidth - tooltipWidth - safeRight - 10,
                x + width / 2 - tooltipWidth / 2
              )
            ),
            top: Math.max(safeTop + 10, y - 120 - padding),
            width: tooltipWidth,
          };
        }
        break;
      case 'bottom':
        tooltipPos = {
          position: 'absolute',
          left: Math.max(
            safeLeft + 10,
            Math.min(screenWidth - tooltipWidth - safeRight - 10, x + width / 2 - tooltipWidth / 2)
          ),
          top: Math.min(screenHeight - 200 - safeBottom, y + height + padding),
          width: tooltipWidth,
        };
        break;
      case 'left':
        tooltipPos = {
          position: 'absolute',
          right: Math.max(safeRight + 10, screenWidth - x + padding),
          top: Math.max(
            safeTop + 10,
            Math.min(screenHeight - 150 - safeBottom, y + height / 2 - 60)
          ),
          width: 200,
        };
        break;
      case 'right':
        tooltipPos = {
          position: 'absolute',
          left: Math.min(screenWidth - 220 - safeRight, x + width + padding),
          top: Math.max(
            safeTop + 10,
            Math.min(screenHeight - 150 - safeBottom, y + height / 2 - 60)
          ),
          width: 200,
        };
        break;
      default:
        tooltipPos = {
          position: 'absolute',
          left: Math.max(
            safeLeft + 10,
            Math.min(screenWidth - tooltipWidth - safeRight - 10, x + width / 2 - tooltipWidth / 2)
          ),
          top: Math.min(screenHeight - 200 - safeBottom, y + height + padding),
          width: tooltipWidth,
        };
    }

    return tooltipPos;
  };

  const isLastStep = safeCurrentStep === steps.length - 1;

  if (!visible || !steps[safeCurrentStep]) return null;

  // Create SVG path for cutout effect
  const renderOverlay = () => {
    if (!targetMeasurements || !isReady) return null;

    const { x, y, width, height } = targetMeasurements;
    const { width: screenWidth, height: screenHeight } = screenDimensions;

    // Add some padding to the cutout
    const padding = 5;
    const cutoutX = x - padding;
    const cutoutY = y - padding;
    const cutoutWidth = width + padding * 2;

    // Ensure a minimum height for the cutout, especially for header components
    const minCutoutHeight = 44; // Minimum reasonable height for UI elements
    const cutoutHeight = Math.max(height + padding * 2, minCutoutHeight);

    return (
      <>
        {/* Top overlay */}
        <View
          style={[
            styles.overlaySection,
            {
              top: 0,
              left: 0,
              width: screenWidth,
              height: cutoutY,
              backgroundColor: overlayColor,
            },
          ]}
        />

        {/* Left overlay */}
        <View
          style={[
            styles.overlaySection,
            {
              top: cutoutY,
              left: 0,
              width: cutoutX,
              height: cutoutHeight,
              backgroundColor: overlayColor,
            },
          ]}
        />

        {/* Right overlay */}
        <View
          style={[
            styles.overlaySection,
            {
              top: cutoutY,
              left: cutoutX + cutoutWidth,
              width: screenWidth - (cutoutX + cutoutWidth),
              height: cutoutHeight,
              backgroundColor: overlayColor,
            },
          ]}
        />

        {/* Bottom overlay */}
        <View
          style={[
            styles.overlaySection,
            {
              top: cutoutY + cutoutHeight,
              left: 0,
              width: screenWidth,
              height: screenHeight - (cutoutY + cutoutHeight),
              backgroundColor: overlayColor,
            },
          ]}
        />

        {/* Highlight border */}
        <View
          style={[
            styles.highlight,
            {
              left: cutoutX,
              top: cutoutY,
              width: cutoutWidth,
              height: cutoutHeight,
              borderColor: highlightBorderColor,
            },
          ]}
        />
      </>
    );
  };

  // Get the tooltip position style
  const tooltipPosition = getTooltipPosition();

  // Handle button presses with debounce to prevent double taps
  const handleNextPress = () => {
    if (isTransitioning) return;
    if (DEBUG) console.log(`Next button pressed at step ${safeCurrentStep + 1}/${steps.length}`);
    onNext();
  };

  const handleFinishPress = () => {
    if (isTransitioning) return;
    if (DEBUG) console.log(`Finish button pressed at step ${safeCurrentStep + 1}/${steps.length}`);
    onFinish();
  };

  return (
    <Animated.View style={[styles.overlay, { opacity: fadeAnim }]}>
      <View style={styles.fullScreen}>
        {targetMeasurements && isReady && (
          <>
            {renderOverlay()}

            {tooltipVisible && (
              <Animated.View
                style={[
                  styles.tooltipContainer,
                  tooltipPosition,
                  {
                    opacity: tooltipFadeAnim,
                    transform: [{ scale: scaleAnim }],
                  },
                ]}>
                <Text style={[styles.title, { color: textColor }, titleStyle]}>
                  {steps[safeCurrentStep].title}
                </Text>
                <Text style={[styles.description, { color: textColor }, descriptionStyle]}>
                  {steps[safeCurrentStep].description}
                </Text>

                <View style={styles.buttonContainer}>
                  <View style={styles.leftButtonContainer}>{/* Previous button removed */}</View>

                  <View style={styles.rightButtonContainer}>
                    <TouchableOpacity
                      onPress={isLastStep ? handleFinishPress : handleNextPress}
                      style={[styles.button, styles.primaryButton]}
                      activeOpacity={0.7}
                      disabled={isTransitioning}>
                      <Text style={[styles.buttonText, styles.primaryButtonText]}>
                        {isLastStep ? t('tutorial.common.finish') : t('tutorial.common.next')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.dotsContainer}>
                  {steps.map((_, index) => (
                    <View
                      key={index}
                      style={[
                        styles.dot,
                        {
                          backgroundColor:
                            index === safeCurrentStep
                              ? highlightBorderColor
                              : 'rgba(255,255,255,0.5)',
                        },
                      ]}
                    />
                  ))}
                </View>
              </Animated.View>
            )}
          </>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  overlaySection: {
    position: 'absolute',
  },
  fullScreen: {
    width: '100%',
    height: '100%',
  },
  highlight: {
    position: 'absolute',
    borderRadius: 8,
    borderWidth: 2,
    backgroundColor: 'transparent',
  },
  tooltipContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    zIndex: 1001,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  leftButtonContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  rightButtonContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    minWidth: 100,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: COLORS.theme,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  primaryButtonText: {
    color: '#FFFFFF',
  },
  dotsContainer: {
    flexDirection: 'row',
    marginTop: 16,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});

export default TutorialLayer;
