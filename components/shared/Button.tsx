import * as Haptics from 'expo-haptics';
import { Button as PaperButton } from 'react-native-paper';

import { Image } from 'react-native';

type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'google' | 'apple';

type ButtonProps = Omit<React.ComponentProps<typeof PaperButton>, 'mode'> & {
  variant?: ButtonVariant;
  haptic?: boolean;
  hapticStyle?: keyof typeof Haptics.ImpactFeedbackStyle;
  disabled?: boolean;
  loading?: boolean;
};

const Button = ({
  variant = 'primary',
  style,
  haptic = true,
  hapticStyle = 'Light',
  onPress,
  disabled,
  loading,
  ...rest
}: ButtonProps) => {
  const getButtonStyles = (variant: ButtonVariant, isDisabled?: boolean) => {
    const baseStyles = {
      mode: 'contained' as const,
      buttonColor: isDisabled ? '#e5e7eb' : '#FF9339', // gray-200 for disabled
      textColor: isDisabled ? '#9ca3af' : undefined, // gray-400 for disabled
      style: {
        borderRadius: 6,
        opacity: isDisabled ? 0.7 : loading ? 0.8 : 1,
        justifyContent: 'center' as const,
        ...(typeof style === 'object' ? style : {}),
      },
    };

    switch (variant) {
      case 'primary':
        return baseStyles;
      case 'secondary':
        return {
          ...baseStyles,
          mode: 'contained-tonal' as const,
          buttonColor: isDisabled ? '#e5e7eb' : '#6B7280', // Using gray-500 for secondary
          textColor: isDisabled ? '#9ca3af' : '#ffffff', // White text for better contrast
        };
      case 'danger':
        return {
          ...baseStyles,
          buttonColor: isDisabled ? '#e5e7eb' : 'rgb(239 68 68)', // red-500
        };
      case 'google':
        return {
          ...baseStyles,
          mode: 'outlined' as const,
          buttonColor: isDisabled ? '#e5e7eb' : 'white',
          textColor: isDisabled ? '#9ca3af' : '#3c4043',
          style: {
            ...baseStyles.style,
            backgroundColor: isDisabled ? '#e5e7eb' : 'white',
            borderWidth: 1,
            elevation: 0,
          },
          contentStyle: {
            paddingVertical: 5,
          },
          icon: ({ size }: { size: number }) => (
            <Image
              source={require('@/assets/logo/google.png')}
              style={{
                width: size + 8,
                height: size + 8,
              }}
              resizeMode="contain"
            />
          ),
        };
      case 'apple':
        return {
          ...baseStyles,
          mode: 'outlined' as const,
          buttonColor: isDisabled ? '#e5e7eb' : 'black',
          textColor: isDisabled ? '#9ca3af' : 'white',
          style: {
            ...baseStyles.style,
            backgroundColor: isDisabled ? '#e5e7eb' : 'black',
            borderWidth: 0,
            elevation: 0,
          },
          contentStyle: {
            paddingVertical: 5,
          },
          icon: ({ size }: { size: number }) => (
            <Image
              source={require('@/assets/logo/apple-black.png')}
              style={{
                width: size + 24,
                height: size + 24,
                marginTop: 2,
              }}
              resizeMode="contain"
            />
          ),
        };
    }
  };

  const handlePress = async (e: any) => {
    if (haptic) {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle[hapticStyle]);
    }
    onPress?.(e);
  };

  const isDisabled = disabled || loading;

  return (
    <PaperButton
      {...getButtonStyles(variant, isDisabled)}
      onPress={handlePress}
      disabled={isDisabled}
      loading={loading}
      {...rest}
    />
  );
};

export default Button;
