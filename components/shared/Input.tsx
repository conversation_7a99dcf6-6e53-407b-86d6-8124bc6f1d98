import { TextInput, HelperText } from 'react-native-paper';
import { IconSource } from 'react-native-paper/lib/typescript/components/Icon';

import React from 'react';
import { forwardRef, useState } from 'react';

import { StyleSheet, TextInput as RNTextInput, View, Text } from 'react-native';

type InputProps = React.ComponentProps<typeof TextInput> & {
  error?: string;
  icon?: IconSource;
  isPassword?: boolean;
  description?: string;
};

const Input = forwardRef<RNTextInput, InputProps>((props, ref) => {
  const {
    error,
    style,
    activeOutlineColor = '#f97316',
    icon,
    isPassword,
    description,
    ...rest
  } = props;
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    props.onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    props.onBlur?.(e);
  };

  const iconColor = error
    ? '#ef4444' // red-500 for error
    : isFocused
      ? activeOutlineColor
      : '#999da5'; // gray-500 for inactive

  return (
    <View>
      <TextInput
        ref={ref}
        mode="outlined"
        style={[styles.input, style]}
        error={!!error}
        activeOutlineColor={activeOutlineColor}
        onFocus={handleFocus}
        onBlur={handleBlur}
        secureTextEntry={isPassword ? !showPassword : rest.secureTextEntry}
        {...rest}
        left={
          icon && !props.left ? (
            <TextInput.Icon icon={icon} color={iconColor} size={20} />
          ) : (
            props.left
          )
        }
        right={
          isPassword ? (
            <TextInput.Icon
              icon={showPassword ? 'eye-off' : 'eye'}
              color={iconColor}
              size={20}
              onPress={() => setShowPassword(!showPassword)}
            />
          ) : (
            props.right
          )
        }
      />
      {description && <Text className="mb-2 mt-1 text-xs text-gray-500">{description}</Text>}
      {error && (
        <HelperText type="error" visible={!!error}>
          {error}
        </HelperText>
      )}
    </View>
  );
});

Input.displayName = 'Input';

const styles = StyleSheet.create({
  input: {
    marginBottom: 0,
    backgroundColor: 'white',
  },
});

export default Input;
