import { LANGUAGES } from '@/i18n';
import { useI18n } from '@/i18n/provider';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import i18next from 'i18next';
import { useTranslation } from 'react-i18next';
import { Dropdown } from 'react-native-element-dropdown';

import { useState } from 'react';

import { StyleSheet, View } from 'react-native';

const LOCALE_KEY = 'finsmarket_locale';

type LanguageOption = {
  label: string;
  value: string;
};

const languageOptions: LanguageOption[] = [
  { label: 'English', value: LANGUAGES.en },
  { label: '繁體中文', value: LANGUAGES['zh-TW'] },
  { label: '日本語', value: LANGUAGES.ja },
  { label: '한국어', value: LANGUAGES.ko },
  { label: 'ไทย', value: LANGUAGES.th },
  { label: 'Vietnamese', value: LANGUAGES.vi },
  { label: 'Malay', value: LANGUAGES.ms },
  { label: 'Portuguese', value: LANGUAGES.pt },
];

export default function LanguagePicker() {
  const { t } = useTranslation();
  const { setLocale, locale } = useI18n();
  const [isFocus, setIsFocus] = useState(false);

  const handleLanguageChange = async (selected: LanguageOption) => {
    if (selected) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      const newLocale = selected.value as keyof typeof LANGUAGES;
      // Update AsyncStorage
      await AsyncStorage.setItem(LOCALE_KEY, newLocale);
      // Update i18next
      await i18next.changeLanguage(newLocale);
      // Update context
      await setLocale(newLocale);
    }
  };

  return (
    <View style={styles.container}>
      <Dropdown
        style={[styles.dropdown, isFocus && styles.dropdownFocused]}
        containerStyle={styles.dropdownContainer}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        selectedTextProps={{
          numberOfLines: 1,
          ellipsizeMode: 'tail',
        }}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        data={languageOptions}
        maxHeight={300}
        labelField="label"
        valueField="value"
        placeholder={t('common.selectLanguage')}
        searchPlaceholder={t('common.search')}
        value={locale}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={handleLanguageChange}
        activeColor="rgb(249, 249, 249)"
        renderLeftIcon={() => (
          <MaterialIcons
            style={styles.icon}
            color={isFocus ? '#FF9339' : '#4b5563'}
            name="language"
            size={20}
          />
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    maxWidth: 150,
    alignSelf: 'flex-start',
  },
  dropdown: {
    height: 36,
    paddingHorizontal: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    width: 150,
  },
  dropdownFocused: {
    borderWidth: 0,
    borderBottomWidth: 1,
    borderColor: '#FF9339',
  },
  icon: {
    marginRight: 8,
  },
  placeholderStyle: {
    fontSize: 14,
    color: '#4b5563',
    flex: 1,
  },
  selectedTextStyle: {
    fontSize: 14,
    color: '#111827',
    marginRight: 8,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 14,
    borderRadius: 6,
  },
});
