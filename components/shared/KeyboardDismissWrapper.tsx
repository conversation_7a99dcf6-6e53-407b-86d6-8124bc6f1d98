import React from 'react';

import {
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  View,
  type ViewStyle,
} from 'react-native';

type Props = {
  children: React.ReactNode;
  style?: ViewStyle;
  keyboardVerticalOffset?: number;
};

export const KeyboardDismissWrapper = ({ children, style, keyboardVerticalOffset = 0 }: Props) => {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={keyboardVerticalOffset}
      style={[{ flex: 1 }, style]}>
      <View style={{ flex: 1 }} pointerEvents="box-none">
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          {children}
        </TouchableWithoutFeedback>
      </View>
    </KeyboardAvoidingView>
  );
};

// Reference: https://reactnative.dev/docs/keyboardavoidingview
