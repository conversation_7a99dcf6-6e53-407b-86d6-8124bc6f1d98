import FontAwesome from '@expo/vector-icons/FontAwesome';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import Modal from 'react-native-modal';

import React from 'react';

import { ScrollView, Text, TouchableOpacity, View } from 'react-native';

type AgreementModalProps = {
  isVisible: boolean;
  onClose: () => void;
  onAgree: () => void;
  onRefuse: () => void;
};

export default function AgreementModal({
  isVisible,
  onClose,
  onAgree,
  onRefuse,
}: AgreementModalProps) {
  return (
    <Modal
      isVisible={isVisible}
      backdropTransitionOutTiming={0}
      hideModalContentWhileAnimating={true}
      useNativeDriverForBackdrop={true}
      animationIn="fadeIn"
      animationOut="fadeOut"
      onBackdropPress={onClose}
      style={{ margin: 0 }}>
      <View className="mx-4 mb-8 mt-12 flex-1 overflow-hidden rounded-2xl bg-white">
        {/* 标题栏 */}
        <View className="border-b border-gray-200 p-4">
          <View className="flex-row items-center justify-between">
            <Text className="text-xl font-bold text-gray-800">User Agreement</Text>
            <TouchableOpacity onPress={onClose}>
              <FontAwesome name="times" size={24} color="gray" />
            </TouchableOpacity>
          </View>
        </View>

        {/* 内容区域 */}
        <ScrollView className="flex-1 p-4">
          {/* 前言部分 */}
          <View className="mb-6">
            <Text className="mb-4 text-base text-gray-600">
              Before using Stocklens App, please carefully read and understand the following terms
              and conditions. By confirming, you agree to proceed with using our services.
            </Text>
          </View>

          {/* 主要条款 */}
          <View className="mb-6">
            <View className="mb-4">
              <Text className="mb-2 text-lg font-bold text-gray-800">1. Service Content</Text>
              <Text className="text-gray-600">
                This agreement establishes the terms between users and Zentanet Limited regarding
                the use of Stocklens platform services. We provide comprehensive insights into US
                stock market data and analysis.
              </Text>
            </View>

            <View className="mb-4">
              <Text className="mb-2 text-lg font-bold text-gray-800">2. User Rights</Text>
              <Text className="text-gray-600">
                • Access to real-time market data{'\n'}• Personalized stock analysis{'\n'}•
                Portfolio management tools{'\n'}• Community features
              </Text>
            </View>

            <View className="mb-4">
              <Text className="mb-2 text-lg font-bold text-gray-800">3. Privacy Protection</Text>
              <Text className="text-gray-600">
                We prioritize the security of your personal information and implement strict data
                protection measures. Your data will only be used as specified in our privacy policy.
              </Text>
            </View>
          </View>

          {/* 免责声明 */}
          <View className="mb-6 rounded-lg bg-gray-50 p-4">
            <Text className="text-sm font-semibold text-gray-700">Disclaimer</Text>
            <Text className="mt-2 text-sm text-gray-600">
              The information provided is for reference only and does not constitute investment
              advice. Users should make investment decisions based on their own judgment.
            </Text>
          </View>
        </ScrollView>

        {/* 底部操作区 */}
        <View className="border-t border-gray-200 p-4">
          <View className="mb-4">
            <BouncyCheckbox
              size={20}
              fillColor="#FF9339"
              unFillColor="#FFFFFF"
              text="I have read and agree to the terms"
              textStyle={{ textDecorationLine: 'none', color: '#666' }}
              iconStyle={{ borderColor: '#FF9339' }}
              onPress={(isChecked: boolean) => {
                console.log(isChecked);
              }}
            />
          </View>
          <View className="flex-row justify-between">
            <TouchableOpacity
              onPress={onRefuse}
              className="w-[48%] rounded-lg border border-theme bg-white p-3">
              <Text className="text-center text-theme">Refuse</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={onAgree} className="w-[48%] rounded-lg bg-theme p-3">
              <Text className="text-center text-white">Agree</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
