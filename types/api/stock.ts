// export enum LanguageOptions {
//   en = 'en',
//   zh_tw = 'zh-TW',
//   ja = 'ja',
//   ko = 'ko',
//   th = 'th',
//   vi = 'vi',
//   ms = 'ms',
//   pt = 'pt',
// }

export type AnalysisStockTrendResponse = {
  mission_id: string;
  response: {
    symbol: string;
    period: string;
    interval: string;
    volume: string;
    overview: string;
    technical: {
      RSI: string;
      'Williams %R': string;
      ADX: string;
      'Standard Deviation': string;
    };
    trend: {
      short_term: string;
      long_term: string;
    };
    recommend: {
      buy: string;
      sell: string;
      key: string;
    };
    summary: string;
  };
};

export type AnalysisStockAndIndustryComparisonResponse = {
  mission_id: string;
  response: {
    stock_comparision: {
      symbol: string;
      technical: {
        'Standard Deviation': string;
        RSI: string;
        'Williams %R': string;
        ADX: string;
      };
      sector_comparison: {
        overview: string;
        grading: string;
        purple_update: number;
        blue_update: number;
        difference: string;
        score: number;
      };
      summary: string;
    };
    industry_comparision: {
      industry: string;
      technical: {
        'Standard Deviation': string;
        RSI: string;
        'Williams %R': string;
        ADX: string;
      };
      market_comparison: {
        overview: string;
        grading: string;
        purple_update: number;
        blue_update: number;
        difference: string;
        score: number;
      };
      forecast: string;
    };
  };
};

export type ResearcherFinancialStatementResponse = {
  mission_id: string;
  response: {
    symbol: string;
    period: string;
    analysis: string[];
    summary: string;
    opinion: string;
  };
};

export type ResearcherTargetGradingResponse = {
  mission_id: string;
  response: {
    symbol: string;
    analysis: string[];
    summary: string;
    opinion: {
      target_price: number | string;
      target_price_reason: string;
      grading: string;
      grading_reason: string;
    };
  };
};

export type ResearcherStockNewsResponse = {
  mission_id: string;
  response: {
    symbol: string;
    overview: string;
    key_news_6: {
      title: string;
      text: string;
      url: string;
    }[];
    summary: string;
  };
};

export type CompanySearchResponse = {
  symbol: string;
  name: string;
  currency: string;
  stockExchange: string;
  exchangeShortName: string;
}[];

export type UserViewStockUsageResponse = {
  success: boolean;
  data: {
    limit: number;
    usage: number;
    stocks: string[];
    resetDateTime: string;
  };
};

export type LatestClosingPricesResponse = {
  success: boolean;
  data: {
    symbol: string;
    price: number;
    date: string;
  }[];
};
