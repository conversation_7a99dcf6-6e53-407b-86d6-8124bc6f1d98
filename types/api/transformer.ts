import { ReportManageUserResponse } from './report';

export type ReportManageReponseDTO = {
  success: boolean;
  data: {
    reportList: ReportStockListDTO[];
    user: ReportManageUserResponse & {
      currentSymbolList: string[];
      availableUsages: string;
    };
  };
};

export type ReportManageUserReponseDTO = ReportManageUserResponse & {
  currentSymbolList: string[];
};

export type ReportStockListDTO = {
  date: string;
  stockList: StockList[];
};

export type StockList = {
  symbol: string;
  companyName: string;
  logo: string;
  exchangeShortName: string;
  reportUrl: string;
};
