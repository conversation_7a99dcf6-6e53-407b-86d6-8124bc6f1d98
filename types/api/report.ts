export type ReportSubscribeResponse = {
  mission_id: string;
  response: {
    action: string;
    firstName: string;
    lastName: string;
    email: string;
    language_code: string;
    symbol_list: string[];
  };
};

export type ReportManageResponse = {
  mission_id: string;
  response: {
    message: string;
    status: 'SUCCESS' | 'FAIL';
    report_link_list: ReportLinkList;
    symbol_list: string[];
    user_data: ReportManageUserResponse;
    available_usages: string; // times left || date
  };
};

export type ReportActiveResponse = {
  mission_id: string;
  response: {
    status: 'SUCCESS' | 'FAIL';
    message: string;
    available_usages: string;
  };
};

export type ReportLinkList = {
  [data: string]: {
    [stock: string]: string;
  };
};

export type ReportManageUserResponse = {
  first_name: string;
  last_name: string;
  email: string;
  language_code: string;
};
