// AI Chat API Types

// Enums
export enum ChatMessageRole {
  USER = 'USER',
  AI = 'AI',
}

export enum ChatFeedback {
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  RESET = 'RESET',
}

// Base types
export type ChatMessage = {
  id: string;
  role: ChatMessageRole;
  content: string;
  createdAt: string;
  feedback?: ChatFeedback | null;
  parentId?: string | null;
};

export type Chat = {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  symbol?: string;
};

export type ChatWithMessages = Chat & {
  messages: ChatMessage[];
};

// API Request types
export type SendChatMessageRequest = {
  chatId?: string;
  message?: string;
  retryMessageId?: string;
};

export type SubmitFeedbackRequest = {
  messageId: string;
  feedback: ChatFeedback;
};

// API Response types
export type ChatUsageResponse = {
  success: boolean;
  data: {
    limit: number;
    usage: number;
    resetDateTime: string;
  };
};

export type ChatHistoryResponse = {
  success: boolean;
  data: {
    chats: Chat[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
};

export type ChatDetailsResponse = {
  success: boolean;
  data: ChatWithMessages;
};

export type SubmitFeedbackResponse = {
  success: boolean;
  message?: string;
};

// Streaming response types for POST /api/chat
export type ChatStreamMetadata = {
  type: 'metadata';
  chatId: string;
  messageId: string;
  aiMessageId: string;
  timestamp: string;
};

export type ChatStreamMessage = {
  type: 'message';
  content: string;
};

export type ChatStreamResponse = ChatStreamMetadata | ChatStreamMessage;

// Chat history query parameters
export type ChatHistoryParams = {
  limit?: number;
  offset?: number;
};
